<?xml version="1.0" encoding="UTF-8"?>
<configuration debug="false" scan="true" scanPeriod="1 seconds">
    <springProperty scope="context" name="logPath" source="log.path"/>
    <springProperty scope="context" name="isDebug" source="log.debug"/>
    <!-- 应用名称：和统一配置中的项目代码保持一致（小写） -->
    <property name="APP_NAME" value="rpp"/>
    <contextName>${APP_NAME}</contextName>
    <!--日志文件保留天数 -->
    <property name="LOG_MAX_HISTORY" value="7"/>
    <!--定义日志文件的存储地址 勿在 LogBack 的配置中使用相对路径 -->

    <!--应用日志文件保存路径 -->
    <!--在没有定义${LOG_HOME}系统变量的时候，可以设置此本地变量。提交测试、上线时，要将其注释掉，使用系统变量。 -->
    <property name="LOG_HOME" value="${logPath}/${APP_NAME}"/>
    <!--=========================== 按照每天生成日志文件：默认配置=================================== -->
    <!-- 控制台输出 -->
    <appender name="console" class="ch.qos.logback.core.ConsoleAppender">
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <!--格式化输出：%d表示日期，%c类名，%t表示线程名，%L行， %p日志级别 %msg：日志消息，%n是换行符  -->
            <pattern>%black(%contextName - %d{yyyy-MM-dd HH:mm:ss}) %green([%c][%t][%L]) %highlight(%-5level) - %gray(%msg%n)</pattern>
        </encoder>
    </appender>

    <!-- 按照每天生成日志文件：主项目日志 -->
    <appender name="APP_DEBUG" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <!--日志文件输出的文件名 -->
            <FileNamePattern>${LOG_HOME}/debug-%d{yyyy-MM-dd}.log</FileNamePattern>
            <!--日志文件保留天数 -->
            <MaxHistory>${LOG_MAX_HISTORY}</MaxHistory>
        </rollingPolicy>
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <!--格式化输出：%d表示日期，%c类名，%t表示线程名，%L行， %p日志级别 %msg：日志消息，%n是换行符  -->
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%c][%t][%L][%p] - %msg%n</pattern>
            <charset>UTF-8</charset>
        </encoder>
        <!-- 此日志文件只记录debug级别的 -->
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>debug</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY</onMismatch>
        </filter>
    </appender>

    <!-- 按照每天生成日志文件：主项目日志 -->
    <appender name="APP_INFO" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <!--日志文件输出的文件名 -->
            <FileNamePattern>${LOG_HOME}/info-%d{yyyy-MM-dd}.log</FileNamePattern>
            <!--日志文件保留天数 -->
            <MaxHistory>${LOG_MAX_HISTORY}</MaxHistory>
        </rollingPolicy>
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <!--格式化输出：%d表示日期，%c类名，%t表示线程名，%L行， %p日志级别 %msg：日志消息，%n是换行符  -->
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%c][%t][%L][%p] - %msg%n</pattern>
            <charset>UTF-8</charset>
        </encoder>

        <!-- 此日志文件只记录info级别的 -->
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>info</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY</onMismatch>
        </filter>
    </appender>

    <!-- 按照每天生成日志文件：主项目日志 -->
    <appender name="APP_ERROR" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <!--日志文件输出的文件名 -->
            <FileNamePattern>${LOG_HOME}/error-%d{yyyy-MM-dd}.log</FileNamePattern>
            <!--日志文件保留天数 -->
            <MaxHistory>${LOG_MAX_HISTORY}</MaxHistory>
        </rollingPolicy>
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <!--格式化输出：%d表示日期，%c类名，%t表示线程名，%L行， %p日志级别 %msg：日志消息，%n是换行符  -->
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%c][%t][%L][%p] - %msg%n</pattern>
            <charset>UTF-8</charset>
        </encoder>
        <!-- 此日志文件只记录error级别的 -->
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>error</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY</onMismatch>
        </filter>
    </appender>

    <!-- 按照每天生成日志文件：sql info日志 sql日志太多单独开一个-->
    <appender name="SQL_INFO" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <!--日志文件输出的文件名 -->
            <FileNamePattern>${LOG_HOME}/sql-info-%d{yyyy-MM-dd}.log</FileNamePattern>
            <!--日志文件保留天数 -->
            <MaxHistory>${LOG_MAX_HISTORY}</MaxHistory>
        </rollingPolicy>
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <!--格式化输出：%d表示日期，%c类名，%t表示线程名，%L行， %p日志级别 %msg：日志消息，%n是换行符  -->
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%c][%t][%L][%p] - %msg%n</pattern>
            <charset>UTF-8</charset>
        </encoder>
        <!-- 此日志文件只记录sql info级别的 -->
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>info</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY</onMismatch>
        </filter>
    </appender>

    <!--日志输出到文件-->
    <root level="info">
        <appender-ref ref="APP_DEBUG"/>
        <appender-ref ref="APP_INFO"/>
        <appender-ref ref="APP_ERROR"/>
        <appender-ref ref="SQL_INFO"/>
        <appender-ref ref="console"/>
    </root>

    <!-- 日志级别 -->
    <logger name="com.example.logback" level="INFO"/>

    <!--==============================监控sql日志输出================================ -->
    <!--记录系统执行过的sql语句-->
    <logger name="jdbc.sqlonly" level="INFO" additivity="false">
        <appender-ref ref="SQL_INFO"/>
    </logger>

    <!-- 记录返回结果集信息-->
    <logger name="jdbc.resultset" level="ERROR" additivity="false">
        <appender-ref ref="console" />
        <appender-ref ref="APP_ERROR"/>
    </logger>

    <!--记录sql执行的时间，可以分析耗时的sql语句-->
    <logger name="jdbc.resultsettable" level="INFO" additivity="false">
        <appender-ref ref="SQL_INFO"/>
    </logger>

    <!--记录数据库连接和释放信息，可记录当前的数据库连接数，便于诊断连接是否释放-->
    <logger name="jdbc.connection" level="OFF" additivity="false"> </logger>

    <!--记录sql执行的时间，可以分析耗时的sql语句-->
    <logger name="jdbc.sqltiming" level="OFF" additivity="false"> </logger>

    <!--记录除了ResultSet外的所有JDBC调用情况。一般不需要-->
    <logger name="jdbc.audit" level="OFF" additivity="false"> </logger>

</configuration>

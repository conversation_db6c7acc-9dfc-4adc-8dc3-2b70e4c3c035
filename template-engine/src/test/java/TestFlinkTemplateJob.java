import org.apache.flink.streaming.util.TestStreamEnvironment;
import org.apache.flink.table.api.Table;
import org.apache.flink.table.api.bridge.java.StreamTableEnvironment;
import org.apache.flink.table.api.internal.TableEnvironmentImpl;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.flink.table.api.bridge.java.internal.StreamTableEnvironmentImpl;
import org.apache.flink.table.api.EnvironmentSettings;
import org.apache.flink.table.api.*;
import org.apache.flink.table.expressions.Expression;
import org.apache.flink.table.operations.ModifyOperation;
import org.apache.flink.table.operations.Operation;
import org.apache.flink.table.catalog.ObjectIdentifier;
import org.apache.flink.table.catalog.UnresolvedIdentifier;
import org.apache.flink.table.operations.CatalogSinkModifyOperation;
import org.apache.flink.table.catalog.Column;
import org.apache.flink.shaded.netty4.io.netty.util.internal.StringUtil;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.runtime.minicluster.MiniCluster;
import org.apache.flink.runtime.minicluster.MiniClusterConfiguration;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.util.*;
import java.util.HashMap;
import java.util.Map;
import java.util.Collections;
import com.google.common.base.Joiner;
import org.apache.flink.types.Row;
import org.apache.flink.util.StringUtils;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import org.apache.flink.api.java.typeutils.RowTypeInfo;
import org.apache.flink.table.api.DataTypes;
import org.apache.flink.table.types.DataType;
import org.apache.flink.table.types.FieldsDataType;
import org.apache.flink.table.types.logical.RowType;
import com.google.common.collect.Lists;
import org.apache.flink.streaming.api.environment.CustomTableEnvironment;
import org.apache.flink.table.api.internal.CustomTableEnvironmentImpl;
import org.apache.flink.table.api.EnvironmentSettings;
import org.apache.flink.table.api.ExplainDetail;
import org.apache.flink.table.api.Table;
import org.apache.flink.table.api.bridge.java.StreamTableEnvironment;
import org.apache.flink.table.api.bridge.java.internal.StreamTableEnvironmentImpl;
import org.apache.flink.table.api.internal.CustomTableEnvironmentImpl;
import org.apache.flink.table.api.internal.TableEnvironmentImpl;
import org.apache.flink.table.catalog.Column;
import org.apache.flink.table.functions.FunctionService;
import org.apache.flink.table.functions.UserDefinedFunction;
import org.apache.flink.table.operations.Operation;
import org.yaml.snakeyaml.Yaml;
import java.io.InputStream;

import java.io.IOException;
import java.net.URL;
import static org.apache.flink.table.api.Expressions.*;
import static org.apache.flink.table.api.Expressions.$;


public class TestFlinkTemplateJob {

	private Map<String, Table> registeredTable = new HashMap();
	private StreamExecutionEnvironment env ;
	private String jobName = "";
	private String sessionId = "";
	private String dbUrl = "";
	private String dbUser = "";
	private String dbPassword = "";
	private final ClassLoader systemClassLoader = ClassLoader.getSystemClassLoader();
	private static final MiniCluster miniCluster;
	static {
		miniCluster = new MiniCluster(new MiniClusterConfiguration.Builder()
				.setConfiguration(new Configuration())
				.setNumTaskManagers(
						1)
				.setNumSlotsPerTaskManager(
						1).build());
		try {
			miniCluster.start();
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	public void initFunctions(TableEnvironmentImpl tEnv) throws ClassNotFoundException {
		InputStream f = Thread.currentThread().getContextClassLoader().getResourceAsStream("sql-client-defaults.yaml");
		Yaml yaml = new Yaml();
		Map properties = (Map) yaml.load(f);
		System.out.println("properties的值为：" + properties);
		List functionList = (List) properties.get("functions");
		for (Object func : functionList) {
			Map funcMeta = (Map) func;
			String funcName = (String) funcMeta.get("name");
			String className = (String) funcMeta.get("class");
			tEnv.createFunction(funcName, (Class<? extends UserDefinedFunction>) Class.forName(className), true);
			System.out.println("register function " + funcName + " success.");
		}
	}

	public static void main(String[] args) throws Exception{
		new TestFlinkTemplateJob().execute();
	}
	public void execute() throws Exception{

		env = new TestStreamEnvironment(miniCluster, 1);

		EnvironmentSettings settings = EnvironmentSettings.newInstance().inStreamingMode().useBlinkPlanner().build();
		TableEnvironmentImpl tEnv = (TableEnvironmentImpl)StreamTableEnvironment.create(env, settings);
		tEnv.executeSql("CREATE FUNCTION SetConvertSingle AS 'com.alibaba.blink.udx.SetConvertSingle'");
		tEnv.executeSql("CREATE FUNCTION JsonValue AS 'com.alibaba.blink.udx.udf.JsonValue'");

		tEnv.executeSql("CREATE FUNCTION MapKeyFetcher AS 'com.alibaba.blink.udx.udf.MapKeyFetcher'");tEnv.executeSql("CREATE FUNCTION dynamicRule AS 'com.alibaba.blink.udx.udtf.DynamicRulesFunction'");tEnv.executeSql("CREATE FUNCTION touFangDataAnalysis AS 'com.alibaba.blink.udx.udf.TouFangDataAnalysis'");tEnv.executeSql("CREATE FUNCTION binlogParser AS 'com.alibaba.blink.udx.udtf.BinlogParserFunction'");tEnv.executeSql("CREATE FUNCTION msgpackFetch AS 'com.poizon.udf.msgpack.MsgPackParserUdf'");tEnv.executeSql("CREATE FUNCTION JsonPath AS 'com.flink.udf.json.UDFJson'");tEnv.executeSql("CREATE FUNCTION JsonBuild AS 'com.alibaba.blink.udx.udf.JsonStringUdfV2'");tEnv.executeSql("CREATE FUNCTION MergeJson2JsonArray AS 'com.alibaba.blink.udx.udaf.JsonMergeArray'");tEnv.executeSql("CREATE FUNCTION MergeStr2ArrayWithKey AS 'com.alibaba.blink.udx.udaf.MergeStringToArrayWithKey'");tEnv.executeSql("CREATE FUNCTION MergeStr2Array AS 'com.alibaba.blink.udx.udaf.MergeStringToArray'");
		initFunctions(tEnv);

		Configuration configuration =  tEnv.getConfig().getConfiguration();
		// default configs
		configuration.setString("table.exec.mini-batch.allow-latency","10000");
		configuration.setString("table.exec.mini-batch.enabled","true");
		configuration.setString("table.exec.mini-batch.size","1000000");

		configuration.setString("job_name","ads_crowd_label_metric_about_user_aucition_access_2_ou_063d6a14");
		configuration.setString("dynamic.rules.job.id","");
		configuration.setString("execute.job.jdbc.url","#url");
		configuration.setString("execute.job.jdbc.username","#username");
		configuration.setString("execute.job.jdbc.password","#password");
		env.getConfig().setGlobalJobParameters(configuration);
		//user defined configs
		configuration.setString("relative.start.time","minute-5");
		configuration.setString("execute.job.jdbc.password","ZS5AoXs7lduYbnXN");
		configuration.setString("execute.job.jdbc.url","*****************************************************************************************************************************************************************************************");
		configuration.setString("scan.startup.mode","timestamp");
		configuration.setString("table.exec.state.ttl","86400000");
		configuration.setString("execute.job.jdbc.username","dw_realtime_property_platform");
		configuration.setString("scan.startup.timestamp-millis","#startTimeStr");

		globalSink_10c4bca08a7044cdb003a46ba3ac5b49(tEnv);

		tEnv.execute("ads_crowd_label_metric_about_user_aucition_access_2_ou_063d6a14");

	}

	public void explain() throws Exception{
		StreamExecutionEnvironment env = StreamExecutionEnvironment.getExecutionEnvironment();
		EnvironmentSettings settings = EnvironmentSettings.newInstance().inStreamingMode().useBlinkPlanner().build();
		TableEnvironmentImpl tEnv = (TableEnvironmentImpl)StreamTableEnvironment.create(env, settings);
		tEnv.executeSql("CREATE FUNCTION SetConvertSingle AS 'com.alibaba.blink.udx.SetConvertSingle'");
		tEnv.executeSql("CREATE FUNCTION JsonValue AS 'com.alibaba.blink.udx.udf.JsonValue'");
		List<Operation> operations = new ArrayList();
		tEnv.executeSql("CREATE FUNCTION MapKeyFetcher AS 'com.alibaba.blink.udx.udf.MapKeyFetcher'");tEnv.executeSql("CREATE FUNCTION dynamicRule AS 'com.alibaba.blink.udx.udtf.DynamicRulesFunction'");tEnv.executeSql("CREATE FUNCTION touFangDataAnalysis AS 'com.alibaba.blink.udx.udf.TouFangDataAnalysis'");tEnv.executeSql("CREATE FUNCTION binlogParser AS 'com.alibaba.blink.udx.udtf.BinlogParserFunction'");tEnv.executeSql("CREATE FUNCTION msgpackFetch AS 'com.poizon.udf.msgpack.MsgPackParserUdf'");tEnv.executeSql("CREATE FUNCTION JsonPath AS 'com.flink.udf.json.UDFJson'");tEnv.executeSql("CREATE FUNCTION JsonBuild AS 'com.alibaba.blink.udx.udf.JsonStringUdfV2'");tEnv.executeSql("CREATE FUNCTION MergeJson2JsonArray AS 'com.alibaba.blink.udx.udaf.JsonMergeArray'");tEnv.executeSql("CREATE FUNCTION MergeStr2ArrayWithKey AS 'com.alibaba.blink.udx.udaf.MergeStringToArrayWithKey'");tEnv.executeSql("CREATE FUNCTION MergeStr2Array AS 'com.alibaba.blink.udx.udaf.MergeStringToArray'");
		initFunctions(tEnv);

		Configuration configuration =  tEnv.getConfig().getConfiguration();
		configuration.setString("relative.start.time","minute-5");
		configuration.setString("execute.job.jdbc.password","ZS5AoXs7lduYbnXN");
		configuration.setString("execute.job.jdbc.url","*****************************************************************************************************************************************************************************************");
		configuration.setString("scan.startup.mode","timestamp");
		configuration.setString("table.exec.state.ttl","86400000");
		configuration.setString("execute.job.jdbc.username","dw_realtime_property_platform");
		configuration.setString("scan.startup.timestamp-millis","#startTimeStr");

		globalSink_10c4bca08a7044cdb003a46ba3ac5b49(tEnv);
		System.out.println(tEnv.explainInternal(operations,ExplainDetail.JSON_EXECUTION_PLAN));
	}

	public void check() throws Exception{
		StreamExecutionEnvironment env = StreamExecutionEnvironment.getExecutionEnvironment();
		EnvironmentSettings settings = EnvironmentSettings.newInstance().inStreamingMode().useBlinkPlanner().build();
		CustomTableEnvironmentImpl tEnv = (CustomTableEnvironmentImpl) CustomTableEnvironment.create( settings);
		tEnv.executeSql("CREATE FUNCTION SetConvertSingle AS 'com.alibaba.blink.udx.SetConvertSingle'");
		tEnv.executeSql("CREATE FUNCTION JsonValue AS 'com.alibaba.blink.udx.udf.JsonValue'");

		tEnv.executeSql("CREATE FUNCTION MapKeyFetcher AS 'com.alibaba.blink.udx.udf.MapKeyFetcher'");tEnv.executeSql("CREATE FUNCTION dynamicRule AS 'com.alibaba.blink.udx.udtf.DynamicRulesFunction'");tEnv.executeSql("CREATE FUNCTION touFangDataAnalysis AS 'com.alibaba.blink.udx.udf.TouFangDataAnalysis'");tEnv.executeSql("CREATE FUNCTION binlogParser AS 'com.alibaba.blink.udx.udtf.BinlogParserFunction'");tEnv.executeSql("CREATE FUNCTION msgpackFetch AS 'com.poizon.udf.msgpack.MsgPackParserUdf'");tEnv.executeSql("CREATE FUNCTION JsonPath AS 'com.flink.udf.json.UDFJson'");tEnv.executeSql("CREATE FUNCTION JsonBuild AS 'com.alibaba.blink.udx.udf.JsonStringUdfV2'");tEnv.executeSql("CREATE FUNCTION MergeJson2JsonArray AS 'com.alibaba.blink.udx.udaf.JsonMergeArray'");tEnv.executeSql("CREATE FUNCTION MergeStr2ArrayWithKey AS 'com.alibaba.blink.udx.udaf.MergeStringToArrayWithKey'");tEnv.executeSql("CREATE FUNCTION MergeStr2Array AS 'com.alibaba.blink.udx.udaf.MergeStringToArray'");
		initFunctions(tEnv);

		Configuration configuration =  tEnv.getConfig().getConfiguration();
		// default configs
		configuration.setString("table.exec.mini-batch.allow-latency","10000");
		configuration.setString("table.exec.mini-batch.enabled","true");
		configuration.setString("table.exec.mini-batch.size","1000000");
		configuration.setString("table.exec.validate.real-connect","true");
		configuration.setString("job_name","ads_crowd_label_metric_about_user_aucition_access_2_ou_063d6a14");
		configuration.setString("job_id","");
		configuration.setString("execute.job.jdbc.url","#url");
		configuration.setString("execute.job.jdbc.username","#username");
		configuration.setString("execute.job.jdbc.password","#password");
		env.getConfig().setGlobalJobParameters(configuration);
		//user defined configs
		configuration.setString("relative.start.time","minute-5");
		configuration.setString("execute.job.jdbc.password","ZS5AoXs7lduYbnXN");
		configuration.setString("execute.job.jdbc.url","*****************************************************************************************************************************************************************************************");
		configuration.setString("scan.startup.mode","timestamp");
		configuration.setString("table.exec.state.ttl","86400000");
		configuration.setString("execute.job.jdbc.username","dw_realtime_property_platform");
		configuration.setString("scan.startup.timestamp-millis","#startTimeStr");

		globalSink_10c4bca08a7044cdb003a46ba3ac5b49(tEnv);
		tEnv.getJobGraph("ads_crowd_label_metric_about_user_aucition_access_2_ou_063d6a14");
	}

	private String generateColumnStr(Table inputTable){
		List<Column> columnList = inputTable.getResolvedSchema().getColumns();
		String columnStr = "";
		for (Column column:columnList){
			columnStr += "`"+column.getName()+"`"+column.getDataType().getLogicalType().toString().replace("*PROCTIME*","")+",";
		}
		return columnStr.substring(0,columnStr.length()-1);
	}

	private FieldsDataType getDataType(DataTypes.Field... fields){
		List logicalFields = new ArrayList();
		List fieldDataTypes = new ArrayList();
		for (DataTypes.Field field : fields) {
			logicalFields.add( new RowType.RowField(
					field.getName(),
					field.getDataType().getLogicalType(),
					""));
			fieldDataTypes.add(field.getDataType());
		}
		return new FieldsDataType(new RowType(logicalFields), fieldDataTypes);
	}

	private String generateColumnStr(Table inputTable, String cf){
		if(StringUtil.isNullOrEmpty(cf)){
			return generateColumnStr(inputTable);
		}
		List<Column> columnList = inputTable.getResolvedSchema().getColumns();
		String columnStr = "";
		for (Column column:columnList){
			columnStr += "`"+column.getName()+"`"+column.getDataType().getLogicalType().toString().replace("*PROCTIME*","")+",";
		}
		columnStr = columnStr.substring(0, columnStr.length() - 1);
		columnStr = "`" + cf + "` ROW <" + columnStr + ">";
		return columnStr;
	}

	public Long relative2AbsoluteTs(String relativeTs){
		String[] arr = relativeTs.split("-");
		String unit = arr[0].trim().toUpperCase();
		Integer gap = arr.length == 1 ? 0 : Integer.parseInt(arr[1].trim());
		Long currentTs = System.currentTimeMillis();
		switch (unit){
			case "DAY":
				return currentTs - currentTs % (1000 * 3600 * 24 ) - gap * (1000 * 3600 * 24) - (1000 * 3600 * 8);
			case "HOUR":
				return currentTs - currentTs % (1000 * 3600) - gap * (1000 * 3600);
			case "MINUTE":
				return currentTs - currentTs % (1000 * 60) - gap * (1000 * 60);
			case "SECOND":
				return currentTs - currentTs % 1000 - gap * 1000 ;
			default:
				throw new IllegalArgumentException("Unsupport relativeTs:"+relativeTs);

		}
	}
	public Table source_1e4af75b011e4f55aa94ca87953a0660(TableEnvironmentImpl tEnv) {
		if(registeredTable.containsKey("source_1e4af75b011e4f55aa94ca87953a0660")){ return (Table)registeredTable.get("source_1e4af75b011e4f55aa94ca87953a0660");}
		tEnv.executeSql("CREATE TABLE IF NOT EXISTS `ads_crowd_label_metric_about_user_01` (`job`VARCHAR,`group_name`VARCHAR,`time`VARCHAR,`label_info`VARCHAR,`dim_name`VARCHAR,`dim_value`VARCHAR) WITH ('connector'='kafka','value.format'='json','topic'='ads_crowd_label_metric_about_user_01','properties.bootstrap.servers'='t0-kafka.shizhuang-inc.net:18100','scan.startup.mode'='timestamp','properties.group.id'='test','scan.startup.timestamp-millis'='#startTimeStr')".replace("#startTimeStr",relative2AbsoluteTs(tEnv.getConfig().getConfiguration().getString("relative.start.time","day"))+""));
		Table retTable;
		if(!false){
			tEnv.createTemporaryView("`FLK_VIEW_ads_crowd_label_metric_about_user_01`", tEnv.sqlQuery("SELECT * FROM ( SELECT *,ROW_NUMBER() OVER (PARTITION BY `job`,`group_name`,`label_info` ORDER BY proctime() desc) AS row_num_src3 FROM ads_crowd_label_metric_about_user_01) WHERE row_num_src3 <= 1"));
			retTable = tEnv.from("`FLK_VIEW_ads_crowd_label_metric_about_user_01`");
		}else{
			retTable = tEnv.from("ads_crowd_label_metric_about_user_01");
		}
		registeredTable.put("source_1e4af75b011e4f55aa94ca87953a0660",retTable);
		return retTable;
	}
	public Table source_398909ed50e044858c43311cc9953b04(TableEnvironmentImpl tEnv) {
		if(registeredTable.containsKey("source_398909ed50e044858c43311cc9953b04")){ return (Table)registeredTable.get("source_398909ed50e044858c43311cc9953b04");}
		tEnv.executeSql("CREATE TABLE IF NOT EXISTS `test_join_1` (  `messageKey`  VARBINARY, `message`  VARBINARY, `topic`  VARCHAR METADATA VIRTUAL, `partition`  INTEGER METADATA VIRTUAL, `offset`  BIGINT METADATA VIRTUAL, `ts` TIMESTAMP(3) METADATA FROM 'timestamp') WITH ('scan.startup.mode'='timestamp','value.fields-include'='EXCEPT_KEY','scan.startup.timestamp-millis'='#startTimeStr','key.fields'='messageKey','connector'='kafka','properties.bootstrap.servers'='t1-k8s-kafka.shizhuang-inc.net:30701','properties.group.id'='test','topic'='test_join_1','value.format'='raw','key.format'='raw')".replace("#startTimeStr",relative2AbsoluteTs(tEnv.getConfig().getConfiguration().getString("relative.start.time","day"))+""));
		Table retTable;
		if(!false){
			tEnv.createTemporaryView("`FLK_VIEW_test_join_1`", tEnv.sqlQuery("SELECT * FROM ( SELECT *,ROW_NUMBER() OVER (PARTITION BY `ts` ORDER BY proctime() desc) AS row_num_src4 FROM (SELECT JsonValue(`data`,'$.id') AS `id`,cast(JsonValue(`data`,'$.ts') AS BIGINT) AS `ts`,dts_operation_flag as FLK_BINLOG_RECORD_TYPE, `offset` as FLK_KAFKA_OFFSET, `partition` as FLK_KAFKA_PARTITION, `ts` as FLK_KAFKA_TS, CAST(JsonValue(cast(`message` as varchar),'$.es') AS BIGINT) AS FLK_BINLOG_TS FROM `test_join_1`,  LATERAL TABLE (binlogParser (`message`)) as tmp (data,old_data,dts_operation_flag,dts_db_name,dts_table_name,dts_before_flag,dts_after_flag))) WHERE row_num_src4 <= 1"));
			retTable = tEnv.from("`FLK_VIEW_test_join_1`");
		}else{
			retTable = tEnv.from("test_join_1");
		}
		registeredTable.put("source_398909ed50e044858c43311cc9953b04",retTable);
		return retTable;
	}
	public Table sql_5d390b803c1842059fa52743263c5ed2(TableEnvironmentImpl tEnv) {
		if(registeredTable.containsKey("sql_5d390b803c1842059fa52743263c5ed2")){ return (Table)registeredTable.get("sql_5d390b803c1842059fa52743263c5ed2");}
		Table retTable = null;
		retTable = source_398909ed50e044858c43311cc9953b04(tEnv);
		retTable = source_1e4af75b011e4f55aa94ca87953a0660(tEnv);

		tEnv.executeSql("create view FLK_VIEW_2a36977ca0db4b87b7a6e46dc41e1ba0_0 as SELECT `JsonValue`(`dim_value`, '$.user_id') AS `user_id`, `JsonValue`(`label_info`, '$.is_auction_access') AS `is_auction_access` FROM `FLK_VIEW_ads_crowd_label_metric_about_user_01` WHERE `job` = 'crowd_label_metric_about_user_aucition_access' AND `group_name` = 'crowd_label_metric_about_user_aucition_access'");

		if(!StringUtil.isNullOrEmpty("FLK_VIEW_2a36977ca0db4b87b7a6e46dc41e1ba0_0")){
			retTable = tEnv.from("FLK_VIEW_2a36977ca0db4b87b7a6e46dc41e1ba0_0");
		}
		registeredTable.put("sql_5d390b803c1842059fa52743263c5ed2",retTable);
		return retTable;
	}
	public Table sink_3d70c6655c274079b43934c81b34d2c8(TableEnvironmentImpl tEnv) {
		if(registeredTable.containsKey("sink_3d70c6655c274079b43934c81b34d2c8")){ return (Table)registeredTable.get("sink_3d70c6655c274079b43934c81b34d2c8");}
		Table inputTable = sql_5d390b803c1842059fa52743263c5ed2(tEnv);
		if(null!=inputTable){
			if(true){
				tEnv.createTemporaryView("view_7f12b4d44eea47c18c9330eef4da1f32",inputTable);
				inputTable = tEnv.sqlQuery("select * from FLK_VIEW_2a36977ca0db4b87b7a6e46dc41e1ba0_0");
			}
		}else{
			if(true){
				inputTable = tEnv.sqlQuery("select * from FLK_VIEW_2a36977ca0db4b87b7a6e46dc41e1ba0_0");
			}
		}
		List collect = new ArrayList();
		for(Column col : inputTable.getResolvedSchema().getColumns()) {
			collect.add("`" + col.getName() + "`");
		}
		String allFields = String.join(",",collect);
		String columnStr = "";
		columnStr = (StringUtil.isNullOrEmpty(columnStr) ? generateColumnStr(inputTable,"") : columnStr);
		String primaryKeyStr = "primary Key(user_id) NOT ENFORCED";
		String schemaStr = StringUtil.isNullOrEmpty(primaryKeyStr) ? columnStr:columnStr+","+primaryKeyStr;
		tEnv.executeSql("CREATE TABLE IF NOT EXISTS `sourcedata_user_realtime_attr` (#schema) WITH ('url'='*******************************************************************************************','bufferSize'='1000','connector'='du-adb3.0','flushIntervalMs'='1000','ignoreDelete'='true','batchSize'='1000','userName'='du_bigdata','tableName'='sourcedata_user_realtime_attr','replaceMode'='false','password'='oK**MPoNaSVWZx!X')".replace("#schema",schemaStr));
		tEnv.createTemporaryView("view_08166c88d1c54d1aa967480756d27009",inputTable);
		tEnv.sqlUpdate("INSERT INTO `sourcedata_user_realtime_attr` SELECT * FROM view_08166c88d1c54d1aa967480756d27009");
		return null;
	}
	public Table globalSink_10c4bca08a7044cdb003a46ba3ac5b49(TableEnvironmentImpl tEnv) throws Exception {
		if(registeredTable.containsKey("globalSink_10c4bca08a7044cdb003a46ba3ac5b49")){ return (Table)registeredTable.get("globalSink_10c4bca08a7044cdb003a46ba3ac5b49");}
		sink_3d70c6655c274079b43934c81b34d2c8(tEnv);
		return null;
	}

}

import org.apache.flink.runtime.jobmaster.JobResult;
import org.apache.flink.streaming.api.scala.async.ResultFuture;

import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.function.BiConsumer;

public class TestAsync {

	public static void main(String[] args) throws Exception{
		boolean test = new TestAsync().test();
		System.out.println(test);
	}

	private boolean test(){
		ExecutorService executorService = Executors.newFixedThreadPool(2);
		CompletableFuture<Boolean> resultFuture = new CompletableFuture<>();
		CompletableFuture.supplyAsync(() -> {
				try {
			Thread.sleep(5000L);
		} catch (InterruptedException e) {
			e.printStackTrace();
		}
			System.out.println("other thread");
			throw new RuntimeException("fff");
		},executorService).whenComplete((o, throwable) -> {
			if (Objects.nonNull(throwable)){
				System.out.println("exception");
				executorService.shutdown();
//				resultFuture.completeExceptionally(throwable);
			}
		});


		System.out.println("main thread");
//		System.out.println(resultFuture.get());

		System.out.println("main exit");

		return true;
	}
}

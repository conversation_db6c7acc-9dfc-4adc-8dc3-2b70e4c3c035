import org.apache.flink.api.common.state.ValueState;
import org.apache.flink.api.common.state.ValueStateDescriptor;
import org.apache.flink.api.common.typeutils.base.LongSerializer;
import org.apache.flink.api.java.functions.KeySelector;
import org.apache.flink.api.java.tuple.Tuple2;
import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.formats.common.TimestampFormat;
import org.apache.flink.formats.json.JsonOptions;
import org.apache.flink.formats.json.JsonRowDataSerializationSchema;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.datastream.KeyedStream;
import org.apache.flink.streaming.api.datastream.SingleOutputStreamOperator;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.flink.streaming.api.functions.KeyedProcessFunction;
import org.apache.flink.streaming.connectors.kafka.FlinkKafkaProducer;
import org.apache.flink.streaming.connectors.kafka.internals.KafkaSerializationSchemaWrapper;
import org.apache.flink.table.api.EnvironmentSettings;
import org.apache.flink.table.api.Table;
import org.apache.flink.table.api.bridge.java.StreamTableEnvironment;
import org.apache.flink.table.api.bridge.java.internal.StreamTableEnvironmentImpl;
import org.apache.flink.table.api.internal.TableEnvironmentImpl;
import org.apache.flink.table.data.RowData;
import org.apache.flink.table.runtime.typeutils.InternalTypeInfo;
import org.apache.flink.table.types.DataType;
import org.apache.flink.table.types.logical.LogicalType;
import org.apache.flink.util.Collector;

import java.util.Properties;


public class Test002 {

	public static void main(String[] args) throws Exception {
		StreamExecutionEnvironment env = StreamExecutionEnvironment.getExecutionEnvironment();
		EnvironmentSettings settings = EnvironmentSettings.newInstance().inStreamingMode().useBlinkPlanner().build();
		TableEnvironmentImpl tEnv = (TableEnvironmentImpl) StreamTableEnvironment.create(env, settings);

		ParameterTool parameterTool = ParameterTool.fromArgs(args);

		//初始化作业配置信息
		String bootstrapServersSource = parameterTool.get("kafka.bootstrapServersSource");
		String bootstrapServersSink = parameterTool.get("kafka.bootstrapServersSink");
		String sourceTopic = parameterTool.get("kafka.sourceTopic");
		String sinkTopic = parameterTool.get("kafka.sinkTopic");

		String compressionType = parameterTool.get("sink.compression.type");
		String batchSize = parameterTool.get("sink.batch.size");
		String lingerMs = parameterTool.get("sink.linger.ms");
		String bufferMemory = parameterTool.get("sink.buffer.memory");

		Properties kafkaSinkProps = new Properties();
		kafkaSinkProps.setProperty("bootstrap.servers", bootstrapServersSink);
		kafkaSinkProps.setProperty("compression.type", compressionType);
		kafkaSinkProps.setProperty("batch.size", batchSize);
		kafkaSinkProps.setProperty("linger.ms", lingerMs);
		kafkaSinkProps.setProperty("buffer.memory", bufferMemory);

		String startTime = "";
		tEnv.executeSql("CREATE TABLE `dwd_dewu_increasea_media_business_attribution_device` (\n" +
				"    `partition`  INTEGER METADATA VIRTUAL,\n" +
				"    `offset`  BIGINT METADATA VIRTUAL,\n" +
				"device_uuid  VARCHAR\n" +
				",device_id  VARCHAR\n" +
				",device_id_type  VARCHAR\n" +
				",`user_id`  VARCHAR\n" +
				",manufacturer  VARCHAR\n" +
				",device_model  VARCHAR\n" +
				",device_os  VARCHAR\n" +
				",device_os_version  VARCHAR\n" +
				",app_version   VARCHAR\n" +
				",android_channel  VARCHAR\n" +
				",active_ts   TIMESTAMP(3)\n" +
				",`date`  VARCHAR\n" +
				",`hour`  VARCHAR\n" +
				",user_agent  VARCHAR\n" +
				",dw_uuid  VARCHAR\n" +
				",fst_client_ip  VARCHAR\n" +
				",properties  VARCHAR\n" +
				",day_first_active_time  bigint\n" +
				",diff_time BIGINT\n" +
				",direct_flag int\n" +
				",tid varchar,\n" +
				"rid varchar,\n" +
				"log_time varchar,\n" +
				"media varchar,\n" +
				"aid varchar,\n" +
				"gid varchar,\n" +
				"pid varchar,\n" +
				"cid varchar,\n" +
				"ad_name varchar,\n" +
				"imei varchar,\n" +
				"oaid varchar,\n" +
				"android_id varchar,\n" +
				"mac varchar,\n" +
				"idfa varchar,\n" +
				"caid varchar,\n" +
				"ua varchar,\n" +
				"ip varchar,\n" +
				"ipua varchar,\n" +
				"os varchar,\n" +
				"app_id varchar,\n" +
				"module_type varchar,\n" +
				"package_name varchar,\n" +
				"four_channel varchar,\n" +
				"model varchar,\n" +
				"delivery_type varchar,\n" +
				"spu_id varchar,\n" +
				"spu_tags varchar,\n" +
				"tags varchar,\n" +
				"jump_tag varchar,\n" +
				"event_time BIGINT,\n" +
				"algo_tags varchar,\n" +
				"rta_trace_id varchar,\n" +
				"rta_exp_ids varchar,\n" +
				"recall varchar,\n" +
				"categories varchar,\n" +
				"hash_oaid varchar,\n" +
				"market varchar,\n" +
				"ipv6 varchar,\n" +
				"inactive varchar,\n" +
				"anti_spam varchar,\n" +
				"rta_valid_features varchar,\n" +
				"rta_user_weight_factor varchar,\n" +
				"site_set varchar,\n" +
				"union_site varchar,\n" +
				"deep_link varchar,\n" +
				"rta_cpc_bid varchar,\n" +
				"delivery_mode varchar,\n" +
				"action_type varchar,\n" +
				"vid varchar,\n" +
				"rta_real varchar,\n" +
				"real_cost varchar,\n" +
				"ad_platform_type varchar,\n" +
				"promoted_object_type varchar,\n" +
				"rbid varchar,\n" +
				"business_brand varchar,\n" +
				"operation_mode varchar,\n" +
				"oaid_ori varchar,\n" +
				"out_target_id varchar,\n" +
				"event_ts TIMESTAMP(3),\n" +
				"attribution_type varchar,\n" +
				" es as TO_TIMESTAMP_LTZ( day_first_active_time, 3),\n" +
				"WATERMARK FOR  es AS es - INTERVAL '5' SECOND \n" +
				") WITH (\n" +
				"    'connector' = 'kafka',\n" +
				"    'topic' = '"+sourceTopic+"',\n" +
				"    'properties.group.id' = 'dwd_dewu_increasea_media_direct_business_attribution_device_with_code',\n" +
				"    'properties.bootstrap.servers' = '10.252.41.249:9092,10.252.41.252:9092,10.252.41.251:9092,10.252.41.250:9092,10.252.42.11:9092',\n" +
				"    'scan.startup.mode' = 'timestamp',\n" +
				"    'scan.topic-partition-discovery.interval' = '30 s',\n" +
				"    'scan.startup.timestamp-millis' = '"+startTime+"',\n" +
				"    'key.format' = 'json',\n" +
				"    'key.json.ignore-parse-errors' = 'true',\n" +
				"    'key.fields' = 'device_uuid;date',\n" +
				"    'value.format' = 'json',\n" +
				"    'value.json.fail-on-missing-field' = 'false',\n" +
				"    'value.fields-include' = 'EXCEPT_KEY'\n" +
				")");

		tEnv.executeSql("CREATE VIEW business_attribution_device as\n" +
				" SELECT\n" +
				"  *\n" +
				"  FROM(\n" +
				"      SELECT\n" +
				"      *,\n" +
				"      row_number() over(partition by rid order by abs(diff_time) ASC) rk_02\n" +
				"      FROM(\n" +
				"            SELECT\n" +
				"            *,\n" +
				"            row_number() over(partition by device_uuid,`date` order by abs(diff_time) ASC) rk_01\n" +
				"            FROM\n" +
				"               dwd_dewu_increasea_media_business_attribution_device\n" +
				"            WHERE\n" +
				"                  attribution_type  in('1day','7day','ad_attribution')\n" +
				"              and `date`>='20230725'\n" +
				"              and device_uuid is not null \n" +
				"              and device_uuid <>'null'\n" +
				"              and device_uuid <>''\n" +
				"              and aid is not null\n" +
				"              and aid <>''\n" +
				"              and direct_flag =1\n" +
				"              and device_uuid<>'0000000000000000'\n" +
				"              and REGEXP_REPLACE(device_uuid,'^UUID','')<>'0000000000000000' \n" +
				"              and rid <> ''\n" +
				"              and rid is not null\n" +
				"      )\n" +
				"      WHERE rk_01 =1\n" +
				"  )\n" +
				"  where rk_02 =1\n" +
				"\n");

		Table businessAttributionDevice = tEnv.from("business_attribution_device");
		InternalTypeInfo<RowData> rowDataInternalTypeInfo = InternalTypeInfo.ofFields(businessAttributionDevice.getResolvedSchema().
				getColumnDataTypes().stream().map(DataType::getLogicalType).toArray(LogicalType[]::new));
		DataStream<Tuple2<Boolean, RowData>> twoRankStream = ((StreamTableEnvironmentImpl) tEnv).toRetractStream(businessAttributionDevice, rowDataInternalTypeInfo);

		KeyedStream<Tuple2<Boolean, RowData>, String> twoRankStreamKeyed = twoRankStream.keyBy(new KeySelector<Tuple2<Boolean, RowData>, String>() {
			@Override
			public String getKey(Tuple2<Boolean, RowData> booleanRowTuple2) throws Exception {
				return booleanRowTuple2.f1.getString(23).toString();
			}
		});
		SingleOutputStreamOperator<RowData> process = twoRankStreamKeyed.process(new KeyedProcessFunction<String, Tuple2<Boolean, RowData>, RowData>() {

			ValueState<RowData> valueState;
			ValueState<Long> firstArriveTime;

			@Override
			public void open(Configuration parameters) throws Exception {
				super.open(parameters);
				valueState = getRuntimeContext().getState(new ValueStateDescriptor<RowData>("valueState", rowDataInternalTypeInfo));
				firstArriveTime = getRuntimeContext().getState(new ValueStateDescriptor<Long>("firstArriveTime", LongSerializer.INSTANCE));
			}

			@Override
			public void processElement(Tuple2<Boolean, RowData> input, Context context, Collector<RowData> collector) throws Exception {
				Boolean rowKind = input.f0;
				if (rowKind) {
					valueState.update(input.f1);
				} else {
					valueState.clear();
				}
				Long firstArriveTimeValue = this.firstArriveTime.value();
				if (firstArriveTimeValue == null) {
					firstArriveTimeValue = input.f1.getLong(19);
					firstArriveTime.update(firstArriveTimeValue);
					firstArriveTimeValue += 7 * 60 * 1000;
					context.timerService().registerEventTimeTimer(firstArriveTimeValue);
				}
			}

			@Override
			public void onTimer(long timestamp, OnTimerContext ctx, Collector<RowData> out) throws Exception {
				RowData needSendData = valueState.value();
				if (needSendData != null) {
					out.collect(needSendData);
				}
				valueState.clear();
				firstArriveTime.clear();
			}
		});


		JsonRowDataSerializationSchema jsonRowDataSerializationSchema = new JsonRowDataSerializationSchema(
				((InternalTypeInfo<RowData>) process.getTransformation().getOutputType()).toRowType(),
				TimestampFormat.ISO_8601, JsonOptions.MapNullKeyMode.LITERAL, "", false);
		KafkaSerializationSchemaWrapper<RowData> rowDataKafkaSerializationSchemaWrapper = new KafkaSerializationSchemaWrapper<>(sinkTopic, null, true, jsonRowDataSerializationSchema);
		FlinkKafkaProducer<RowData> flinkKafkaProducer = new FlinkKafkaProducer<>(sinkTopic, rowDataKafkaSerializationSchemaWrapper, kafkaSinkProps, FlinkKafkaProducer.Semantic.AT_LEAST_ONCE, 5);

//		process.addSink(new SinkFunction<RowData>() {
//			@Override
//			public void invoke(RowData value, Context context) throws Exception {
//				System.out.println(value);
//			}
//		});

		process.addSink(flinkKafkaProducer);
//		env.setParallelism(1);
		env.execute("test");


	}
}

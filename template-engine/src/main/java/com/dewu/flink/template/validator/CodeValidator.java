package com.dewu.flink.template.validator;

import com.dewu.flink.template.translator.CodeResponse;
import com.dewu.flink.template.utils.ExceptionUtils;
import com.dewu.utils.StringUtils;
import org.codehaus.janino.SimpleCompiler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.lang.reflect.Method;
import java.util.concurrent.*;

/**
 * @Description: validator for program.
 * @author: zhiping.lin
 * @date: 2022/7/15
 */
public class CodeValidator implements Validator<CodeResponse> {
	private final static int corePoolSize = 20;
	private final static int TIMEOUT_SEC = 30;
	private static ExecutorService ES = new ThreadPoolExecutor(corePoolSize, 100, 30, TimeUnit.SECONDS, new ArrayBlockingQueue<>(corePoolSize));
	private static final Logger log = LoggerFactory.getLogger(CodeValidator.class);

	@Override
	public boolean validate(CodeResponse codeResponse) throws RuntimeException {
		Future<Boolean> rst = ES.submit(() -> {
			String code = codeResponse.getCode();
			if (StringUtils.isBlank(code)) {
				throw new ValidateException("generate code is empty");
			} else if(!code.contains("sink_")){
				throw new ValidateException("this job has no sink operator,please add it.");
			} else {
				try {
					log.info("validate :\n" + code);
					SimpleCompiler compiler = new SimpleCompiler();
					Thread.currentThread().getContextClassLoader().loadClass("org.apache.flink.table.api.ApiExpression");
					compiler.setParentClassLoader(Thread.currentThread().getContextClassLoader());
//					compiler.getClassLoader().loadClass("org.apache.flink.table.api.ApiExpression");
					compiler.cook(code);
					Class<?> classes = compiler.getClassLoader().loadClass("com.dewu.flink.template.translator.FlinkTemplateJob");
					Method main = classes.getDeclaredMethod("check");
					main.invoke(classes.getConstructors()[0].newInstance());
					return true;
				} catch (Exception e) {
					throw new ValidateException("code validate failed.", e);
				}
			}
		});
		try {
			return rst.get(TIMEOUT_SEC, TimeUnit.SECONDS);
		} catch (TimeoutException e) {
			log.warn("code validate timeout : " + ExceptionUtils.getDeepestMsg(e), e);
			rst.cancel(true);
			return true;
		} catch (Exception e) {
			rst.cancel(true);
			throw new ValidateException("code validate failed : " + ExceptionUtils.getDeepestMsg(e), e);
		}
	}

	public static void main(String[] args) {
		String code = "";


		if (StringUtils.isBlank(code)) {
			throw new ValidateException("generate code is empty");
		} else {
			try {
				System.out.println(code);
				SimpleCompiler compiler = new SimpleCompiler();
				compiler.setParentClassLoader(Thread.currentThread().getContextClassLoader());
				compiler.cook(code);
				log.info("validate :\n" + code);
				Class<?> classes = compiler.getClassLoader().loadClass("com.dewu.flink.template.translator.FlinkTemplateJob");
				Method main = classes.getDeclaredMethod("explain");
				main.invoke(classes.getConstructors()[0].newInstance());
			} catch (Exception e) {
				throw new ValidateException("code validate failed.", e);
			}
		}
	}
}

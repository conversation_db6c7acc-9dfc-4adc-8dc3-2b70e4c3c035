/*
 *  Copyright 2019-2020 <PERSON>
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */
package com.dewu.flink.template.express.service;

import com.dewu.flink.template.express.domain.TeJobRuleExpress;
import com.dewu.flink.template.express.service.dto.TeJobRuleExpressDto;
import com.dewu.flink.template.express.service.dto.TeJobRuleExpressQueryCriteria;
import org.springframework.data.domain.Pageable;

import java.util.Map;
import java.util.List;
import java.io.IOException;
import javax.servlet.http.HttpServletResponse;

/**
 * <AUTHOR>
 * @website https://el-admin.vip
 * @description 服务接口
 * @date 2023-02-08
 **/
public interface TeJobRuleExpressService {

	/**
	 * 查询数据分页
	 *
	 * @param criteria 条件
	 * @param pageable 分页参数
	 * @return Map<String, Object>
	 */
	Map<String, Object> queryAll(TeJobRuleExpressQueryCriteria criteria, Pageable pageable);

	/**
	 * 查询所有数据不分页
	 *
	 * @param criteria 条件参数
	 * @return List<TeJobRuleExpressDto>
	 */
	List<TeJobRuleExpressDto> queryAll(TeJobRuleExpressQueryCriteria criteria);

	/**
	 * 根据ID查询
	 *
	 * @param id ID
	 * @return TeJobRuleExpressDto
	 */
	TeJobRuleExpressDto findById(Long id);


	int countByMetadataId(Long metadataId);

	int countByMetadataIdWithoutId(Long id, Long metadataId);

	/**
	 * 创建
	 *
	 * @param resources /
	 * @return TeJobRuleExpressDto
	 */
	TeJobRuleExpressDto create(TeJobRuleExpress resources);

	/**
	 * 编辑
	 *
	 * @param resources /
	 */
	void update(TeJobRuleExpress resources);

	/**
	 * 多选删除
	 *
	 * @param ids /
	 */
	void deleteAll(Long[] ids);

	/**
	 * 导出数据
	 *
	 * @param all      待导出的数据
	 * @param response /
	 * @throws IOException /
	 */
	void download(List<TeJobRuleExpressDto> all, HttpServletResponse response) throws IOException;


	void updateStatus(Long id);

	void updateJobRules(String job_id, Map<String, String> principles, String exprType);

	void deleteJobRules(List<String> jobIds);

}
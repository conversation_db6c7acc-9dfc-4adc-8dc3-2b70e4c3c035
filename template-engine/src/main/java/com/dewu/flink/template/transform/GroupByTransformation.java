package com.dewu.flink.template.transform;

import com.dewu.flink.template.utils.TemplateSqlUtil;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.annotation.JsonIgnore;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import org.apache.flink.shaded.netty4.io.netty.util.internal.StringUtil;

import java.io.*;
import java.util.UUID;

@JsonIgnoreProperties(ignoreUnknown = true)
public class GroupByTransformation extends OneInputTransformation {

    private String groupByExpression;

    private String selectExpression;

    private String filterExpression;

    private String havingExpression;

    @JsonIgnore
    private String viewName = "view_"+UUID.randomUUID().toString().replace("-","");

    public GroupByTransformation() {}

    @Override
    public Boolean isFieldNeed(String fieldName) {
        return null;
    }

    public String getGroupByExpression() {
        return groupByExpression;
    }

    public String getSelectExpression() {
        return selectExpression;
    }

    public String getFilterExpression() {
        return filterExpression;
    }

    public String getHavingExpression() {
        return havingExpression;
    }

    public void setGroupByExpression(String groupByExpression) {
        this.groupByExpression = groupByExpression;
    }

    public void setSelectExpression(String selectExpression) {
        this.selectExpression = selectExpression;
    }

    public void setFilterExpression(String filterExpression) {
        this.filterExpression = filterExpression;
    }

    public void setHavingExpression(String havingExpression) {
        this.havingExpression = havingExpression;
    }

    @Override
    public String transformStr(StringWriter writer) throws IOException {
        if(getResultFunction() != null){
            return getResultFunction();
        }
        String inputFunction = this.getInput().transformStr(writer);
        setResultFunction("groupBy_"+ UUID.randomUUID().toString().replace("-",""));
        writer.write(TemplateSqlUtil.readTemplate("GroupBy.template")
                .replace("#transformFunction",getResultFunction())
                .replace("#inputFunction",inputFunction)
                .replace("#viewName",viewName)
                .replace("#groupByExpression",groupByExpression)
                .replace("#selectExpression",selectExpression)
                .replace("#filterExpression",StringUtil.isNullOrEmpty(filterExpression) ? "1=1":filterExpression)
                .replace("#havingExpression",StringUtil.isNullOrEmpty(havingExpression) ? "":" HAVING "+havingExpression));

        return getResultFunction();
    }

    @Override
    public Boolean validate() {
        if(StringUtil.isNullOrEmpty(groupByExpression)){
            return false;
        }
        if(StringUtil.isNullOrEmpty(selectExpression)){
            return false;
        }
        return true;
    }
}

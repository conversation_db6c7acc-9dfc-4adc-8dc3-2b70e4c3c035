package com.dewu.flink.template.translator.generator;

import com.alibaba.fastjson.JSONObject;
import com.dewu.flink.template.chaos.ChaosRuleInfo;
import com.dewu.flink.template.job.service.dto.TeJobDataDrillDto;
import com.dewu.flink.template.meta.GlobalMetaManager;
import com.dewu.flink.template.template.AbstractTemplate;
import com.dewu.flink.template.transform.*;
import com.dewu.flink.template.translator.CodeResponse;
import com.dewu.flink.template.utils.JsonUtil;
import com.dewu.flink.template.utils.TemplateSqlUtil;
import com.dewu.utils.StringUtils;
import com.google.common.collect.BiMap;
import com.google.common.collect.HashBiMap;
import org.apache.calcite.util.Pair;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.core.JsonProcessingException;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.flink.util.Preconditions;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static com.dewu.flink.template.translator.generator.AlgoUtil.ALGO_KAFKA_SINK_ID;
import static com.dewu.flink.template.translator.generator.AlgoUtil.ALGO_REBALANCE_ID;
import static com.dewu.flink.template.translator.generator.TemplateOption.TEMPLATE_JOB_ID;
import static com.dewu.flink.template.utils.Constant.DYNAMIC_JOB_NAME;
import static com.dewu.flink.template.utils.Constant.K8S_IMG;

@Service
public class TableCodeGenerator extends AbstractCodeGenerator {
	@Autowired
	private GlobalMetaManager globalMetaManager;

//	@Value("${spring.datasource.druid.url}")
//	@JSONField(serialize = false)
//	private String url;
//	@Value("${spring.datasource.druid.username}")
//	@JSONField(serialize = false)
//	private String username;
//	@Value("${spring.datasource.druid.password}")
//	@JSONField(serialize = false)
//	private String password;

	private TeJobDataDrillDto teJobDataDrillDto = TeJobDataDrillDto.EMPTY;

	private static final Logger logger = LoggerFactory.getLogger(TableCodeGenerator.class);

	@Override
	public void setDataDrill(TeJobDataDrillDto teJobDataDrillDto) {
		this.teJobDataDrillDto = teJobDataDrillDto;
	}

	public CodeResponse generate(String jobType, String json, String className, Properties properties, String jobName) {
		return generate(jobType, json, className, properties, jobName, null);
	}

	public CodeResponse generate(String jobType, String json, String className, Properties properties, String jobName, ChaosRuleInfo chaosRules) {
		try {
			boolean isDataDrill = teJobDataDrillDto.getIsDataDrill();
			boolean isDataSyncTemplate = false;
			ObjectMapper mapper = new ObjectMapper();
			TableCodeParam param = mapper.readValue(json, TableCodeParam.class);
			List<TransformationWrapper> transformations = param.getTransformationWrappers();

			Map<String, String> params = new HashMap<>();
			params.put("optionConfigs", param.getOptionConfigs());
			Properties newJobConfigs = TemplateSqlUtil.parsePreJobConfigs(params);
			if (null != properties && properties.size() > 0) {
				newJobConfigs.putAll(properties);
			}
			newJobConfigs.setProperty(DYNAMIC_JOB_NAME, jobName);
			logger.info("newJobConfigs:" + newJobConfigs);
			List<String> binaryTables = new ArrayList<>();
			if (newJobConfigs.containsKey("data.sync.mode")
				&& Objects.nonNull(newJobConfigs.get("data.sync.mode"))
				&& StringUtils.isNotBlank(newJobConfigs.get("data.sync.mode").toString())) {
				isDataSyncTemplate = newJobConfigs.get("data.sync.mode").toString().toLowerCase(Locale.ROOT).equals("true");
			}
			if (newJobConfigs.containsKey("binary.tables")
				&& Objects.nonNull(newJobConfigs.get("binary.tables"))
				&& StringUtils.isNotBlank(newJobConfigs.get("binary.tables").toString())) {
				binaryTables.addAll(Arrays.asList(newJobConfigs.get("binary.tables").toString().split(";")));
			}
			List<String> binlogTables = new ArrayList<>();
			if (newJobConfigs.containsKey("binlog.tables")
				&& Objects.nonNull(newJobConfigs.get("binlog.tables"))
				&& StringUtils.isNotBlank(newJobConfigs.get("binlog.tables").toString())) {
				binlogTables.addAll(Arrays.asList(newJobConfigs.get("binlog.tables").toString().split(";")));
			}
			List<String> deDupTables = new ArrayList<>();
			if (newJobConfigs.containsKey("deDup.tables")
				&& Objects.nonNull(newJobConfigs.get("deDup.tables"))
				&& StringUtils.isNotBlank(newJobConfigs.get("deDup.tables").toString())) {
				deDupTables.addAll(Arrays.asList(newJobConfigs.get("deDup.tables").toString().split(";")));
			}
			boolean globalSourceDep = true;
			if (newJobConfigs.containsKey("deDup.source.all")
				&& Objects.nonNull(newJobConfigs.get("deDup.source.all"))
				&& StringUtils.isNotBlank(newJobConfigs.get("deDup.source.all").toString())) {
				globalSourceDep = !newJobConfigs.get("deDup.source.all").equals("false");
			}
			List<JoinTransformation> joinTransformations = new ArrayList<>();
			Map<String, AbstractTransformation> transformationMap = new HashMap();
			Map<String, List<String>> sourceMap = new HashMap<>();
			if (isDataDrill) {
				for (TeJobDataDrillDto.DrillData sourceData : teJobDataDrillDto.getSourceData()) {
					sourceMap.put(sourceData.getDataName(), sourceData.getData());
				}
			}

			List<Long> sourceIds = transformations.stream().filter(t -> "SourceTransformation".equals(t.getName()) || "LookupJoinTransformation".equals(t.getName())).map(t ->
				JSONObject.parseObject(t.getTransformation()).getJSONObject("connectConfig").getLong("tableId")
			).collect(Collectors.toList());
			BiMap<String, String> tableMeta = HashBiMap.create();
			for (long id : sourceIds) {
				Pair<String, String> nameMapping = globalMetaManager.getSimpleTableMetaIdPairByTableId(id);
				tableMeta.put(nameMapping.left, nameMapping.right);
			}
			BiMap<String, String> tableMetaInverse = tableMeta.inverse();
			Map<String, String> globalConfig = JsonUtil.parseObject(globalMetaManager.getDeployInfo().getGlobalConfig(), Map.class);
			Preconditions.checkArgument(globalConfig.containsKey("delay_join_table_id"), "delay_join_table_id not found in global config");
			Preconditions.checkArgument(globalConfig.containsKey("algo_odps_log_table_id"), "algo_odps_log_table_id not found in global config");
			Preconditions.checkArgument(globalConfig.containsKey("algo_kafka_metrics_table_id"), "algo_kafka_metrics_table_id not found in global config");
			String dimTimeTableId = globalConfig.get("delay_join_table_id");
			String algoOdpsLogTableId = globalConfig.get("algo_odps_log_table_id");
			String algoKafkaMetricsTableId = globalConfig.get("algo_kafka_metrics_table_id");

			boolean isAlgoTemplate = false;

			for (TransformationWrapper transformationWrapper : transformations) {
				if (transformationWrapper.getName().equals("GroupByTransformation")) {
					GroupByTransformation groupByTransformation = mapper.readValue(transformationWrapper.getTransformation(), GroupByTransformation.class);
					if (!groupByTransformation.validate()) {
						return new CodeResponse(transformationWrapper.getName() + ":" + transformationWrapper.getId() + " 验证失败");
					}
					groupByTransformation.setInputId(transformationWrapper.getInputIds()[0]);
					transformationMap.put(transformationWrapper.getId(), groupByTransformation);
				} else if (transformationWrapper.getName().equals("JoinTransformation")) {
					JoinTransformation joinTransformation = mapper.readValue(transformationWrapper.getTransformation(), JoinTransformation.class);
					if (!joinTransformation.validate()) {
						return new CodeResponse(transformationWrapper.getName() + ":" + transformationWrapper.getId() + " 验证失败");
					}
					joinTransformation.setLeftId(transformationWrapper.getInputIds()[0]);
					joinTransformation.setRightId(transformationWrapper.getInputIds()[1]);
					if (newJobConfigs.containsKey("deDup.join.all")) {
						joinTransformation.setDeduplicateByJoinKey("true".equals(newJobConfigs.getProperty("deDup.join.all")));
					}
					transformationMap.put(transformationWrapper.getId(), joinTransformation);
					joinTransformations.add(joinTransformation);
				} else if (transformationWrapper.getName().equals("SinkTransformation")) {
					transformationMap.put(transformationWrapper.getId(), buildSink(isDataDrill, newJobConfigs, mapper, transformationWrapper));
				} else if (transformationWrapper.getName().equals("SourceTransformation")) {
					SourceTransformation sourceTransformation = mapper.readValue(transformationWrapper.getTransformation(), SourceTransformation.class);
					if (!sourceTransformation.validate()) {
						return new CodeResponse(transformationWrapper.getName() + ":" + transformationWrapper.getId() + " 验证失败");
					}
					sourceTransformation.setGlobalMetaManager(globalMetaManager);
					Properties connectConfig = sourceTransformation.getConnectConfig();
					String tableId = connectConfig.getProperty("tableId");
					if (isDataDrill) {
						String tableName = tableMetaInverse.get(tableId);
						sourceTransformation.setDrillData(sourceMap.get(tableName));
						sourceTransformation.setDataDrill(isDataDrill);
					}
					sourceTransformation.setConnectConfig(addStartTsConfig(connectConfig, newJobConfigs));
					sourceTransformation.setBinaryTables(binaryTables);
					sourceTransformation.setBinlogTables(binlogTables);
					sourceTransformation.setGlobalSourceDep(globalSourceDep);
					sourceTransformation.setDeDupTables(deDupTables);
					sourceTransformation.setNonNullContext(newJobConfigs);
					if (!isDataDrill || !tableId.equals(dimTimeTableId)) {
						transformationMap.put(transformationWrapper.getId(), sourceTransformation);
					}
				} else if (transformationWrapper.getName().equals("LookupJoinTransformation")) {
					LookupJoinTransformation lookupJoinTransformation = mapper.readValue(transformationWrapper.getTransformation(), LookupJoinTransformation.class);
					if (!lookupJoinTransformation.validate()) {
						return new CodeResponse(transformationWrapper.getName() + ":" + transformationWrapper.getId() + " 验证失败");
					}
					lookupJoinTransformation.setGlobalMetaManager(globalMetaManager);
					lookupJoinTransformation.setInputId(transformationWrapper.getInputIds()[0]);
					String tableId = lookupJoinTransformation.getConnectConfig().getProperty("tableId");
					if (isDataDrill) {
						String tableName = tableMetaInverse.get(tableId);
						lookupJoinTransformation.setDataDrill(isDataDrill);
						lookupJoinTransformation.setDrillData(sourceMap.get(tableName));
					}
					if (!isDataDrill || !tableId.equals(dimTimeTableId)) {
						transformationMap.put(transformationWrapper.getId(), lookupJoinTransformation);
					}
				} else if (transformationWrapper.getName().equals("TopnTransformation")) {
					TopnTransformation topnTransformation = mapper.readValue(transformationWrapper.getTransformation(), TopnTransformation.class);
					if (!topnTransformation.validate()) {
						return new CodeResponse(transformationWrapper.getName() + ":" + transformationWrapper.getId() + " 验证失败");
					}
					topnTransformation.setInputId(transformationWrapper.getInputIds()[0]);
					transformationMap.put(transformationWrapper.getId(), topnTransformation);
				} else if (transformationWrapper.getName().equals("CalcTransformation")) {
					transformationMap.put(transformationWrapper.getId(), buildCalc(mapper, transformationWrapper));
				} else if (transformationWrapper.getName().equals("AlgoTransformation")) {
					// add a keyBy func to help enable Parrelism modification
					GroupByTransformation groupByTransformation = new GroupByTransformation();
					groupByTransformation.setInputId(transformationWrapper.getInputIds()[0]);
					groupByTransformation.setGroupByExpression("md5(cast(message as varchar))");
					groupByTransformation.setSelectExpression("last_value(cast(message as varchar)) as message");
					groupByTransformation.setId(Integer.getInteger(ALGO_REBALANCE_ID));
					transformationMap.put(ALGO_REBALANCE_ID, groupByTransformation);
					// algo transformation will take another sink in our dag.
					isAlgoTemplate = true;
					AlgoTransformation algoTransformation = mapper.readValue(transformationWrapper.getTransformation(), AlgoTransformation.class);
					if (!algoTransformation.validate()) {
						return new CodeResponse(transformationWrapper.getName() + ":" + transformationWrapper.getId() + " 验证失败");
					}
					algoTransformation.setInputId(ALGO_REBALANCE_ID);
					transformationMap.put(transformationWrapper.getId(), algoTransformation);
					properties.setProperty(K8S_IMG, algoTransformation.buildImage());
					// build sink.
					transformationMap.put(ALGO_KAFKA_SINK_ID, buildSink(isDataDrill, newJobConfigs, mapper, AlgoUtil.buildKafkaMetricsSinkTW(algoKafkaMetricsTableId, new String[]{transformationWrapper.getId()})));
				} else if (transformationWrapper.getName().equals("UnionTransformation")) {
					UnionTransformation unionTransformation = mapper.readValue(transformationWrapper.getTransformation(), UnionTransformation.class);
					if (!unionTransformation.validate()) {
						return new CodeResponse(transformationWrapper.getName() + ":" + transformationWrapper.getId() + " 验证失败");
					}
					unionTransformation.setLeftId(transformationWrapper.getInputIds()[0]);
					unionTransformation.setRightId(transformationWrapper.getInputIds()[1]);
					transformationMap.put(transformationWrapper.getId(), unionTransformation);
				} else {
					throw new IllegalArgumentException("UnSupport Transformation:" + transformationWrapper.getName());
				}
			}
//
//			StringWriter writer = new StringWriter();
//			StringBuilder sinkFunctions = new StringBuilder();
//			String sinkExplainFunctions = "";
//			String globalsinkFunctionName = "globalsinkFunction";
//			for (String id : transformationMap.keySet()) {
//				if (transformationMap.get(id) instanceof SinkTransformation) {
////                operations.add(sink_explain_4495b79dcb254e8fa4c5f79776682b68(tEnv));
//					String sinkName = transformationMap.get(id).transformStr(writer);
//					String sinkFunction = "\t\t#sinkFunction(tEnv);\n".replace("#sinkFunction", sinkName);
//					if (isDataDrill) {
//						sinkFunction = "\t\tDataDrillSink " + sinkName + " = " + sinkFunction + "\n" + "\t\t" + globalsinkFunctionName + ".add(" + sinkName + ");";
//					}
//					sinkFunctions.append(sinkFunction);
//
//					sinkExplainFunctions += ("\t\toperations.add(#sinkFunction(tEnv));\n".replace("#sinkFunction", sinkName.replace("sink", "sink_explain")));
//				}
//			}
//
//			if (isDataDrill) {
//				String resultList = "\t\tList<DataDrillSink> " + globalsinkFunctionName + " = new ArrayList();\n";
//				sinkFunctions.insert(0, resultList);
//			}

			GlobalSinkTransformation globalSinkTransformation = new GlobalSinkTransformation();
			globalSinkTransformation.setDataDrill(isDataDrill);

			List<Transformation> globalSinkInputs = new ArrayList<>();
			List<String> globalSinkInputIds = new ArrayList<>();
			globalSinkTransformation.setId(99999);
			globalSinkTransformation.setInputs(globalSinkInputs);
			globalSinkTransformation.setInputIds(globalSinkInputIds);


			for (String transformationId : transformationMap.keySet()) {
				// build source and sink.
				if (isAlgoTemplate && transformationMap.get(transformationId) instanceof SourceTransformation) {
					SourceTransformation source = (SourceTransformation) transformationMap.get(transformationId);
					source.setStreamVarbinaryMode(true);
				} else if (transformationMap.get(transformationId) instanceof SinkTransformation) {
//                operations.add(sink_explain_4495b79dcb254e8fa4c5f79776682b68(tEnv));
					SinkTransformation sinkTransformation = (SinkTransformation) transformationMap.get(transformationId);
					globalSinkInputs.add(sinkTransformation);
					globalSinkInputIds.add(transformationId);
					if (isAlgoTemplate) {
						sinkTransformation.setVarbinaryMode(true);
						sinkTransformation.setTargetBinaryMessageKeyCol("track_id");
						if (!transformationId.equals(ALGO_KAFKA_SINK_ID)) {
							sinkTransformation.setTargetBinaryMessageCol("output");
						}
					} else if (jobType.startsWith("SYNC_")) {
						sinkTransformation.setOriginalSourceSink(true);
						sinkTransformation.setTargetBinaryMessageCol("message");
						sinkTransformation.setTargetBinaryMessageKeyCol("messageKey");
					}
				}

				// build transformation relations
				if (transformationMap.get(transformationId) instanceof OneInputTransformation) {
					OneInputTransformation oneInputTransformation = (OneInputTransformation) transformationMap.get(transformationId);
					oneInputTransformation.setInput(transformationMap.get(oneInputTransformation.getInputId()));
				} else if (transformationMap.get(transformationId) instanceof TwoInputTransformation) {
					TwoInputTransformation twoInputTransformation = (TwoInputTransformation) transformationMap.get(transformationId);
					twoInputTransformation.setLeft(transformationMap.get(twoInputTransformation.getLeftId()));
					twoInputTransformation.setRight(transformationMap.get(twoInputTransformation.getRightId()));
				}
			}


//			globalSinkTransformation.transformStr(writer);
			String ret = AbstractTemplate.buildWithSink(globalSinkTransformation, globalMetaManager, "rpp", teJobDataDrillDto, className, jobName, newJobConfigs.getProperty(TEMPLATE_JOB_ID.key()), newJobConfigs);
//			String udfList = globalMetaManager.getDeployInfo().getUdfJarList();
//			StringBuilder udfStr = new StringBuilder();
//			Map<String, String> kvs = JsonUtil.parseObject(udfList, Map.class);
//			for (Map.Entry<String, String> kv : kvs.entrySet()) {
//				udfStr.append("tEnv.executeSql(\"CREATE FUNCTION ").append(kv.getKey()).append(" AS '").append(kv.getValue()).append("'\");");
//			}
//			String jobConfigurationStr = TemplateSqlUtil.generateJobConfigurationStr(newJobConfigs);
//			String ret = TemplateSqlUtil.readTemplate(isDataDrill ? "TotalDataDrill.template" : "Total.template")
//				.replace("#udfExpr", udfStr.toString())
//				.replace("#sinkFunctions", sinkFunctions.toString())
//				.replace("#globalsinkFunctionName", globalsinkFunctionName)
//				.replace("#transformationFunction", writer.toString())
//				.replace("#className", className)
//				.replace("#sinkExplainFunctions", sinkExplainFunctions)
//				.replace("#jobConfigurationStr", jobConfigurationStr)
//				.replace("#jobName", jobName)
//				.replace("#miniCluster", isDataDrill ? "miniCluster" : "null");

//			writer.close();
//			logger.info(ret);
			return new CodeResponse(ret);
		} catch (Exception e) {
			throw new CodeGenerateException("fail to generate code for : " + json, e);
		}
	}

	public Properties addStartTsConfig(Properties properties, Properties jobConfigurations) {
		if (jobConfigurations.containsKey("scan.startup.mode")) {
			switch (jobConfigurations.getProperty("scan.startup.mode")) {
				case "timestamp":
					properties.setProperty("scan.startup.mode", "timestamp");
					properties.setProperty("scan.startup.timestamp-millis", jobConfigurations.get("scan.startup.timestamp-millis").toString());
					break;
				case "latest-offset":
					properties.setProperty("scan.startup.mode", "latest-offset");
					break;
				case "earliest-offset":
					properties.setProperty("scan.startup.mode", "earliest-offset");
					break;
				default:
			}
		}
		return properties;
	}

	private void generateCreateFunctionStr(String sql) {
		Pattern p = Pattern.compile("(sqlQuery\\(\\\"(.*?)\\\"\\))");
		Matcher m = p.matcher(sql);
		while (m.find()) {
			System.out.println(m.group().replace("sqlQuery(\"", "").replace("\")", "").trim());
		}
	}

	private CalcTransformation buildCalc(ObjectMapper mapper, TransformationWrapper transformationWrapper) throws JsonProcessingException {
		CalcTransformation calcTransformation = mapper.readValue(transformationWrapper.getTransformation(), CalcTransformation.class);
		if (!calcTransformation.validate()) {
			throw new RuntimeException(transformationWrapper.getName() + ":" + transformationWrapper.getId() + " 验证失败");
		}
		calcTransformation.setInputId(transformationWrapper.getInputIds()[0]);
		return calcTransformation;
	}

	private SinkTransformation buildSink(boolean isDataDrill, Properties newJobConfigs, ObjectMapper mapper, TransformationWrapper transformationWrapper) throws JsonProcessingException {
		SinkTransformation sinkTransformation = mapper.readValue(transformationWrapper.getTransformation(), SinkTransformation.class);
		if (!sinkTransformation.validate()) {
			throw new RuntimeException(transformationWrapper.getName() + ":" + transformationWrapper.getId() + " 验证失败");
		}
		sinkTransformation.setGlobalMetaManager(globalMetaManager);
		sinkTransformation.setInputId(transformationWrapper.getInputIds()[0]);
		sinkTransformation.setDataDrill(isDataDrill);
		if (isDataDrill && !teJobDataDrillDto.getExpectData().isEmpty()) {
			sinkTransformation.setDrillData(teJobDataDrillDto.getExpectData().get(0).getData());
		}
//		newJobConfigs.setProperty(DYNAMIC_JOB_NAME, )
		sinkTransformation.setNonNullContext(newJobConfigs);
		return sinkTransformation;
	}
//
//	public static void main(String[] args) {
//		// new TableCodeGenerator().generateCreateFunctionStr("sqlQuery(\" a(b)c\")abc   sqlQuery(\"de\")");
//		String json = "[{\"inputIds\":[1],\"name\":\"SinkTransformation\",\"id\":0,\"transformation\":\"{\\\"schemaConfig\\\":{\\\"primaryKey\\\":\\\"name\\\"},\\\"connectConfig\\\":{\\\"tableId\\\":\\\"1003\\\"}}\"},{\"inputIds\":[2],\"name\":\"GroupByTransformation\",\"id\":1,\"transformation\":\"{\\\"filterExpression\\\":\\\"\\\",\\\"havingExpression\\\":\\\"COUNT(1) > 1\\\",\\\"groupByExpression\\\":\\\"`time1`\\\",\\\"selectExpression\\\":\\\"COUNT(1)\\\"}\"},{\"inputIds\":[3],\"name\":\"CalcTransformation\",\"id\":2,\"transformation\":\"{\\\"filterExpression\\\":\\\"`age` = 20\\\",\\\"selectExpression\\\":\\\"`name`, `age`\\\"}\"},{\"inputIds\":[4],\"name\":\"GroupByTransformation\",\"id\":3,\"transformation\":\"{\\\"filterExpression\\\":\\\"\\\",\\\"havingExpression\\\":\\\"\\\",\\\"groupByExpression\\\":\\\"`name`\\\",\\\"selectExpression\\\":\\\"*\\\"}\"},{\"inputIds\":[],\"name\":\"SourceTransformation\",\"id\":4,\"transformation\":\"{\\\"connectConfig\\\":{\\\"tableId\\\":\\\"1002\\\"}}\"}]\n";
//		System.out.println(new TableCodeGenerator().generate(json, "Test", null, UUID.randomUUID().toString()));
//	}
}

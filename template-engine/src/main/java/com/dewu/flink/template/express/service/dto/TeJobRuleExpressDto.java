/*
*  Copyright 2019-2020 <PERSON>
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.dewu.flink.template.express.service.dto;

import com.dewu.flink.template.rule.base.express.FactoryExpression;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.validation.constraints.NotNull;
import java.sql.Timestamp;
import java.io.Serializable;

/**
* @website https://el-admin.vip
* @description /
* <AUTHOR>
* @date 2023-02-08
**/
@Data
public class TeJobRuleExpressDto implements Serializable {

    /** 自增id */
    private Long id;

    /** 表达式 */
    private String ruleExpress;

    /** 作业id */
    private String teJobInfoId;

    /** 规则组id */
    private Long teJobRuleGroupId;

    /** 创建时间 */
    private Timestamp createTime;

    /** 修改时间 */
    private Timestamp modifyTime;

    /** 规则输出目标表 */
    private String reDataSourceMetadataId;

    private String isUsed;

    private Long ruleType;

    private String exprType;

    private String ruleTag;
}
package com.dewu.flink.template.transform;

import com.dewu.flink.template.utils.TemplateSqlUtil;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import java.io.IOException;
import java.io.StringWriter;
import java.util.Properties;
import java.util.UUID;

@JsonIgnoreProperties(ignoreUnknown = true)
public class DelayEmitTransformation extends OneInputTransformation {
	private String leftView = "left_view_" + UUID.randomUUID().toString().replace("-", "");
	private String leftViewAlias = "tl";
	private String rightViewAlias = "tr";
	private Integer delayMinutes = 0;
	private String delayJoinTableId;


	public DelayEmitTransformation() {
	}

	@Override
	public Boolean isFieldNeed(String fieldName) {
		return null;
	}

	public String getLeftViewAlias() {
		return leftViewAlias;
	}

	public String getRightViewAlias() {
		return rightViewAlias;
	}


	public Integer getDelayMinutes() {
		return delayMinutes;
	}

	public String getDelayJoinTableId() {
		return delayJoinTableId;
	}

	public void setLeftViewAlias(String leftViewAlias) {
		this.leftViewAlias = leftViewAlias;
	}

	public void setRightViewAlias(String rightViewAlias) {
		this.rightViewAlias = rightViewAlias;
	}


	public void setDelayMinutes(Integer delayMinutes) {
		this.delayMinutes = delayMinutes;
	}

	public void setDelayJoinTableId(String delayJoinTableId) {
		this.delayJoinTableId = delayJoinTableId;
	}

	@Override
	public String transformStr(StringWriter writer) throws IOException {
		if (getResultFunction() != null) {
			return getResultFunction();
		}
		String leftFunction = this.getInput().transformStr(writer);
		setResultFunction("delay_emit_" + UUID.randomUUID().toString().replace("-", ""));

		Properties connectConfig = new Properties();
		connectConfig.put("tableId", delayJoinTableId);
		updateConnectConfig(connectConfig, getGlobalMetaManager().getTableMetaByTableId(Long.valueOf(connectConfig.getProperty("tableId"))), SourceTransformation.class);

		connectConfig.remove("tableId");
		writer.write(TemplateSqlUtil.readTemplate("DelayEmit.template")
			.replace("#transformFunction", getResultFunction())
			.replace("#leftFunction", leftFunction)
			.replace("#leftView", leftView)
			.replace("#leftAlias", leftViewAlias)
			.replace("#rightAlias", rightViewAlias)
			.replace("#delayMinutes", String.valueOf(delayMinutes))
			.replace("#config", TemplateSqlUtil.generateConfigStr(connectConfig))
		);

		return getResultFunction();
	}

	@Override
	public Boolean validate() {
		return true;
	}
}

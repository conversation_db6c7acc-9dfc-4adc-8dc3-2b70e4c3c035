package com.dewu.flink.template.transform.service;

import com.dewu.flink.template.transform.domain.TransformationConfig;

import java.util.List;

/**
 * transformationService interface for web api.
 */
public interface TransformationConfigService {
	 List<TransformationConfig>   findAllByTransformationName(String name);

	List<TransformationConfig>  findAllByTransformationNameAndType(String name,String Type );

}

package com.dewu.flink.template.deploy;

import com.dewu.flink.template.common.Response;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Author: WangLin
 * Date: 2023/1/11 上午10:31
 * Description: 更新作业监控返回结果
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class UpdateTaskMonitorResponse implements Response {
	/**
	 * 是否更新成功
	 */
	private boolean flag;
	/**
	 * 更新提示信息【成功："OK" 失败："具体失败原因"】
	 */
	private String msg;
}

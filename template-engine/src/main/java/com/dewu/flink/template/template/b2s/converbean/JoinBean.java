package com.dewu.flink.template.template.b2s.converbean;

import com.dewu.flink.template.utils.JsonUtil;
import lombok.EqualsAndHashCode;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.core.JsonProcessingException;

import java.util.HashMap;
import java.util.Map;

/**
 * @Description: XXX
 * @author: zhiping.lin
 * @date: 2022/8/24
 */
@EqualsAndHashCode(callSuper = true)
public class JoinBean extends AbstractTransformBean {
	private String joinConditionExpression = null;
	private String joinType = null;
	private boolean isDedup = false;
	private String leftViewAlias = "tl";
	private String rightViewAlias = "tr";

	public JoinBean(String joinConditionExpression, String joinType, boolean isDedup) {
		this.id = id_id.getAndIncrement();
		this.selectExpression = "*";
		this.joinConditionExpression = joinConditionExpression;
		this.joinType = joinType;
		this.isDedup = isDedup;
		this.name = "JoinTransformation";
	}

	public String getJoinConditionExpression() {
		return joinConditionExpression;
	}

	public void setJoinConditionExpression(String joinConditionExpression) {
		this.joinConditionExpression = joinConditionExpression;
	}

	public String getJoinType() {
		return joinType;
	}

	public void setJoinType(String joinType) {
		this.joinType = joinType;
	}

	public boolean isDedup() {
		return isDedup;
	}

	public void setDedup(boolean dedup) {
		isDedup = dedup;
	}

	public String getLeftViewAlias() {
		return leftViewAlias;
	}

	public void setLeftViewAlias(String leftViewAlias) {
		this.leftViewAlias = leftViewAlias;
	}

	public String getRightViewAlias() {
		return rightViewAlias;
	}

	public void setRightViewAlias(String rightViewAlias) {
		this.rightViewAlias = rightViewAlias;
	}

	@Override
	public String wrapTransformation() throws JsonProcessingException {
		Map<String, Object> transform = new HashMap<>();
		transform.put("joinType", joinType);
		transform.put("selectExpression", selectExpression);
		transform.put("isDeduplicateByJoinKey", isDedup);
		transform.put("joinConditionExpression", joinConditionExpression);
		transform.put("leftViewAlias", leftViewAlias);
		transform.put("rightViewAlias", rightViewAlias);
		return JsonUtil.writeString(transform);
	}

	@Override
	public String toString() {
		try {
			return getString();
		} catch (JsonProcessingException e) {
			e.printStackTrace();
		}
		return null;
	}
}

package com.dewu.flink.template.translator.generator;

import com.dewu.flink.template.transform.LookupJoinTransformation;
import com.dewu.flink.template.transform.SinkTransformation;
import com.dewu.flink.template.transform.SourceTransformation;

import static com.dewu.flink.template.translator.generator.DataSourceType.DMQ;
import static com.dewu.flink.template.translator.generator.DataSourceType.KAFKA;
import static com.dewu.flink.template.translator.generator.LibraConnectorType.*;

/**
 * @Description: manage all connectors from libra
 * @author: zhiping.lin
 * @date: 2022/8/17
 */
public class ConnectorManager {

	private String name;

	public java.lang.String getName() {
		return name;
	}

	public static LibraConnectorType getLibraConnector(DataSourceType dataSourceType, Class<?> clazz) {
		switch (dataSourceType) {
			case RDS:
				return C_RDS;
			case DMQ:
				return C_ROCKET_MQ;
			case KAFKA:
				if (clazz.isAssignableFrom(SinkTransformation.class)) {
					return C_UPSERT_KAFKA;
				} else if (clazz.isAssignableFrom(SourceTransformation.class)) {
					return C_KAFKA;
				} else if (clazz.isAssignableFrom(LookupJoinTransformation.class)) {
					return C_KAFKA;
				} else {
					throw new RuntimeException(String.format("Not a valid transformation[%s] to fetch a connector.", clazz));
				}
			case HBASE:
				return C_HBASE;
			case ODPS:
				return C_DU_ODPS;
			case CLICKHOUSE:
				return C_CLICKHOUSE;
			case ADB:
				return C_ADB;
			case PRINT:
				return C_PRINT;
			case STARROCKS:
				return C_STAR_ROCKS_NEW;
			case DATAGEN:
				return C_DATAGEN;
			case ES:
				return C_DU_ES_6;
			default:
				throw new RuntimeException("Not support connector type " + dataSourceType);
		}
	}

	public static boolean isOfflineDataSources(DataSourceType dataSourceType) {
		return !KAFKA.equals(dataSourceType) && !DMQ.equals(dataSourceType);
	}

	public static boolean isStreamDataSources(DataSourceType dataSourceType) {
		return KAFKA.equals(dataSourceType) || DMQ.equals(dataSourceType);
	}
}

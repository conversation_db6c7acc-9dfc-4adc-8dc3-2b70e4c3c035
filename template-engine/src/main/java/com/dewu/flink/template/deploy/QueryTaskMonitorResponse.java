package com.dewu.flink.template.deploy;

import com.dewu.flink.template.common.Response;
import com.dewu.flink.template.job.domain.TaskMonitor;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Author: WangLin
 * Date: 2023/1/10 下午18:05
 * Description: 查询作业监控返回结果
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class QueryTaskMonitorResponse implements Response {
	/**
	 * 是否查询成功
	 */
	private boolean flag;
	/**
	 * 查询提示信息【成功："OK" 失败："具体失败原因"】
	 */
	private String msg;
	/**
	 * 查询的结果
	 */
	private TaskMonitor taskMonitor;
}

package com.dewu.flink.template.template.b2s.converbean;

import com.dewu.flink.template.utils.JsonUtil;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.core.JsonProcessingException;

import java.util.HashMap;
import java.util.Map;

/**
 * @Description: XXX
 * @author: zhiping.lin
 * @date: 2022/8/24
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class SourceBean extends AbstractTransformBean {
	String tableId;
	String table;
	private boolean isSink = false;

	public SourceBean(String tableName, String tableId) {
		this.setId(id_id.getAndIncrement());
		this.name = "SourceTransformation";
		this.table = tableName;
		this.tableId = tableId;
	}

	@Override
	public String wrapTransformation() throws JsonProcessingException {
		Map<String, String> connectConfig = new HashMap<>();
		Map<String, Object> transform = new HashMap<>();
		connectConfig.put("tableId", tableId);
		transform.put("connectConfig", connectConfig);
		return JsonUtil.writeString(transform);
	}

	@Override
	public String toString() {
		try {
			return getString();
		} catch (JsonProcessingException e) {
			e.printStackTrace();
		}
		return null;
	}
}

package com.dewu.flink.template.translator.generator;

/**
 * @Description: XXX
 * @author: zhiping.lin
 * @date: 2023/4/17
 */
public enum LibraConnectorType {
	C_RDS(1, "du-rds"),

	C_ADB(3, "du-adb3.0"),

	<PERSON><PERSON>KAFKA(4, "kafka"),

	C_UPSERT_KAFKA(5, "du-kafka"),

	C_ORIGIN_UPSERT_KAFKA(11, "upsert-kafka"),

	C_CLICKHOUSE(8, "du-clickhouse"),

	C_PRINT(9, "print"),

	C_STAR_ROCKS_NEW(10, "du-starrocks-new"),

	C_STAR_ROCKS(16, "du-starrocks"),

	C_STAR_ROCKS_1(34, "starrocks"),

	C_DATAGEN(12, "datagen"),

	C_ROCKET_MQ(13, "du-rocketmq"),

	C_JDBC(14, "jdbc"),

	C_SLS(16, "sls"),

	C_DU_SLS(17, "du-sls"),

	C_DU_ES_6(2, "du-elasticsearch-6"),
	C_ES_6(18, "elasticsearch-6"),
	C_ES_7(19, "elasticsearch-7"),
	C_DU_ES_7(20, "du-elasticsearch-7"),

	C_HBASE(6, "du-cloudhbase"),
	C_DU_HBASE_2(21, "du-cloudhbase-v2"),


	C_DU_ODPS(7, "du-odps"),
	C_ODPS_V2(15, "du-odps-v2"),
	C_DU_CONTIMUOUS_ODPS(22, "du-continuous-odps"),
	C_ODPS(23, "odps"),


	C_DU_REDIS(24, "du-redis"),
	C_REDIS(25, "redis"),

	C_SENSORSDATE(26, "sensorsdata"),
	C_PAIMON(27, "paimon"),
	C_HOLOGRES(28, "hologres"),
	C_FILESYSTEM(29, "filesystem"),
	C_BLACKHOLE(30, "blackhole"),
	C_DU_INFLUXDB(31, "du-influxdb"),
	C_DU_VICTORIA_METRIC(32, "du-victoria-metric"),
	C_VICTORIA_METRIC(33, "victoria-metric"),

	C_ODPS_KAFKA(35, "odps-kafka"),



	;

	private String name;
	private int id;

	LibraConnectorType(int id, String name) {
		this.name = name;
		this.id = id;
	}

	public String getName() {
		return name;
	}

	public int getId() {
		return id;
	}
}

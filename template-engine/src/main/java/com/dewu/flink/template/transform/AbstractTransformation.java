package com.dewu.flink.template.transform;

import com.alibaba.fastjson.JSON;
import com.dewu.flink.template.config.ColumnInfo;
import com.dewu.flink.template.config.SchemaConfig;
import com.dewu.flink.template.meta.GlobalMetaBaseDomain;
import com.dewu.flink.template.meta.GlobalMetaManager;
import com.dewu.flink.template.meta.param.ParamHandler;
import com.dewu.flink.template.meta.tablefield.service.dto.ReDataSourceTableFieldDto;
import com.dewu.flink.template.meta.tableinfo.service.dto.ReDataSourceMetadataDto;
import com.dewu.flink.template.transform.domain.TransformationConfig;
import com.dewu.flink.template.translator.generator.ConnectorManager;
import com.dewu.flink.template.translator.generator.DataSourceType;
import com.dewu.flink.template.utils.TemplateSqlUtil;
import lombok.Data;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.annotation.JsonIgnore;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import org.apache.flink.shaded.netty4.io.netty.util.internal.StringUtil;
import org.apache.flink.util.CollectionUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Properties;
import java.util.stream.Collectors;

// 校验
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public abstract class AbstractTransformation implements Transformation {
	private static Logger logger = LoggerFactory.getLogger(AbstractTransformation.class);

	@JsonIgnore
	private Integer id;
	@JsonIgnore
	private String name;
	@JsonIgnore
	private String resultFunction;
	@JsonIgnore
	private GlobalMetaManager globalMetaManager;
	@JsonIgnore
	protected Properties context = new Properties();


	public AbstractTransformation() {

	}

	public void setNonNullContext(Properties context) {
		if (null != context) {
			this.context = context;
		}
	}

	protected <T extends AbstractTransformation> void updateConnectConfig(Properties connectConfig, GlobalMetaBaseDomain globalMetaBaseDomain, Class<T> clazz) {
		updateConnectConfig(connectConfig, globalMetaBaseDomain, clazz, true);
	}

	protected <T extends AbstractTransformation> void updateConnectConfig(Properties connectConfig, GlobalMetaBaseDomain globalMetaBaseDomain, Class<T> clazz, boolean isSource) {
		String connector = ConnectorManager.getLibraConnector(DataSourceType.numOf(globalMetaBaseDomain.getReDataSourceDto().getDataSourceType()), clazz).getName();
		List<TransformationConfig> transformationConfigList = globalMetaManager.getTransformationMeta(this.getClass().getSimpleName(), connector);
		connectConfig.setProperty("connector", connector);
		ParamHandler.buildConnector(connector, isSource, connectConfig, globalMetaBaseDomain, context);
		for (TransformationConfig transformationConfig : transformationConfigList) {
			if (!StringUtil.isNullOrEmpty(transformationConfig.getRequiredColumns())) {
				List<Column> requiredColumnList = JSON.parseArray(transformationConfig.getRequiredColumns(), Column.class);
				for (Column requiredColumn : requiredColumnList) {
					connectConfig.put(requiredColumn.getName(), connectConfig.getOrDefault(requiredColumn.getName(), requiredColumn.getDefaultValue()));
				}
			}
		}
	}

	protected void updateSchemaConfig(Properties connectConfig, SchemaConfig schemaConfig, GlobalMetaBaseDomain globalMetaBaseDomain) {
		List<ColumnInfo> columnInfoList = schemaConfig.getColumns();
		logger.info("begin to update schema for " + connectConfig.getProperty("topic") + " with " + schemaConfig);
		if (columnInfoList == null || columnInfoList.size() == 0) {
			columnInfoList = (columnInfoList == null ? new ArrayList() : columnInfoList);
			List<ReDataSourceTableFieldDto> reDataSourceTableFieldDtoList = globalMetaBaseDomain.getReDataSourceTableFieldDtos();
			ReDataSourceMetadataDto reDataSourceMetadataDto = globalMetaBaseDomain.getReDataSourceMetadataDto();
			for (ReDataSourceTableFieldDto reDataSourceTableFieldDto : reDataSourceTableFieldDtoList) {
				if (isFieldNeed(reDataSourceTableFieldDto.getFieldName())) {
					columnInfoList.add(new ColumnInfo(reDataSourceTableFieldDto.getFieldName(), TemplateSqlUtil.dataTypeMapping(reDataSourceTableFieldDto.getDataType())));
				}
			}
			schemaConfig.setColumns(columnInfoList);
			schemaConfig.setPrimaryKey(reDataSourceMetadataDto.getUniqueKey());
			logger.info("finish update schema for " + connectConfig.getProperty("topic") + " with " + schemaConfig);
		}
	}

	private static class Column {
		private String name;
		private String type;
		private String defaultValue;

		public String getName() {
			return name;
		}

		public String getType() {
			return type;
		}

		public String getDefaultValue() {
			return defaultValue;
		}

		public void setName(String name) {
			this.name = name;
		}

		public void setType(String type) {
			this.type = type;
		}

		public void setDefaultValue(String defaultValue) {
			this.defaultValue = defaultValue;
		}
	}

	public abstract Boolean isFieldNeed(String fieldName);
}

package com.dewu.flink.template.deploy;

import com.dewu.flink.template.deploy.dmq.CreateKafkaTopicRequest;
import com.dewu.flink.template.deploy.dmq.DmqKafkaManager;
import com.dewu.flink.template.deploy.dmq.KafkaTopicDetail;
import com.dewu.flink.template.deploy.dmq.QueryKafkaTopicRequest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.Optional;

/**
 * Author: WangLin
 * Date: 2022/7/25 上午10:27
 * Description:
 */
@Component
public class DeployExample {

	@Autowired
	private RealtimePlatformDeployManager deployManager;

	@Autowired
	private DmqKafkaManager dmqKafkaManager;

//	@Scheduled(cron = "*/10 * * * * ?")
//	public void queryTopic() throws Exception {
//
//		QueryKafkaTopicRequest queryKafkaTopicRequest = new QueryKafkaTopicRequest();
//		queryKafkaTopicRequest.setTopic("realtime_test11111");
//
//		Optional<KafkaTopicDetail> kafkaTopicDetail = dmqKafkaManager.queryKafkaTopic(queryKafkaTopicRequest);
//
//		System.out.println(kafkaTopicDetail);
//
//	}


//	@Scheduled(cron = "*/10 * * * * ?")
//	public void createTopic() throws Exception {
//
//		CreateKafkaTopicRequest createKafkaTopicRequest = new CreateKafkaTopicRequest();
//
//		createKafkaTopicRequest.setTopic("lin_lin_test_v1000");
//		createKafkaTopicRequest.setPartition(2);
//		createKafkaTopicRequest.setUsername("wanglin");
//
//		Optional<KafkaTopicDetail> kafkaTopicDetail = dmqKafkaManager.createKafkaTopic(createKafkaTopicRequest);
//
//		System.out.println(kafkaTopicDetail);
//
//	}


//	@Scheduled(cron = "*/10 * * * * ?")
//	public void test_submit() throws Exception {
//
//		SubmitTaskRequest submitTaskRequest = new SubmitTaskRequest();
//		submitTaskRequest.setOperator("<EMAIL>");
//		submitTaskRequest.setTaskId("e278502573dcdc1763e248bee52bdc32");
//		submitTaskRequest.setCheckpointUrl("hdfs://hdfs-bigdata-hdd-c1/meta/flink/checkpoint/e278502573dcdc1763e248bee52bdc32/53544/00000000000000000000000000000000/chk-8");
//		SubmitTaskResponse submitTaskResponse = deployManager.submitTask(submitTaskRequest);
//
//		System.out.println(submitTaskResponse);
//	}


//	@Scheduled(cron = "*/10 * * * * ?")
//	public void test_kill() throws Exception {
//
//		KillTaskRequest killTaskRequest = new KillTaskRequest();
//		killTaskRequest.setOperator("<EMAIL>");
//		killTaskRequest.setTaskId("d97db9a87d93b7cc22679f5018ae45d3");
//		KillTaskResponse killTaskResponse = deployManager.killTask(killTaskRequest);
//
//		System.out.println(killTaskResponse);
//	}


//	@Scheduled(cron = "*/10 * * * * ?")
//	public void test_delete() throws Exception {
//
//		DeleteTaskRequest deleteTaskRequest = new DeleteTaskRequest("a650be9ab71a54fc2bc0973a3d4b8af9");
//		DeleteTaskResponse deleteTaskResponse = deployManager.deleteTask(deleteTaskRequest);
//
//		System.out.println(deleteTaskResponse);
//	}


//	@Scheduled(cron = "*/10 * * * * ?")
//	public void test_create() throws Exception {
//
//		CreateTaskRequest createTaskRequest = new CreateTaskRequest();
//		createTaskRequest.setEngineName("flink-1.13");
//		createTaskRequest.setJarId(632);
//		createTaskRequest.setMainClass("com.stephen.job.StephenTest");
//		createTaskRequest.setParentFolderId(0);
//		createTaskRequest.setProjectName("tech-data-dw2");
//		createTaskRequest.setResourceName("k8s-cluster");
//		createTaskRequest.setTaskDesc("stephen test");
//		createTaskRequest.setTaskName("stephen_test_004");
//		createTaskRequest.setTaskType(1);
//		createTaskRequest.setOwners("<EMAIL>,<EMAIL>");
//		createTaskRequest.setOperator("<EMAIL>");
//
//		CreateTaskRequest.FlinkParams flinkParams = new CreateTaskRequest.FlinkParams();
//		flinkParams.setParallel(1);
//		flinkParams.setSlotPerTm(1);
//		createTaskRequest.setFlinkParams(flinkParams);
//
//		CreateTaskRequest.MainParams mainParams = new CreateTaskRequest.MainParams();
//		CreateTaskRequest.Items mainItem = new CreateTaskRequest.Items();
//		mainItem.setKey("author");
//		mainItem.setValue("stephen");
//		List<CreateTaskRequest.Items> mainItemsList = new ArrayList<>();
//		mainItemsList.add(mainItem);
//		mainParams.setItems(mainItemsList);
//		createTaskRequest.setMainParams(mainParams);
//
//		CreateTaskRequest.FlinkCustomParams flinkCustomParams = new CreateTaskRequest.FlinkCustomParams();
//		CreateTaskRequest.Items customItem = new CreateTaskRequest.Items();
//		customItem.setKey("kubernetes.jobmanager.cpu");
//		customItem.setValue("1");
//		List<CreateTaskRequest.Items> customItemsList = new ArrayList<>();
//		customItemsList.add(customItem);
//		flinkCustomParams.setItems(customItemsList);
//		createTaskRequest.setFlinkCustomParams(flinkCustomParams);
//
//		CreateTaskRequest.UdfJarList udfJarList = new CreateTaskRequest.UdfJarList();
//		udfJarList.setBuName("tech-data");
//		udfJarList.setJarId(632);
//		udfJarList.setJarName("stephen_test");
//		udfJarList.setJarVersion(3);
//		udfJarList.setJarPath("libra/user/jars/platform/tech-data-dw2/upload-stephen_test_022707096003707209194-20220722101651569.jar");
//		udfJarList.setProjectName("tech-data-dw2");
//		List<CreateTaskRequest.UdfJarList> udfJarListList = new ArrayList<>();
//		udfJarListList.add(udfJarList);
//		createTaskRequest.setUdfJarList(udfJarListList);
//
//		CreateTaskResponse createTaskResponse = deployManager.createTask(createTaskRequest);
//		System.out.println(createTaskResponse);
//	}

//
//
//	@Scheduled(cron = "*/10 * * * * ?")
//	public void test_update() throws Exception {
//
//		UpdateTaskRequest updateTaskRequest = new UpdateTaskRequest();
//		updateTaskRequest.setTaskId("d97db9a87d93b7cc22679f5018ae45d3");
//		updateTaskRequest.setEngineName("flink-1.13");
//		updateTaskRequest.setJarId(632);
//		updateTaskRequest.setMainClass("com.stephen.job.StephenTest");
//		updateTaskRequest.setParentFolderId(0);
//		updateTaskRequest.setProjectName("tech-data-dw2");
//		updateTaskRequest.setResourceName("k8s-cluster");
//		updateTaskRequest.setTaskDesc("stephen lin test haha");
//		updateTaskRequest.setTaskName("stephen_test_003");
//		updateTaskRequest.setTaskType(1);
//		updateTaskRequest.setOperator("<EMAIL>");
//
//		UpdateTaskRequest.FlinkParams flinkParams = new UpdateTaskRequest.FlinkParams();
//		flinkParams.setParallel(1);
//		flinkParams.setSlotPerTm(1);
//		updateTaskRequest.setFlinkParams(flinkParams);
//
//		UpdateTaskRequest.MainParams mainParams = new UpdateTaskRequest.MainParams();
//		UpdateTaskRequest.Items mainItem = new UpdateTaskRequest.Items();
//		mainItem.setKey("author");
//		mainItem.setValue("stephen");
//		List<UpdateTaskRequest.Items> mainItemsList = new ArrayList<>();
//		mainItemsList.add(mainItem);
//		mainParams.setItems(mainItemsList);
//		updateTaskRequest.setMainParams(mainParams);
//
//		UpdateTaskRequest.FlinkCustomParams flinkCustomParams = new UpdateTaskRequest.FlinkCustomParams();
//		UpdateTaskRequest.Items customItem = new UpdateTaskRequest.Items();
//		customItem.setKey("kubernetes.jobmanager.cpu");
//		customItem.setValue("2");
//		List<UpdateTaskRequest.Items> customItemsList = new ArrayList<>();
//		customItemsList.add(customItem);
//		flinkCustomParams.setItems(customItemsList);
//		updateTaskRequest.setFlinkCustomParams(flinkCustomParams);
//
//		UpdateTaskRequest.UdfJarList udfJarList = new UpdateTaskRequest.UdfJarList();
//		udfJarList.setBuName("tech-data");
//		udfJarList.setJarId(632);
//		udfJarList.setJarName("stephen_test");
//		udfJarList.setJarVersion(3);
//		udfJarList.setJarPath("libra/user/jars/platform/tech-data-dw2/upload-stephen_test_022707096003707209194-20220722101651569.jar");
//		udfJarList.setProjectName("tech-data-dw2");
//		List<UpdateTaskRequest.UdfJarList> udfJarListList = new ArrayList<>();
//		udfJarListList.add(udfJarList);
//		updateTaskRequest.setUdfJarList(udfJarListList);
//
//		UpdataTaskResponse updataTaskResponse = deployManager.updateTask(updateTaskRequest);
//		System.out.println(updataTaskResponse);
//	}


//	@Scheduled(cron = "*/10 * * * * ?")
//	public void test_query() throws Exception {
//		String[] taskIdList = {"de80b4a8495dec2916235d71f01283d1", "d09691fce9d3d92765414ba21479c99b"};
//		String projectName = "tech-data-dw2";
//
//		QueryTaskRequest queryTaskRequest = new QueryTaskRequest(taskIdList, projectName);
//		QueryTaskResponse queryTaskResponse = deployManager.queryJobStatus(queryTaskRequest);
//		System.out.println(queryTaskResponse);
//	}


//	@Scheduled(cron = "*/10 * * * * ?")
//	public void test_update_monitor() throws Exception {
//		String taskId = "e763de491945b9ab85fe61839b53b724";
//
//		QueryTaskMonitorRequest queryTaskMonitorRequest = new QueryTaskMonitorRequest(taskId);
//		QueryTaskMonitorResponse queryTaskMonitorResponse = deployManager.queryTaskMonitor(queryTaskMonitorRequest);
//
//		TaskMonitor taskMonitor = queryTaskMonitorResponse.getTaskMonitor();
//		taskMonitor.getTaskAlertFeature().setAlertWay("FS_CHAT");
//		taskMonitor.getTaskAlertFeature().setReceivers(Collections.singletonList("实时模板平台作业告警群"));
//
//		UpdateTaskMonitorRequest updateTaskMonitorRequest = new UpdateTaskMonitorRequest(taskMonitor);
//		UpdateTaskMonitorResponse updateTaskMonitorResponse = deployManager.updateTaskMonitor(updateTaskMonitorRequest);
//
//		System.out.println(updateTaskMonitorResponse);  // UpdateTaskMonitorResponse(flag=true, msg=OK)


//	@Scheduled(cron = "*/10 * * * * ?")
//	public void test_query_checkpoint_list() throws Exception {
//
//		String taskId = "43f65841eb596c43ffe7676a8a2e4cfe";
//
//		QueryTaskCheckpointRequest queryTaskCheckpointRequest = new QueryTaskCheckpointRequest(taskId);
//
//		QueryTaskCheckpointResponse queryTaskCheckpointResponse = deployManager.queryTaskCheckpoint(queryTaskCheckpointRequest);
//
//
//		queryTaskCheckpointResponse.getLatestCheckpointPath().ifPresent(System.out::println);
//
//		if(queryTaskCheckpointResponse.getLatestCheckpointPath().isPresent()){
//			System.out.println(queryTaskCheckpointResponse.getLatestCheckpointPath().get());
//		}else{
//			System.out.println("no ...");
//		}
//	}


}

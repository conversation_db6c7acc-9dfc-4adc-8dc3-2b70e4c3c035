/*
*  Copyright 2019-2020 <PERSON>
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.dewu.flink.template.lineage.service.dto;

import lombok.Data;
import java.sql.Timestamp;
import java.io.Serializable;

/**
* @website https://el-admin.vip
* @description /
* <AUTHOR>
* @date 2024-05-22
**/
@Data
public class TeAlgoImageLineageDto implements Serializable {

    /** 自增id */
    private Long id;

    /** 镜像名称 */
    private String image;

    /** 镜像标签 */
    private String tag;

    /** 作业id */
    private Long jobId;

    /** 作业名称 */
    private String jobName;

    /** -1:已删除 0:初始化 1:已创建作业/flink未启动 2:部署中/启动中/flink发布中 3:job上线中/flink运行中 4:job启动失败/flink发布失败 5:job下线中/flink下线中 6:job已下线/flink已下线  7:job运行异常/flink运行异常 8:flink运行已结束 */
    private Integer jobStatus;

    /** 部门标签  所属业务域 */
    private String departmentLabel;

    /** job创建人 */
    private String createUserName;

    /** libra作业负责人 */
    private String libraOwers;

    /** 任务创建时间 */
    private String jobCreateTime;

    /** 任务更新时间 */
    private String jobUpdateTime;

    /** 创建时间 */
    private Timestamp createTime;

    /** 修改时间 */
    private Timestamp modifyTime;

    /** 是否删除 */
    private Integer isDel;
}
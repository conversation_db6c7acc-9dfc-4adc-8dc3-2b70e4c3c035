package com.dewu.flink.template.example;

import com.dewu.annotation.AnonymousAccess;
import com.dewu.annotation.Log;
import com.dewu.flink.template.meta.GlobalMetaManager;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


/**
 * <AUTHOR>
 * @date 2022-07-26
 **/
@RestController
@RequiredArgsConstructor
@Api(tags = "job管理")
@RequestMapping("/api/metaTest")
public class MetaManagerCaseController {

	private final GlobalMetaManager globalMetaManager;


	@GetMapping
	@ApiOperation("查询job")
    @PreAuthorize("@el.check('templateEngineJobInfo:list')")
//	@AnonymousAccess
	public ResponseEntity<Object> queryTemplateEngineJobInfo(String name) {

		return new ResponseEntity<>(globalMetaManager.getTransformationMeta(name), HttpStatus.OK);


	}

}

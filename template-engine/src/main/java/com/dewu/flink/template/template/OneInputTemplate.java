package com.dewu.flink.template.template;

import com.dewu.flink.template.transform.SourceTransformation;
import lombok.Data;
import lombok.EqualsAndHashCode;

import static com.dewu.flink.template.translator.generator.TemplateOption.TABLE_ID;

@EqualsAndHashCode(callSuper = true)
@Data
public abstract class OneInputTemplate extends AbstractTemplate {
	private String sourceTableId;
	private String filterExpression;
	private String selectExpression;
	private String sourceCombinedExpression;

	public SourceTransformation generateSourceTransformation() {
		SourceTransformation sourceTransformation = new SourceTransformation();
		sourceTransformation.setConnectConfig(addStartTsConfig(wrapSimpleKeyPairConfig(TABLE_ID.key(), sourceTableId)));
		sourceTransformation.setGlobalMetaManager(getGlobalMetaManager());
		sourceTransformation.setCombinedTransformExpression(sourceCombinedExpression);
		return sourceTransformation;
	}
}

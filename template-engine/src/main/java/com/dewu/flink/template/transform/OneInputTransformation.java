package com.dewu.flink.template.transform;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.annotation.JsonIgnore;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.annotation.JsonIgnoreProperties;

@JsonIgnoreProperties(ignoreUnknown = true)
public abstract class OneInputTransformation extends AbstractTransformation{
    @JsonIgnore
    private Transformation input;
    @JsonIgnore
    private String inputId;

    public Transformation getInput() {
        return input;
    }

    public String getInputId() {
        return inputId;
    }

    public void setInput(Transformation input) {
        this.input = input;
    }

    public void setInputId(String inputId) {
        this.inputId = inputId;
    }
}

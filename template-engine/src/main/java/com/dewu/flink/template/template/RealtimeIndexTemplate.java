package com.dewu.flink.template.template;

import com.dewu.flink.template.meta.metric.service.dto.TeMetricMetadataInfoDto;
import com.dewu.flink.template.transform.*;
import com.dewu.flink.template.translator.generator.ParamTool;
import com.dewu.flink.template.utils.TemplateSqlUtil;
import com.dewu.utils.StringUtils;
import com.google.common.collect.Sets;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.calcite.sql.SqlNode;
import org.apache.calcite.sql.SqlNodeList;
import org.apache.calcite.sql.SqlSelect;
import org.apache.calcite.sql.parser.SqlParseException;
import org.apache.calcite.sql.parser.SqlParser;
import org.apache.flink.api.java.tuple.Tuple2;
import org.apache.flink.configuration.ConfigOption;
import org.apache.flink.table.api.SqlDialect;
import org.apache.flink.util.CollectionUtil;
import org.apache.flink.util.Preconditions;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

import static com.dewu.flink.template.translator.generator.TemplateOption.*;
import static com.dewu.flink.template.utils.SqlParserUtils.getCurrentSqlParserConfig;

@EqualsAndHashCode(callSuper = true)
@Data
public class RealtimeIndexTemplate extends AbstractTemplate {

	private static final Logger logger = LoggerFactory.getLogger(RealtimeIndexTemplate.class);
	private final static Set<ConfigOption<?>> mustOpts = Sets.newHashSet(
		DIM_IDS,
		METRIC_IDS,
		SINK_TABLE_ID
	);

	private String dims;
	private String metrics;
	private String extSql;

	public static RealtimeIndexTemplate apply(Map<String, String> params, Properties jobConfigs, String jobName) {
		ParamTool tool = ParamTool.apply(params);
		logger.info("jobConfigs:" + jobConfigs);
		tool.checkNonNullParam(mustOpts);

		RealtimeIndexTemplate realtimeIndexTemplate = new RealtimeIndexTemplate();

		realtimeIndexTemplate.setDims(tool.getString(DIM_IDS));
		realtimeIndexTemplate.setMetrics(tool.getString(METRIC_IDS));
		realtimeIndexTemplate.setSinkTableId(tool.getString(SINK_TABLE_ID));
		realtimeIndexTemplate.setJobName(jobName);
		realtimeIndexTemplate.setExtSql(tool.getString(EXT_SQL));
		realtimeIndexTemplate.setJobConfigurations(jobConfigs);

		return realtimeIndexTemplate;
	}

	@Override
	public String generateTransformationStr() throws IOException {
		List<String> dimNames = new ArrayList<>();
		List<String> metricIDs = Arrays.asList(metrics.split(","));
		List<String> metricNames = new ArrayList<>();
		List<TeMetricMetadataInfoDto> metrics =
			metricIDs.stream().map(metricId -> {
				TeMetricMetadataInfoDto metric = getGlobalMetaManager().getMetric(Long.parseLong(metricId));
				String metricDims = metric.getTeMetricGroupMetadataInfoDto().getTeCommonAttributesIds2();
				if (!arrEqual(metricDims, dims)) {
					throw new RuntimeException("dim not matched when create index template with " + dims + " but found " + metricDims + " for metric " + metric.getMetricName());
				}
				return metric;
			}).collect(Collectors.toList());

		Map<Tuple2<Long, String>, List<TeMetricMetadataInfoDto>> groupedMetrics =
			metrics.stream().collect(Collectors.groupingBy(metric -> new Tuple2<>(metric.getTeMetricGroupMetadataInfoDto().getReDataSourceMetadataId(), metric.getTeMetricGroupMetadataInfoDto().getMetricGroupName())));
		Map<Transformation, String> sourceTransformations = new HashMap<>();
		Map<String, Transformation> regSources = new HashMap<>();
		for (Map.Entry<Tuple2<Long, String>, List<TeMetricMetadataInfoDto>> entry : groupedMetrics.entrySet()) {
			Tuple2<Long, String> k = entry.getKey();
			List<TeMetricMetadataInfoDto> v = entry.getValue();

			List<String> requiredMetrics = new ArrayList<>();
			for (TeMetricMetadataInfoDto teMetricMetadataInfoDto : v) {
				requiredMetrics.add(teMetricMetadataInfoDto.getMetricName());
				if (CollectionUtil.isNullOrEmpty(dimNames)) {
					dimNames.addAll(Arrays.stream(teMetricMetadataInfoDto.getTeMetricGroupMetadataInfoDto().getTeCommonAttributesIds().split(",")).collect(Collectors.toList()));
				}
			}
			metricNames.addAll(requiredMetrics);
//			String selectExpr = dimNames + "," + String.join(",", requiredMetrics);
			List<String> formattedMetrics = requiredMetrics.stream().map(metric -> "realtime_" + metric).collect(Collectors.toList());
			List<String> metricNameTransform = requiredMetrics.stream().map(metric -> TemplateSqlUtil.wrapWithBackQuote(metric) + " as realtime_" + metric).collect(Collectors.toList());
			String finalTransExpr = String.join(",", metricNameTransform) + "," + TemplateSqlUtil.formatSelectField(String.join(",", dimNames));
			String finalSelectExpr = String.join(",", formattedMetrics);
			String groupName = k.f1;
			String tableId = String.valueOf(k.f0);
			Transformation sourceTransformation;
			if (regSources.containsKey(tableId)) {
				sourceTransformation = regSources.get(tableId);
			} else {
				SourceTransformation tmp = new SourceTransformation();
				tmp.setConnectConfig(addStartTsConfig(wrapSimpleKeyPairConfig(TABLE_ID.key(), tableId)));
//			sourceTransformation.setCombinedTransformExpression(String.format("SELECT %s FROM ( SELECT *,ROW_NUMBER() OVER (PARTITION BY %s ORDER BY proctime() desc) AS row_num FROM $.baseTable) WHERE row_num <= 1", selectExpr, dimNames));
				tmp.setCombinedTransformExpression(String.format("select `ts`,`dim_value`,`job`,`metric_value`,`track_id`,`unique_key`,`metric_name`,`ext`,`dim_name`, %s from $.baseTable", buildParseExpr(requiredMetrics, dimNames)));
				tmp.setGlobalMetaManager(getGlobalMetaManager());
				TopnTransformation finalTransform = new TopnTransformation();
				finalTransform.setN(1);
				finalTransform.setOrderByExpression(" proctime() desc ");
				finalTransform.setSelectExpression("metric_name, " + finalTransExpr);
				finalTransform.setGlobalMetaManager(getGlobalMetaManager());
				finalTransform.setInput(tmp);
				finalTransform.setPartitionExpression(TemplateSqlUtil.formatSelectField("metric_name, " + String.join(",", dimNames)));
				regSources.put(tableId, finalTransform);
				sourceTransformation = finalTransform;
			}
			String filterExpr = " metric_name ='" + groupName + "' ";
			Transformation transform = addCalcExpression("*", filterExpr, sourceTransformation);
			if (!transform.validate()) {
				throw new RuntimeException("realtime index template source validate failed.");
			}
			sourceTransformations.put(transform, finalSelectExpr);
		}

		String joinCondition = buildJoinConditionExpr(dimNames);
		Map<Transformation, String> joins = new HashMap<>();

		String baseSelect = buildSelectExpr(dimNames);
		Optional<Transformation> transformation;
		if (sourceTransformations.size() > 1) {
			logger.info("begin to build join chain for realtime index template.");
			transformation = sourceTransformations.keySet().stream().reduce((transformation1, transformation2) -> {
				JoinTransformation joinTransformation = new JoinTransformation();
				joinTransformation.setJoinType("inner");
				String leftSelect = "";
				if (joins.containsKey(transformation1)) {
					leftSelect = joins.get(transformation1);
				} else {
					leftSelect = sourceTransformations.get(transformation1);
				}
				String rightSelect = "";
				if (joins.containsKey(transformation2)) {
					rightSelect = joins.get(transformation2);
				} else {
					rightSelect = sourceTransformations.get(transformation2);
				}
				String selectExpr = leftSelect + "," + rightSelect;
				joinTransformation.setIsDeduplicateByJoinKey(false);
				joinTransformation.setJoinConditionExpression(joinCondition);
				joinTransformation.setSelectExpression(TemplateSqlUtil.formatSelectField(TemplateSqlUtil.formatSelectField(baseSelect + "," + selectExpr)));
				joinTransformation.setLeft(transformation1);
				joinTransformation.setRight(transformation2);
				if (!joinTransformation.validate()) {
					throw new RuntimeException("JoinTransformation 验证失败 : " + joinTransformation);
				}
				joins.put(joinTransformation, selectExpr);
				return joinTransformation;
			});

		} else {
			transformation = sourceTransformations.keySet().stream().findFirst();
		}

		if (!transformation.isPresent()) {
			throw new RuntimeException("realtime index template source gen a empty transform.");
		}

		List<String> finalSelects = metricNames.stream().map(metric -> "realtime_" + metric).collect(Collectors.toList());
		GroupByTransformation groupByTransformation = new GroupByTransformation();
		groupByTransformation.setInput(transformation.get());
		groupByTransformation.setGroupByExpression(String.join(",", dimNames));
		String finalNonNullSelect = String.join(",", buildNonNullSelectExpr(finalSelects)) + "," + String.join(",", dimNames);
		groupByTransformation.setSelectExpression(finalNonNullSelect);

		if (StringUtils.isNotBlank(extSql)) {
			// 解析配置
			SqlParser.Config parserConfig = getCurrentSqlParserConfig(SqlDialect.DEFAULT);
			SqlParser parser = SqlParser.create(extSql.replace("$.baseTable", "testTable"), parserConfig);

			List<String> finalDims = new ArrayList<>();
			try {
				SqlNode sqlNode = parser.parseQuery();
				if (sqlNode instanceof SqlSelect) {
					SqlNodeList selectList = ((SqlSelect) sqlNode).getSelectList();
					for (SqlNode sexpr : selectList.getList()) {
						for (String dim : dimNames) {
							if (wrapEmptyChar(sexpr.toString()).contains(" `" + dim + "` ")) {
								String[] tmp = sexpr.toString().trim().split(" ");
								finalDims.add(tmp[tmp.length - 1]);
							}
						}
					}
				} else {
					throw new RuntimeException("Only enable select stmt in index template ext-sql.");
				}
				setSinkPrimaryKeys(String.join(",", finalDims));
			} catch (SqlParseException e) {
				throw new RuntimeException("not a valid select stmt in index template  ext-sql.");
			}
		} else {
			setSinkPrimaryKeys(dimNames.get(0));
		}
		Transformation sinkTransformation = generateSinkTransformation(groupByTransformation, extSql);
		if (!sinkTransformation.validate()) {
			return "SinkTransformation 验证失败";
		}

		return buildStrWithSink(sinkTransformation);
	}

	private List<String> buildNonNullSelectExpr(List<String> expr) {
		List<String> rst = new ArrayList<>();
		for (String field : expr) {
			rst.add("LAST_VALUE(`" + field + "`) as `" + field + "`");
		}
		return rst;
	}

	private static String wrapEmptyChar(String str) {
		return " " + str + " ";
	}

	private String buildParseExpr(List<String> requiredMetrics, List<String> dimNames) {
		List<String> selectColumns = new ArrayList<>();
		requiredMetrics.forEach(requiredMetric -> {
			selectColumns.add(String.format("JsonValue(metric_value,'%s') as `%s`", requiredMetric, requiredMetric));
		});
		dimNames.forEach(dim -> {
			selectColumns.add(String.format("JsonValue(dim_value,'%s') as `%s`", dim, dim));
		});
		return String.join(",", selectColumns);
	}

	private String buildParseExpr(String dimNames) {
		List<String> selectColumns = new ArrayList<>();
		Arrays.stream(dimNames.split(",")).forEach(dim -> {
			selectColumns.add(String.format("JsonValue(dim_value,'%s') as `%s`", dim, dim));
		});
		return String.join(",", selectColumns);
	}

	private String buildJoinConditionExpr(List<String> dimNames) {
		Preconditions.checkArgument(!CollectionUtil.isNullOrEmpty(dimNames), "dimNames shall not be empty when building join condition for each metricGroups");
		return dimNames.stream().map(dim -> "tl.`" + dim + "` = tr.`" + dim + "`").collect(Collectors.joining(" and "));
	}

	private String buildSelectExpr(List<String> dimNames) {
		Preconditions.checkArgument(!CollectionUtil.isNullOrEmpty(dimNames), "dimNames shall not be empty when building join select for each metricGroups");
		return dimNames.stream().map(dim -> "tl.`" + dim + "`").collect(Collectors.joining(","));
	}

	/**
	 * a and b should not be blank
	 *
	 * @param a
	 * @param b
	 * @return
	 */
	public boolean arrEqual(String a, String b) {
		if (StringUtils.isBlank(a) || StringUtils.isBlank(b)) {
			return false;
		}
		List<String> aArr = Arrays.asList(a.split(","));
		List<String> bArr = Arrays.asList(b.split(","));
		return aArr.equals(bArr);
	}

	public static void main(String[] args) {
		String extSql = "select c as t,a as g,fisha as log, sdf, b from $.baseTable";
		String dimNames = "a,b,c";
		if (StringUtils.isNotBlank(extSql)) {
			// 解析配置
			SqlParser.Config parserConfig = getCurrentSqlParserConfig(SqlDialect.DEFAULT);
			SqlParser parser = SqlParser.create(extSql, parserConfig);

			List<String> finalDims = new ArrayList<>();
			try {
				SqlNode sqlNode = parser.parseQuery();
				if (sqlNode instanceof SqlSelect) {
					SqlNodeList selectList = ((SqlSelect) sqlNode).getSelectList();
					for (SqlNode sexpr : selectList.getList()) {
						for (String dim : dimNames.split(",")) {
							if (wrapEmptyChar(sexpr.toString()).contains(" " + dim + " ") || wrapEmptyChar(sexpr.toString()).contains(" `" + dim + "` ")) {
								String[] tmp = sexpr.toString().trim().split(" ");
								finalDims.add(tmp[tmp.length - 1]);
							}
						}
					}
				} else {
					throw new RuntimeException("Only enable select stmt in index template ext-sql.");
				}
				System.out.println("exchanged");
			} catch (SqlParseException e) {
				throw new RuntimeException("not a valid select stmt in index template  ext-sql.");
			}
		} else {
			System.out.println("normal");
		}
	}

}

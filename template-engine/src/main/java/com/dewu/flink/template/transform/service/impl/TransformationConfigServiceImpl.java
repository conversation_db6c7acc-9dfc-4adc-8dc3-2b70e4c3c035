package com.dewu.flink.template.transform.service.impl;

import com.dewu.flink.template.transform.domain.TransformationConfig;
import com.dewu.flink.template.transform.repository.TransformationConfigRepository;
import com.dewu.flink.template.transform.service.TransformationConfigService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * TransformationConfigServiceImpl for web api.
 */
@Service
@RequiredArgsConstructor
public class TransformationConfigServiceImpl implements TransformationConfigService {
	private final TransformationConfigRepository transformationConfigRepository;


	@Override
	public List<TransformationConfig> findAllByTransformationName(String name) {
		return transformationConfigRepository.findAllByTransformationName(name);
	}

	@Override
	public List<TransformationConfig> findAllByTransformationNameAndType(String name, String type) {
		return transformationConfigRepository.findAllByTransformationNameAndType(name,type);
	}
}

package com.dewu.flink.template.translator.generator;

import org.apache.flink.configuration.ConfigOption;
import org.apache.flink.util.Preconditions;
import org.apache.flink.util.StringUtils;

import java.util.Map;
import java.util.Set;

/**
 * @Description: XXX
 * @author: zhiping.lin
 * @date: 2022/8/17
 */
public class ParamTool {
	Map<String, String> innerMap;

	public ParamTool(Map<String, String> innerMap) {
		this.innerMap = innerMap;
	}

	public static ParamTool apply(Map<String, String> innerMap) {
		return new ParamTool(innerMap);
	}

	public String getString(ConfigOption<String> opt) {
		return innerMap.getOrDefault(opt.key(), opt.defaultValue());
	}

	public Boolean getBoolean(ConfigOption<Boolean> opt) {
		return innerMap.containsKey(opt.key()) ? Boolean.valueOf(innerMap.get(opt.key())) : opt.defaultValue();
	}

	public Long getLong(ConfigOption<Long> opt) {
		return innerMap.containsKey(opt.key()) ? Long.valueOf(innerMap.get(opt.key())) : opt.defaultValue();
	}

	public Integer getInteger(ConfigOption<Integer> opt) {
		return innerMap.containsKey(opt.key()) ? Integer.valueOf(innerMap.get(opt.key())) : opt.defaultValue();
	}

	public Double getDouble(ConfigOption<Double> opt) {
		return innerMap.containsKey(opt.key()) ? Double.valueOf(innerMap.get(opt.key())) : opt.defaultValue();
	}

	public void checkNonNullParam(Set<ConfigOption<?>> opts) {
		opts.forEach(opt ->
			Preconditions.checkArgument(!StringUtils.isNullOrWhitespaceOnly(innerMap.get(opt.key())), "missing param : " + opt.key())
		);
	}


}

package com.dewu.flink.template.deploy;

import com.dewu.flink.template.common.Request;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Author: WangLin
 * Date: 2022/7/24 下午2:25
 * Description: 上线作业请求参数
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class SubmitTaskRequest implements Request {
	/**
	 * 操作者
	 */
	private String operator;

	/**
	 * 作业id
	 */
	private String taskId;

	/**
	 * 指定启动的checkpoint地址
	 */
	private String checkpointUrl;

	public SubmitTaskRequest(String taskId) {
		this.taskId = taskId;
	}
}

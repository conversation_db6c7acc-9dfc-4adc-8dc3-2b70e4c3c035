package com.dewu.flink.template.template.b2s.converbean;

import com.dewu.flink.template.utils.JsonUtil;
import lombok.EqualsAndHashCode;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.core.JsonProcessingException;

import java.util.HashMap;
import java.util.Map;

/**
 * @Description: XXX
 * @author: zhiping.lin
 * @date: 2022/8/24
 */
@EqualsAndHashCode(callSuper = true)
public class GroupByBean extends AbstractTransformBean {
	private String groupByExpression;
	private String havingExpression;
	private String filterExpression;

	public GroupByBean(String groupByExpression, String havingExpression, String filterExpression) {
		this.setId(id_id.getAndIncrement());
		this.name = "GroupByTransformation";
		this.groupByExpression = groupByExpression;
		this.havingExpression = havingExpression;
		this.filterExpression = filterExpression;
	}

	public String getGroupByExpression() {
		return groupByExpression;
	}

	public void setGroupByExpression(String groupByExpression) {
		this.groupByExpression = groupByExpression;
	}

	public String getHavingExpression() {
		return havingExpression;
	}

	public void setHavingExpression(String havingExpression) {
		this.havingExpression = havingExpression;
	}

	public String getFilterExpression() {
		return filterExpression;
	}

	public void setFilterExpression(String filterExpression) {
		this.filterExpression = filterExpression;
	}

	@Override
	public String wrapTransformation() throws JsonProcessingException {
		Map<String, Object> transformation = new HashMap<>();
		transformation.put("groupByExpression", groupByExpression);
		transformation.put("selectExpression", selectExpression);
		transformation.put("filterExpression", filterExpression);
		transformation.put("havingExpression", havingExpression);
		return JsonUtil.writeString(transformation);
	}

	@Override
	public String toString() {
		try {
			return getString();
		} catch (JsonProcessingException e) {
			e.printStackTrace();
		}
		return null;
	}
}

package com.dewu.flink.template.template.b2s;

import com.dewu.flink.template.chaos.rules.service.dto.ChaosRulesDto;
import com.dewu.flink.template.config.SchemaConfig;
import com.dewu.flink.template.job.service.dto.TeJobDataDrillDto;
import com.dewu.flink.template.meta.GlobalMetaBaseDomain;
import com.dewu.flink.template.template.AbstractTemplate;
import com.dewu.flink.template.transform.*;
import com.dewu.flink.template.translator.generator.DataSourceType;
import com.dewu.flink.template.translator.generator.ParamTool;
import com.dewu.flink.template.utils.JsonUtil;
import com.dewu.flink.template.utils.SqlParserUtils;
import com.dewu.flink.template.utils.SqlScriptsRender;
import com.dewu.utils.StringUtils;
import com.google.common.collect.Sets;
import lombok.Data;
import org.apache.calcite.sql.SqlInsert;
import org.apache.calcite.sql.SqlNode;
import org.apache.calcite.sql.SqlSelect;
import org.apache.calcite.sql.parser.SqlParseException;
import org.apache.calcite.sql.parser.SqlParser;
import org.apache.calcite.util.Pair;
import org.apache.commons.collections.MapUtils;
import org.apache.flink.api.java.tuple.Tuple2;
import org.apache.flink.configuration.ConfigOption;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.core.JsonProcessingException;
import org.apache.flink.shaded.netty4.io.netty.util.internal.StringUtil;
import org.apache.flink.sql.parser.ddl.SqlCreateView;
import org.apache.flink.table.api.SqlDialect;
import org.apache.flink.util.CollectionUtil;
import org.apache.flink.util.Preconditions;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;
import java.util.stream.Collectors;

import static com.dewu.flink.template.translator.generator.ConnectorManager.isOfflineDataSources;
import static com.dewu.flink.template.translator.generator.Constants.VIEW_PREFIX;
import static com.dewu.flink.template.translator.generator.TemplateOption.*;
import static com.dewu.flink.template.utils.Constant.*;
import static com.dewu.flink.template.utils.SqlParserUtils.*;
import static com.dewu.flink.template.utils.TemplateSqlUtil.wrapWithBackQuote;

@Data
public class SimpleFlinkSqlTemplate extends AbstractTemplate {
	private static final Logger logger = LoggerFactory.getLogger(SimpleFlinkSqlTemplate.class);

	private String sql;
	private String sourceTableIds;
	private String extSelectExpr;
	private String selectExpr;

	// 延迟source相关配置
	int delaySeconds = 5;
	private List<String> delayTables = new ArrayList<>();

	private String dedupKey = "";

	// source分类
	private List<String> offlineTables = new ArrayList<>();
	private List<String> streamTables = new ArrayList<>();

	// 需要以binary模式进行解析的表
	List<String> binaryTables = new ArrayList<>();
	// binlog是否需要old字段, 默认false
	boolean needOld = false;
	// delayMode是否启用聚合模式
	boolean needAggDelay = false;
	boolean needWaterMark = false;
	// 以binlog模式进行解析的表
	List<String> binlogTables = new ArrayList<>();

	// source去重配置
	boolean globalSourceDep = true;
	List<String> deDupTables = new ArrayList<>();

	String dimTimeTableId;
	String dimTimeTable;

	// auto chaos
	String chaosDataTableId;
	String chaosDataTable;

	Map<String, String> sinkSqls = new HashMap<>();
	List<String> commSqls = new ArrayList<>();

	Map<String, List<String>> sourceMap = new HashMap<>();
	Map<String, GlobalMetaBaseDomain> domains = new HashMap<>();
	LinkedHashMap<String, Transformation> sourceTransforms = new LinkedHashMap<>();
	LinkedHashSet<String> sourceTableSet = new LinkedHashSet<>();
	boolean isRawFlinkSql = false;

	private final static Set<ConfigOption<?>> mustOpts = Sets.newHashSet(
		SOURCE_TABLE_IDS,
		SINK_TABLE_IDS,
		SQL
	);
//	Map<String, String> sinkPrimaryKey;

	public static SimpleFlinkSqlTemplate apply(Map<String, String> params, Properties jobConfigs, String jobName, String jobType) {
		ParamTool tool = ParamTool.apply(params);
		logger.info("jobConfigs:" + jobConfigs);
		tool.checkNonNullParam(mustOpts);

		SimpleFlinkSqlTemplate rawSqlTemplate = new SimpleFlinkSqlTemplate();
		rawSqlTemplate.setJobType(jobType);
		rawSqlTemplate.setJobName(jobName);
		rawSqlTemplate.setJobId(tool.getString(TEMPLATE_JOB_ID));
		rawSqlTemplate.setSourceTableIds(tool.getString(SOURCE_TABLE_IDS));
		rawSqlTemplate.setSql(formatSingleStatement(tool.getString(SQL)));
//		rawSqlTemplate.setSinkTableIds(tool.getString(SINK_TABLE_IDS));
		// only support single sink.
		rawSqlTemplate.setSinkTableId(tool.getString(SINK_TABLE_IDS));
		rawSqlTemplate.setExtSelectExpr(tool.getString(EXT_SELECT_STR));
		rawSqlTemplate.setSelectExpr(tool.getString(SELECT_EXPR));
		rawSqlTemplate.setSinkPrimaryKeys(StringUtils.isNotBlank(tool.getString(SINK_SCHEMA_PRIMARY_KEY)) ? tool.getString(SINK_SCHEMA_PRIMARY_KEY).replaceAll("\\s+", "") : "");
		rawSqlTemplate.setJobConfigurations(jobConfigs);
		rawSqlTemplate.setTemplateMode(tool.getString(SQL_TEMPLATE_MODE));
		try {
			Map viewNames = JsonUtil.parseObject(tool.getString(TABLE_VIEW_NAME), Map.class);
			if (!MapUtils.isEmpty(viewNames)) {
				rawSqlTemplate.getTable2ViewNames().putAll(viewNames);
			}
			Map ddlMappings = JsonUtil.parseObject(tool.getString(DDL_MAPPINGS), Map.class);
			if (!MapUtils.isEmpty(viewNames)) {
				rawSqlTemplate.getDdlMappings().putAll(ddlMappings);
			}
		} catch (JsonProcessingException e) {
			throw new RuntimeException("TABLE_VIEW_NAME parse failed: \n" + tool.getString(TABLE_VIEW_NAME));
		}

		return rawSqlTemplate;
	}

	public static String formatSingleStatement(String sql) {
		sql = sql.trim();
		while (sql.endsWith(";")) {
			sql = sql.substring(0, sql.length() - 1).trim();
		}
		return sql;
	}

	@Override
	public String generateTransformationStr() throws Exception {
		long startTime = System.currentTimeMillis();

		// 1. prepare
		this.prepareGeneration();
		// 2. initial connector configs
		this.initConnectorMeta();
		// 3. init source
		this.initSource();

		// 4. deal user-defined sql with raw flink sql transform

		RawFlinkSqlTransformation rawFlinkSqlTransformation = new RawFlinkSqlTransformation();
		rawFlinkSqlTransformation.setInputs(sourceTransforms);

		// sql 预处理
		if ("rule".equals(templateMode) || "dqc".equals(templateMode)) {
			// 调整sql兼容通用模式
			if (DYNAMIC_RULE_SQL_REPLACING_STR.equals(sql)) {
				Preconditions.checkState(sourceTableSet.size() == 1, "empty sql only support in rule template with one source.");
				sql = " SELECT * FROM " + tableMetaInverse.get(sourceTableIds.trim());
			}
			Preconditions.checkState(getSinkTableId().split(",").length == 1, String.format("Dynamic rule template only support 1 sink, but we found %s", getSinkTableId()));
			sql = "INSERT INTO `" + tableMetaInverse.get(getSinkTableId()) + "` " + sql;
			if (StringUtils.isBlank(selectExpr)) {
				selectExpr = " * ";
			}
		}

		logger.info(sql);
		logger.info("meta : " + tableMeta);
		logger.info("sinkPrimaryKey : " + getSinkPrimaryKeys());
		Boolean isDataDrill = teJobDataDrillDto.getIsDataDrill();
		Map<String, Tuple2<String, String>> tableNameWithLastViewAndSql = new HashMap<>();

		String sqlView = "";
		if ("libra".equals(templateMode)) {
			List<String> subSqls = SqlScriptsRender.splitSql(sql).stream().map(SqlParserUtils::inlineStr).collect(Collectors.toList());
			for (String s : subSqls) {
				SqlParser.Config parserConfig = getCurrentSqlParserConfig(SqlDialect.DEFAULT);
				SqlParser parser = SqlParser.create(s, parserConfig);
				try {
					SqlNode sqlNode = parser.parseStmt();
					if (sqlNode instanceof SqlInsert) {
						String selectSql = escapeUnicode(extractSelectExpr((SqlSelect) ((SqlInsert) sqlNode).getSource(), extSelectExpr));
						String table = ((SqlInsert) sqlNode).getTargetTable().toString();
						sinkSqls.put(table, selectSql);
					} else if (sqlNode instanceof SqlCreateView) {
						sqlView = ((SqlCreateView) sqlNode).getViewName().toString();
						if (isDataDrill) {
							String cutCreateViewSql = sqlNode.toString().replaceAll("FOR SYSTEM_TIME AS OF `PROCTIME`\\(\\)", "");
							commSqls.add(inlineStr(cutCreateViewSql));
						} else {
							commSqls.add(s);
						}
					} else {
						commSqls.add(s);
					}
				} catch (SqlParseException e) {
					throw new RuntimeException(String.format("Not a valid sql [%s] in [%s]  ", s, sql));
				}

			}
			rawFlinkSqlTransformation.setSqls(commSqls);
			rawFlinkSqlTransformation.setLastView(sqlView);
		} else {
			sqlView = VIEW_PREFIX + UUID.randomUUID().toString().replace("-", "");
			// 创建解析器
			List<String> subSqls = SqlScriptsRender.splitSql(sql).stream().map(SqlParserUtils::inlineStr).collect(Collectors.toList());
			int index = 0;
			boolean containsInsert = false;
			for (String subSql : subSqls) {
				SqlParser.Config parserConfig = getCurrentSqlParserConfig(SqlDialect.DEFAULT);
				SqlParser parser = SqlParser.create(subSql, parserConfig);

				// 解析sql
				// which operator shall be extracted?
				// a. dim join -> to be translated
				// b. source -> gen schema, gen deDup => no need
				// c. sink -> gen schema?             => no need
				SqlNode sqlNode = parser.parseStmt();
				if (sqlNode instanceof SqlInsert) {
					String selectSql = extractSelectExpr((SqlSelect) ((SqlInsert) sqlNode).getSource(), extSelectExpr);
					String tableName = ((SqlInsert) sqlNode).getTargetTable().toString();
					// todo : rebuild dim join replacement. unstable.
					selectSql = escapeUnicode(transFlinkSql(selectSql, isDataDrill));
					String lastView = sqlView + "_" + index++;
					String insertSelectSql = "create view " + lastView + " as " + selectSql;
					commSqls.add(insertSelectSql);
					sinkSqls.put(tableName, "select * from " + lastView);
					table2ViewNames.put(tableName, tableName);
					containsInsert = true;
//					commSqls.add(insertSelectSql);
//					tableNameWithLastViewAndSql.put(tableName, new Tuple2<>(lastView, selectSql));
					//todo tablename
				} else {
					String commonSql = sqlNode.toString();
					commonSql = escapeUnicode(transFlinkSql(commonSql, isDataDrill));
					commSqls.add(inlineStr(commonSql));
				}
			}
			if (!containsInsert) {
				throw new RuntimeException("Not a valid sqlInsert : " + sql);
			}
			// iv. set-in new sql
			rawFlinkSqlTransformation.setSqls(commSqls);
			rawFlinkSqlTransformation.setLastView(sqlView + "_" + --index);
		}


		// 6. sql 后置处理
		// drt only sup one sink.
		Transformation postTransform;
		if ("rule".equals(templateMode)) {
			// *, dynamic_rule_wrapped_msg, FLK_DYNAMIC_RULE_TAG
			// where FLK_DYNAMIC_RULE_TAG <> DYNAMIC_RULE_FILTER_FLAG
			postTransform = addDynamicRuleTransform(rawFlinkSqlTransformation, "MapKeyFetcher(FLK_DYNAMIC_RULE_TAG) as FLK_DYNAMIC_RULE_TAG");
			postTransform = addCalcExpression(selectExpr, "FLK_DYNAMIC_RULE_TAG <> '' and FLK_DYNAMIC_RULE_TAG <> '{}'", postTransform);
			// clear sinkSqls for dag changed
			sinkSqls.clear();
		} else if ("dqc".equals(templateMode)) {
			postTransform = addDynamicRuleTransform(rawFlinkSqlTransformation, "dynamic_rule_wrapped_msg, FLK_DYNAMIC_RULE_TAG");
			postTransform = addCalcExpression("'" + jobName + "' as " + FLK_DYNAMIC_DQC_JOB_NAME + ",proctime() as " + FLK_DYNAMIC_DQC_TS + ",dynamic_rule_wrapped_msg as " + FLK_DYNAMIC_DQC_MSG + ", FLK_DYNAMIC_RULE_TAG as " + FLK_DYNAMIC_DQC_TAG + ", '" + String.join(",", tableMeta.keySet()) + "' AS `TABLE` ", "FLK_DYNAMIC_RULE_TAG <> '' and FLK_DYNAMIC_RULE_TAG <> '{}'", postTransform);
			// clear sinkSqls for dag changed
			sinkSqls.clear();
		} else {
			postTransform = rawFlinkSqlTransformation;
		}

		// 7. build sink.
		Transformation sinkTransformation = generateSinkTransformation(postTransform);
		long endTIme = System.currentTimeMillis();
		logger.info("build transformations takes about " + (endTIme - startTime) + " ms.");
		return buildStrWithSink(sinkTransformation);
	}

	private String transFlinkSql(String selectSql, boolean isDataDrill) {
		if (isRawFlinkSql) {
			return selectSql;
		}
		// i. lookup join replacement
		for (String offlineTable : offlineTables) {
			if (!isDataDrill) {
				selectSql = selectSql.replace("JOIN `" + offlineTable + "`", "JOIN `" + offlineTable + "` FOR SYSTEM_TIME AS OF PROCTIME() ");
			}
		}
		// ii. enable delay source
		if (delaySeconds > 0 && !isDataDrill) {
			for (String delayTable : delayTables) {
				if (!needAggDelay) {
					selectSql = selectSql.replace("`" + delayTable + "`", " (select tl.* from ( SELECT *, cast(UNIX_TIMESTAMP() as bigint)+" + delaySeconds + " as FLK_DELAY_ts FROM `" + delayTable + "`) tl inner join `" + dimTimeTable + "` tr on tl.FLK_DELAY_ts = tr.ts )");
				}
			}
		}
		// iii. replace stream source with query view. for we will build stream source with some transformations and then create a view to instead.
		for (String streamTable : streamTables) {
			selectSql = selectSql.replace("`" + streamTable + "`", "`" + VIEW_PREFIX + streamTable + "`");
		}
		return selectSql;
	}

	public void initConnectorMeta() {
		// 1. parse sources
		for (String id : sourceTableSet) {
			GlobalMetaBaseDomain globalMetaBaseDomain = globalMetaManager.getTableMetaByTableId(Long.valueOf(id));
			domains.put(id, globalMetaBaseDomain);
			DataSourceType dataSourceType = DataSourceType.numOf(globalMetaBaseDomain.getReDataSourceDto().getDataSourceType());
			if (isOfflineDataSources(dataSourceType)) {
				offlineTables.add(globalMetaBaseDomain.getReDataSourceMetadataDto().getTableName());
			} else {
				streamTables.add(globalMetaBaseDomain.getReDataSourceMetadataDto().getTableName());
			}
			tableMeta.put(globalMetaBaseDomain.getReDataSourceMetadataDto().getTableName(), id);
		}
		// 2. parse sinks. sink shall be only one now
		String[] sinkIDs = getSinkTableId().split(",");
		String[] primaryKeys = getSinkPrimaryKeys().split(";");
		Preconditions.checkState(StringUtils.isEmpty(getSinkPrimaryKeys()) || sinkIDs.length == primaryKeys.length, String.format("primary-keys[%s] num shall eq to sink-table[%s] num when it is configured.", getSinkPrimaryKeys(), getSinkTableId()));
		for (int i = 0; i < sinkIDs.length; i++) {
			Pair<String, String> nameMapping = globalMetaManager.getSimpleTableMetaIdPairByTableId(Long.valueOf(sinkIDs[i]));
			tableMeta.put(nameMapping.left, nameMapping.right);
			if (!StringUtils.isEmpty(getSinkPrimaryKeys())) {
				sinkPriKeyMeta.put(sinkIDs[i], primaryKeys[i]);
			} else {
				sinkPriKeyMeta.put(sinkIDs[i], "");
			}
		}
		tableMetaInverse = tableMeta.inverse();
	}

	private void initSource() {
		sourceTableSet.forEach(source -> {
			SourceTransformation sourceTransformation;
			String tableName = tableMetaInverse.get(source);
			if (streamTables.contains(tableName)) {
				sourceTransformation = new SourceTransformation();
				sourceTransformation.setConnectConfig(addStartTsConfig(wrapSimpleKeyPairConfig(TABLE_ID.key(), source)));
			} else {
				sourceTransformation = new SourceTransformation();
				sourceTransformation.setConnectConfig(wrapSimpleKeyPairConfig(TABLE_ID.key(), source));
			}
			sourceTransformation.setGlobalMetaManager(getGlobalMetaManager());
			sourceTransformation.setBinaryTables(binaryTables);
			sourceTransformation.setDelayTables(delayTables);
			sourceTransformation.setBinlogTables(binlogTables);
			sourceTransformation.setStreamTables(streamTables);
			sourceTransformation.setOfflineTables(offlineTables);
			sourceTransformation.setBinlogTables(binlogTables);
			sourceTransformation.setContext(jobConfigurations);
			sourceTransformation.setDelaySeconds(delaySeconds);
			sourceTransformation.setNeedAggDelay(needAggDelay);
			sourceTransformation.setNeedWaterMark(needWaterMark);
			sourceTransformation.setGlobalSourceDep(globalSourceDep);
			sourceTransformation.setNeedOld(needOld);
			String preFilterKey = "dedup.pre.filter." + tableName;
			String filter = jobConfigurations.containsKey(preFilterKey) && StringUtils.isNotEmpty(String.valueOf(jobConfigurations.get(preFilterKey))) ? String.valueOf(jobConfigurations.get(preFilterKey)) : "";
			sourceTransformation.setPreCombinedFilterForBinlog(filter);
			sourceTransformation.setChaosRuleInfo(chaosRules);
			sourceTransformation.setDeDupTables(deDupTables);
			sourceTransformation.setViewName(wrapWithBackQuote(VIEW_PREFIX + tableName));
			String finalTableName = table2ViewNames == null || StringUtil.isNullOrEmpty(table2ViewNames.get(tableName)) ? tableName : table2ViewNames.get(tableName);
			sourceTransformation.setRawSQL(ddlMappings.get(finalTableName));
			sourceTransformation.setTableName(finalTableName);
			sourceTransformation.setDataDrill(teJobDataDrillDto.getIsDataDrill());
			if (teJobDataDrillDto.getIsDataDrill()) {
				sourceTransformation.setDrillData(sourceMap.get(tableName));
			}
			if (!sourceTransformation.validate()) {
				throw new RuntimeException("source transformation validate failed : " + sourceTransformation);
			}
			if (!getTeJobDataDrillDto().getIsDataDrill() || !source.equals(dimTimeTableId)) {
				sourceTransforms.put(tableName, sourceTransformation);
			}
		});

//		if (chaosRules != null) {
//			SourceTransformation chaosSource = (SourceTransformation) sourceTransforms.get(chaosDataTable);
//			Set<String> sourceTableNames = sourceTransforms.keySet();
//			for (String table : sourceTableNames) {
//				sourceTransforms.put(table, transformChaosSource((SourceTransformation) sourceTransforms.get(table), chaosSource, chaosRules));
//			}
//		}
	}

	private AbstractTransformation transformChaosSource(SourceTransformation sourceTransformation, SourceTransformation chaosSource, ChaosRulesDto chaosRules) {
		return new DataMockedSourceTransformation(sourceTransformation, chaosSource, chaosRules.getDatagenRuleType(), chaosRules.getDatagenRuleDetail());
	}

	private boolean configExists(String configKey) {
		return jobConfigurations.containsKey(configKey)
			&& Objects.nonNull(jobConfigurations.get(configKey))
			&& StringUtils.isNotBlank(jobConfigurations.get(configKey).toString());
	}

	private void prepareGlobalParams() throws JsonProcessingException {
		if (configExists("delay.tables"))
			delayTables.addAll(Arrays.asList(jobConfigurations.get("delay.tables").toString().split(";")));
		if (configExists("delay.tables.seconds"))
			delaySeconds = Integer.parseInt(jobConfigurations.get("delay.tables.seconds").toString());
		if (configExists("binary.tables"))
			binaryTables.addAll(Arrays.asList(jobConfigurations.get("binary.tables").toString().split(";")));
		if (configExists("binlog.tables"))
			binlogTables.addAll(Arrays.asList(jobConfigurations.get("binlog.tables").toString().split(";")));
		if (configExists("deDup.tables"))
			deDupTables.addAll(Arrays.asList(jobConfigurations.get("deDup.tables").toString().split(";")));
		globalSourceDep = !(configExists("deDup.source.all") && jobConfigurations.get("deDup.source.all").equals("false"));
		needOld = configExists("binlog.old.enable") && jobConfigurations.get("binlog.old.enable").equals("true");
//		globalSourceDep = !(configExists("delay.agg.enable") && jobConfigurations.get("delay.agg.enable").equals("false"));
//		needAggDelay = !configExists("delay.agg.enable") || !jobConfigurations.get("delay.agg.enable").equals("false");
		needAggDelay = configExists("delay.agg.enable") && jobConfigurations.get("delay.agg.enable").equals("true");
		needWaterMark = configExists("watermark.enable") && jobConfigurations.get("watermark.enable").equals("true");
		dedupKey = String.valueOf(jobConfigurations.getOrDefault("sink.dedup.key", ""));
		Map<String, String> globalConfig = JsonUtil.parseObject(getGlobalMetaManager().getDeployInfo().getGlobalConfig(), Map.class);
		Preconditions.checkArgument(globalConfig.containsKey("delay_join_table_id"), "delay_join_table_id not found in global config");
		dimTimeTableId = globalConfig.get("delay_join_table_id");
		// add time dim for each job. fix later.
		dimTimeTable = getGlobalMetaManager().getTableMetaPairByTableId(Long.parseLong(dimTimeTableId)).right.getTableName();
		chaosDataTableId = globalConfig.get("chaos_data_table_id");
		// add chaos table.
		chaosDataTable = getGlobalMetaManager().getTableMetaPairByTableId(Long.parseLong(chaosDataTableId)).right.getTableName();
		sourceTableSet.add(dimTimeTableId);
		sourceTableSet.add(chaosDataTableId);
		sourceTableSet.addAll(Arrays.stream(sourceTableIds.split(",")).collect(Collectors.toList()));

		binaryTables.add(chaosDataTable);
	}

	private void prepareGeneration() throws JsonProcessingException {
		// handle job configs
//		if ("dqc".equals(templateMode)) {
//			jobConfigurations.put("deDup.source.all", "false");
//		}

		isRawFlinkSql = "true".equals(jobConfigurations.getOrDefault(RAW_FLINK_SQL.key(), RAW_FLINK_SQL.defaultValue()));

		this.prepareGlobalParams();
		// handle drill data
		for (TeJobDataDrillDto.DrillData sourceDatum : teJobDataDrillDto.getSourceData()) {
			sourceMap.put(sourceDatum.getDataName(), sourceDatum.getData());
		}
	}

	@Override
	public Transformation generateSinkTransformation(Transformation input, String extSql) {
		GlobalSinkTransformation globalSinkTransformation = new GlobalSinkTransformation();
		List<Transformation> sinkTransformations = new ArrayList<>();
		for (Map.Entry<String, String> meta : sinkPriKeyMeta.entrySet()) {
			String sid = meta.getKey();
			String skey = meta.getValue();
			SinkTransformation sinkTransformation = new SinkTransformation(jobType);
			sinkTransformation.setTableIdentifier(tableMetaInverse.get(sid));
			sinkTransformation.setBinaryKafkaTables(binaryTables);
			sinkTransformation.setConnectConfig(wrapSimpleKeyPairConfig(TABLE_ID.key(), sid));
			sinkTransformation.setInput(input);
			if (StringUtils.isNotBlank(extSql)) {
				sinkTransformation.setExtSql(extSql);
			}
			sinkTransformation.setGlobalMetaManager(getGlobalMetaManager());
			sinkTransformation.setDataDrill(teJobDataDrillDto.getIsDataDrill());
			if (table2ViewNames != null && !StringUtil.isNullOrEmpty(table2ViewNames.get(tableMetaInverse.get(sid)))) {
				sinkTransformation.setTableName(table2ViewNames.get(tableMetaInverse.get(sid)));
				List<String> binaries = new ArrayList<>();
				if (binaryTables.contains(tableMetaInverse.get(sid))) {
					binaries.add(tableMetaInverse.get(sid));
//					binaries.add(table2ViewNames.get(tableMetaInverse.get(sid)));
				}
				sinkTransformation.setBinaryKafkaTables(binaries);
			}
			if (table2ViewNames != null && !StringUtil.isNullOrEmpty(sinkSqls.get(table2ViewNames.get(tableMetaInverse.get(sid))))) {
				sinkTransformation.setExtSql(sinkSqls.get(table2ViewNames.get(tableMetaInverse.get(sid))));
			}
			String primaryKey = skey;
			if (jobName.trim().toUpperCase().startsWith("RT_DCHECK") || jobName.trim().toUpperCase().startsWith("DCHECK") || jobName.trim().toUpperCase().startsWith("CHAOS_DCHECK")) {
				primaryKey = "dCheckSubjectCode";
			} else if (StringUtils.isEmpty(primaryKey)) {
				primaryKey = "FLK_AUTO_PFLAG";
			}
			SchemaConfig schemaConfig = new SchemaConfig(null, primaryKey);
			sinkTransformation.setSchemaConfig(schemaConfig);
			List<TeJobDataDrillDto.DrillData> expectData = teJobDataDrillDto.getExpectData();
			if (!CollectionUtil.isNullOrEmpty(expectData)) {
				sinkTransformation.setDrillData(expectData.get(0).getData());
			}
			Object o = this.jobConfigurations.get("dynamic.rule");
			sinkTransformation.setIsDynamicRule("true".equals(o == null ? "false" : o));
			sinkTransformation.setSinkDedup(StringUtils.isNotEmpty(dedupKey));
			sinkTransformation.setDedupKey(dedupKey);
			sinkTransformations.add(sinkTransformation);
		}
		globalSinkTransformation.setInputs(sinkTransformations);
		globalSinkTransformation.setDataDrill(teJobDataDrillDto.getIsDataDrill());
		return globalSinkTransformation;
	}


	public Transformation addDynamicRuleTransform(Transformation input, String extSelect) {
		DynamicRuleTransformation dynamicRuleTransformation = new DynamicRuleTransformation();
		dynamicRuleTransformation.setInput(input);
		dynamicRuleTransformation.setDynamicExtSelect(extSelect);
		return dynamicRuleTransformation;
	}

}

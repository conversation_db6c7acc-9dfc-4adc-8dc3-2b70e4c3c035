package com.dewu.flink.template.transform;

import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.annotation.JsonIgnoreProperties;

@JsonIgnoreProperties(ignoreUnknown = false)
public class TransformationWrapper {
    private String id;
    private String name;
    private String[] inputIds;
    private String transformation;


    public TransformationWrapper(String id, String name, String[] inputIds, String transformation) {
        this.id = id;
        this.name = name;
        this.inputIds = inputIds;
        this.transformation = transformation;
    }

    public String getId() {
        return id;
    }

    public String[] getInputIds() {
        return inputIds;
    }

    public String getTransformation() {
        return transformation;
    }

    public String getName() {
        return name;
    }

    public TransformationWrapper() {
    }
}

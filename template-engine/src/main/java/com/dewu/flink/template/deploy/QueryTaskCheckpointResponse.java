package com.dewu.flink.template.deploy;

import com.dewu.flink.template.common.Response;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Optional;

/**
 * Author: WangLin
 * Date: 2023/7/04 下午3:23
 * Description: 查询作业最新一次checkpoint返回结果
 */
@Data
@NoArgsConstructor
public class QueryTaskCheckpointResponse implements Response {
	/**
	 * 是否查询成功
	 */
	private boolean flag;
	/**
	 * 查询提示信息【成功："OK" 失败："具体失败原因"】
	 */
	private String msg;
	/**
	 * 查询结果
	 */
	private Optional<String> latestCheckpointPath;
}

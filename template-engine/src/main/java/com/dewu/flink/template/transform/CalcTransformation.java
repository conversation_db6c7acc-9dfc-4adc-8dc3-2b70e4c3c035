package com.dewu.flink.template.transform;

import com.dewu.flink.template.utils.TemplateSqlUtil;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.annotation.JsonIgnore;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import org.apache.flink.shaded.netty4.io.netty.util.internal.StringUtil;

import java.io.IOException;
import java.io.StringWriter;
import java.util.UUID;

@JsonIgnoreProperties(ignoreUnknown = true)
public class CalcTransformation extends OneInputTransformation {
    private String selectExpression;
    private String filterExpression;
    @JsonIgnore
    private String viewName = "view_"+UUID.randomUUID().toString().replace("-","");

    public CalcTransformation() {}

    @Override
    public Boolean isFieldNeed(String fieldName) {
        return null;
    }

    public String getSelectExpression() {
        return selectExpression;
    }

    public String getFilterExpression() {
        return filterExpression;
    }

    public void setSelectExpression(String selectExpression) {
        this.selectExpression = selectExpression;
    }

    public void setFilterExpression(String filterExpression) {
        this.filterExpression = filterExpression;
    }

    @Override
    public String transformStr(StringWriter writer) throws IOException {
        if(getResultFunction() != null){
            return getResultFunction();
        }
        String inputFunction = this.getInput().transformStr(writer);
        setResultFunction("calc_"+ UUID.randomUUID().toString().replace("-",""));
        String calcExpression = "";
//        if(!StringUtil.isNullOrEmpty(selectExpression)){
//            calcExpression += ".select(\"#selectExpression\")".replace("#selectExpression",selectExpression);
//        }
//        if(!StringUtil.isNullOrEmpty(filterExpression)){
//            calcExpression += ".where(\"#filterExpression\")".replace("#filterExpression",filterExpression);
//        }

        writer.write(TemplateSqlUtil.readTemplate("Calc.template")
                .replace("#transformFunction",getResultFunction())
                .replace("#inputFunction",inputFunction)
//                .replace("#calcExpression",calcExpression));
                .replace("#viewName",viewName)
                .replace("#selectExpression",StringUtil.isNullOrEmpty(selectExpression) ? "*":selectExpression)
                .replace("#filterExpression",StringUtil.isNullOrEmpty(filterExpression) ? "1=1":filterExpression));
        return getResultFunction();
    }

    @Override
    public Boolean validate() {
        return true;
    }
}

package com.dewu.flink.template.exceptions.handler;

import com.alibaba.fastjson.JSON;
import com.dewu.flink.template.exceptions.PermissionDeniedException;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;

import java.util.HashMap;
import java.util.Map;

/**
 * Author: WangLin
 * Date: 2024/6/6 13:55
 * Description: PermissionDeniedException 异常处理
 */
@ControllerAdvice
public class PermissionDeniedExceptionHandler {

    @ExceptionHandler(PermissionDeniedException.class)
    public ResponseEntity<Object> handlePermissionDeniedException(PermissionDeniedException e) {
        Map<String, Object> result = createResult("500", e.getMessage(), null);
        return new ResponseEntity<>(result, HttpStatus.OK);
    }

    private Map<String, Object> createResult(String code, String message, Object data) {
        Map<String, Object> result = new HashMap<>();
        result.put("code", code);
        result.put("mes", message);
        result.put("data", data);
        return result;
    }

}

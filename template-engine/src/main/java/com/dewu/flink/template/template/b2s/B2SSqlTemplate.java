package com.dewu.flink.template.template.b2s;

import com.dewu.flink.template.template.b2s.converbean.*;
import com.dewu.utils.StringUtils;
import org.apache.calcite.config.Lex;
import org.apache.calcite.sql.*;
import org.apache.calcite.sql.parser.SqlParser;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.core.JsonProcessingException;

import java.util.Collection;
import java.util.HashMap;
import java.util.Map;


public class B2SSqlTemplate extends SqlTemplate {

	private final Map<Integer, AbstractTransformBean> transformBeanMap = new HashMap<>();

	public B2SSqlTemplate(Map<String, String> tableMeta, Map<String, String> sinkPrimaryKey, String sql, String extSelectExpr) {
		super(tableMeta, sinkPrimaryKey, sql, extSelectExpr);
	}

	@Override
	public String generateTransformationStr() throws Exception {
		// 解析配置
		SqlParser.Config mysqlConfig = SqlParser.configBuilder().setLex(Lex.MYSQL).build();
		// 创建解析器
		SqlParser parser = SqlParser.create(sql, mysqlConfig);
		System.out.println(sql);
		System.out.println("meta : " + tableMeta);
		System.out.println("sinkPrimaryKey : " + sinkPrimaryKey);
		// 解析sql
		SqlNode sqlNode = parser.parseStmt();
		if (sqlNode instanceof SqlInsert) {
			extractInsert((SqlInsert) sqlNode);
		} else {
			throw new RuntimeException("Not a valid sqlInsert : " + sql);
		}
		String rstJson = getJsonParam(transformBeanMap.values());
		System.out.println("generate : " + rstJson);
		return rstJson;
	}

	public String getJsonParam(Collection<AbstractTransformBean> transformBeans) {
		try {
			StringBuilder rst = new StringBuilder("{\"transformationWrappers\":[");
			for (AbstractTransformBean bean : transformBeans) {
				rst.append(bean.getString());
				rst.append(",");
			}
			rst.deleteCharAt(rst.lastIndexOf(","));
			rst.append("],\"optionConfigs\":\"\"}");
			return rst.toString();
		} catch (JsonProcessingException e) {
			e.printStackTrace();
		}
		return null;
	}

	public AbstractTransformBean extractSink(SqlIdentifier sqlNode) {
		SinkBean sink = fetchRawSink(sqlNode, "");
		return register(sink);
	}

	private void extractInsert(SqlInsert sqlNode) {
		AbstractTransformBean sink = extractSink((SqlIdentifier) sqlNode.getTargetTable());
		AbstractTransformBean select = extractSelect((SqlSelect) sqlNode.getSource(), "");
		select.setSelectExpression(wrapExtSelectExpr(select.getSelectExpression()));
		sink.setInputIds(new int[]{select.getId()});
	}

	private String wrapExtSelectExpr(String selectExpr) {
		if (StringUtils.isNotBlank(extSelectExpr)) {
			return selectExpr + "," + extSelectExpr;
		}
		return selectExpr;
	}

	private AbstractTransformBean extractSelect(
		SqlSelect sqlNode,
		String tableNameAlias) {

		SqlNodeList selectList = sqlNode.getSelectList();
		SqlNode where = sqlNode.getWhere();
		SqlNodeList group = sqlNode.getGroup();
		SqlNode having = sqlNode.getHaving();
		SqlNode from = sqlNode.getFrom();

		AbstractTransformBean transform = null;
		if (null != group) {
			GroupByBean groupByBean = new GroupByBean(getString(group), getString(having), getString(where));
			groupByBean.setSelectExpression(getString(selectList));
			transform = groupByBean;
			transform.setUniqKey(getString(group));
		} else if (from instanceof SqlJoin) {
			CalcBean calc = new CalcBean(getString(where));
			calc.setSelectExpression("*");
			transform = calc;
			transform.setUniqKey(getString(((SqlJoin) from).getCondition()));
		} else {
			CalcBean calc = new CalcBean(getString(where));
			calc.setSelectExpression(getString(selectList));
			transform = calc;
		}

		//来源表
		AbstractTransformBean source = extractSource(from, getString(selectList));
		transform.setInputIds(new int[]{source.getId()});
		transform.setAlias(tableNameAlias);
		return register(transform);
	}

	private AbstractTransformBean extractSource(
		SqlNode tableNode, String selectList) {
		String tableAlias = "";
		AbstractTransformBean transform = null;

		if (tableNode instanceof SqlBasicCall) {
			SqlNode[] tableNodeOps = ((SqlBasicCall) tableNode).getOperands();
			SqlNode operand = tableNodeOps[0];
			tableAlias = getString(tableNodeOps[1]);
			if (operand instanceof SqlIdentifier) {
				transform = fetchRawSource((SqlIdentifier) operand, tableAlias);
			} else if (operand instanceof SqlSelect) {
				transform = extractSelect((SqlSelect) operand, tableAlias);
			}
		} else if (tableNode instanceof SqlIdentifier) {
			transform = fetchRawSource((SqlIdentifier) tableNode, tableAlias);
		} else if (tableNode instanceof SqlJoin) {//双表
			//最外层
			SqlJoin sqlJoin = (SqlJoin) tableNode;
			SqlNode condition = sqlJoin.getCondition();
			JoinType joinType = sqlJoin.getJoinType();
			JoinBean joinBean = new JoinBean(condition.toString(), joinType.toString(), false);
			joinBean.setSelectExpression(selectList);

			SqlNode left = sqlJoin.getLeft();
			AbstractTransformBean leftSource = extractSource(left, "*");
			SqlNode right = sqlJoin.getRight();
			AbstractTransformBean rightSource = extractSource(right, "*");
			joinBean.setInputIds(new int[]{leftSource.getId(), rightSource.getId()});

			if (StringUtils.isNotBlank(leftSource.getAlias())) {
				joinBean.setLeftViewAlias(leftSource.getAlias());
			}
			if (StringUtils.isNotBlank(rightSource.getAlias())) {
				joinBean.setRightViewAlias(rightSource.getAlias());
			}
			transform = joinBean;
		}
		if (null == transform) {
			throw new RuntimeException("Not a valid table expr, or some case we not support now: " + getString(tableNode));
		}
		return register(transform);
	}

	public AbstractTransformBean register(AbstractTransformBean transformBean) {
		transformBeanMap.put(transformBean.getId(), transformBean);
		return transformBean;
	}

	public SourceBean fetchRawSource(SqlIdentifier tableNode, String tableAlias) {
		String tableName = tableNode.names.get(0).replace("`", "");
		System.out.printf("-------添加source表 %s as %s%n", tableName, tableAlias);
		SourceBean sourceBean = new SourceBean(tableName, tableMeta.get(tableName));
		sourceBean.setAlias(tableAlias);
		return sourceBean;
	}

	public SinkBean fetchRawSink(SqlIdentifier tableNode, String tableAlias) {
		String tableName = tableNode.names.get(0).replace("`", "");
		System.out.printf("-------添加sink表 %s as %s%n", tableName, tableAlias);
		SinkBean sinkBean = new SinkBean(tableName, tableMeta.get(tableName), "FLK_AUTO_PFLAG");
		sinkBean.setAlias(tableAlias);
		return sinkBean;
	}

	public static void main(String[] args) throws Exception {
		// Sql语句
//		String sql = "insert into test select aid, bid, c.id as cid from (select a.id as aid,b.id as bid, a.age as aage  from A a left join B b on a.uid =b.uid)  t left join C c on t.a_age =c.age ";
		String sql = "insert into test select billNo, payStatus,amount, bizNo, clearReqStatus\n" +
			"from (SELECT JsonValue(a.bill_no) AS billNo, a.pay_status AS payStatus, a.amount AS amount, b.biz_no AS bizNo, b.clear_req_status AS clearReqStatus, a.version as version, a.tenant_id as tenant_id, a.pay_type as pay_type FROM ods_du_kefu_admin_pay_bill_detail a JOIN ( SELECT biz_no ,clear_req_status FROM ods_dw_clearing_clearing_req ) b ON a.bill_no = b.biz_no \n" +
			") t0\n" +
			"WHERE payStatus IN ('1','2','3') AND version = 1 AND tenant_id = 1 AND pay_type IN ('1','2','9')";
//		String sql = "insert into test select count(1) from ("
//			+ "select name,age from ("
//			+ "select * from B group by name "
//			+ ")b_temp where age=20"
//			+ ")inventory group by time1 having count(1)>1";

//        sql =  "insert into test select * from B b_temp join A a_temp"
//                + " on b_temp.day1=a_temp.name where age=20";

//		sql = "select name,age from ("
//			+ "select * from B group by name "
//			+ ")B_temp join("
//			+ "select day1 from a"
//			+ ")a_temp on a_temp.day1=B_temp.name where age=20";
//
//		sql = "insert into test "
//			+ "select name as name,age as age from B B_temp join("
//			+ "select day1 from a"
//			+ ")a_temp on a_temp.day1=B_temp.name where age=20";

//        sql = "select * from B_temp join("
//                + "select day1 from a"
//                + ")a_temp on a_temp.day1=B_temp.name where age=20";

//        sql = "select * from B B_temp";


//        sql = "select name,age from ("
//                + "select * from B group by name "
//                + ")b_temp join a a_temp on b_temp.day1=a_temp.name where age=20";

//        sql = "select * from ("
//                + "select name,age from ("
//                + "select * from B group by name "
//                + ")temp where temp.name='haha')"
//                + "niubi join inventory_sale sale on sale.day1=haha.name where age=20";


//        sql = "select id,money from (select * from ("
//                + "select name,age from ("
//                + "select * from B group by name "
//                + ")temp where temp.name='haha')"
//                + "niubi join inventory_sale sale on sale.day1=haha.name where age=20)main join "
//                + "qian on qian.name=main.qunide";
		Map<String, String> tableMeta = new HashMap<>();
		tableMeta.put("ods_du_kefu_admin_pay_bill_detail", "1001");
		tableMeta.put("ods_dw_clearing_clearing_req", "1002");
		tableMeta.put("C", "1003");
		tableMeta.put("test", "1004");
		Map<String, String> sinkPrimaryKey = new HashMap<>();
		sinkPrimaryKey.put("test", "name");
		System.out.println(new B2SSqlTemplate(tableMeta, sinkPrimaryKey, sql, "").generateTransformationStr());
	}

}

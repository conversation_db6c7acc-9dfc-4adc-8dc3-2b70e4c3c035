package com.dewu.flink.template.transform;


import com.dewu.flink.template.chaos.ChaosRuleInfo;
import com.dewu.flink.template.chaos.rules.service.dto.ChaosRulesDto;
import com.dewu.flink.template.config.ColumnInfo;
import com.dewu.flink.template.config.SchemaConfig;
import com.dewu.flink.template.meta.GlobalMetaBaseDomain;
import com.dewu.flink.template.meta.datasource.service.dto.ReDataSourceDto;
import com.dewu.flink.template.utils.ExprInfo;
import com.dewu.flink.template.utils.SimpleExpressionParser;
import com.dewu.flink.template.utils.TemplateSchemeType;
import com.dewu.flink.template.utils.TemplateSqlUtil;
import com.dewu.utils.StringUtils;
import com.google.common.base.Joiner;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.annotation.JsonIgnore;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import org.apache.flink.shaded.netty4.io.netty.util.internal.StringUtil;
import org.msgpack.core.Preconditions;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.io.StringWriter;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import static com.dewu.flink.template.translator.generator.LibraConnectorType.*;
import static com.dewu.flink.template.utils.JsonUtil.getJsonValues;

@EqualsAndHashCode(callSuper = true)
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class SourceTransformation extends AbstractTransformation {
	private boolean isStreamVarbinaryMode = false;
	private boolean isBinlogSource = false;
	private boolean isDeDup = true;
	private boolean needOld = false;
	private boolean needAggDelay = false;
	private boolean needWaterMark = false;
	private ChaosRuleInfo chaosRuleInfo;
	private ChaosRulesDto chaosRules;
	int delaySeconds = -1;
	private boolean isDataDrill = false;
	private List<String> binaryTables = new ArrayList<>();
	private List<String> delayTables = new ArrayList<>();
	private List<String> streamTables = new ArrayList<>();
	private List<String> offlineTables = new ArrayList<>();
	private List<String> binlogTables = new ArrayList<>();
	private List<String> deDupTables = new ArrayList<>();
	private List<String> dimTables = new ArrayList<>();
	private List<String> drillData = new ArrayList<>();
	private List<String> definedCols = new ArrayList<>();
	private GlobalMetaBaseDomain globalMetaBaseDomain = null;
	private boolean globalSourceDep = true;
	private static AtomicInteger rowNumCounter = new AtomicInteger();
	private Properties connectConfig;
	private SchemaConfig schemaConfig = new SchemaConfig();
	private String combinedTransformExpression = "";
	private String preCombinedFilterForBinlog = "";
	private static final Logger log = LoggerFactory.getLogger(SourceTransformation.class);
	@JsonIgnore
	private String tableName = "source_" + UUID.randomUUID().toString().replace("-", "");
	@JsonIgnore
	private String viewName = "view_" + UUID.randomUUID().toString().replace("-", "");
	@JsonIgnore
	private String binlogOldColumnPrefix = "FLK_OLD_";
	public final static String BINLOG_RECORD_TYPE = "FLK_BINLOG_RECORD_TYPE";
	public final static String OFFSET = "FLK_KAFKA_OFFSET";
	public final static String PARTITION = "FLK_KAFKA_PARTITION";
	public final static String TS = "FLK_KAFKA_TS";
	public final static String BINLOG_TS = "FLK_BINLOG_TS";

	public final static String PRE_COMBINE_VIEW_PREFIX = "FLK_PRE_COMBINE_VIEW_";
	public final static String SRC_TRANS_VIEW_PREFIX = "FLK_SRC_TRANS_VIEW_";
	public final static String COL_FAMILY = "cf";
	public final static String FLK_TE_MSG_FLAG = "FLK_TE_MSG_FLAG";
	String preCombineViewName;
	String sourceTransformViewName;
	private String rawSQL = "";

	public SourceTransformation() {
		preCombineViewName = PRE_COMBINE_VIEW_PREFIX + tableName;
		sourceTransformViewName = SRC_TRANS_VIEW_PREFIX + tableName;
	}

	public SourceTransformation(Properties context) {
		this();
		if (null != context) {
			this.context = context;
		}
	}

	public boolean isStreamVarbinaryMode() {
		return isStreamVarbinaryMode;
	}

	public void setStreamVarbinaryMode(boolean streamVarbinaryMode) {
		isStreamVarbinaryMode = streamVarbinaryMode;
	}

	@Override
	public Boolean isFieldNeed(String fieldName) {
		return CollectionUtils.isEmpty(definedCols) || definedCols.contains(fieldName);
	}

	public void setRawSQL(String rawSQL) {
		this.rawSQL = rawSQL;
	}

	@Override
	public String transformStr(StringWriter writer) throws IOException {
		long startTime = System.currentTimeMillis();

		// 1. pre build env
		long tableId = Long.parseLong(connectConfig.getProperty("tableId"));
		if (null != chaosRuleInfo) {
			chaosRules = chaosRuleInfo.getChaosRule().get(tableId);
		}
		if (getResultFunction() != null) {
			return getResultFunction();
		}
		setResultFunction("source_" + UUID.randomUUID().toString().replace("-", ""));
		// if somewhere we forget to inject globalMetaBaseDomain
		globalMetaBaseDomain = getGlobalMetaManager().getTableMetaByTableId(Long.valueOf(connectConfig.getProperty("tableId")));

		// 2. update connect config
		updateConnectConfig(connectConfig, globalMetaBaseDomain, SourceTransformation.class);

		String cluster = globalMetaBaseDomain.getReDataSourceDto().getDataSourceName();
		String connector = String.valueOf(connectConfig.get("connector"));
		boolean isStreamSource = connector.equals(C_KAFKA.getName()) || connector.equals(C_ROCKET_MQ.getName()) || connector.equals(C_UPSERT_KAFKA.getName()) || connector.equals(C_ORIGIN_UPSERT_KAFKA.getName());
		isDeDup = globalSourceDep || deDupTables.contains(connectConfig.getProperty("topic"));
		isBinlogSource = cluster.toLowerCase().contains("binlog") || binlogTables.contains(connectConfig.getProperty("topic"));
		String topic = connectConfig.getProperty("topic");
		if (isStreamSource) {
			needAggDelay = needAggDelay && delayTables.contains(topic);
			isStreamVarbinaryMode = isStreamVarbinaryMode || binaryTables.contains(topic);
		}else{
			isStreamVarbinaryMode = false;
		}

		// 3. update schema config
		String colsConfigKey = "source.#table.cols".replace("#table", globalMetaBaseDomain.getTableName());
		if (context.containsKey(colsConfigKey)) {
			String colsStr = context.getProperty(colsConfigKey, "");
			definedCols.addAll(Arrays.stream(colsStr.split(";")).collect(Collectors.toList()));
			log.info("defined cols with {} : " + colsStr, globalMetaBaseDomain.getTableName());
		}

		if (!isStreamVarbinaryMode) {
			updateSchemaConfig(connectConfig, schemaConfig, globalMetaBaseDomain);
		}

		// 4. trash
		connectConfig.remove("tableId");

		// 5. add dedup pro
		if (!isStreamVarbinaryMode && isDeDup && !StringUtil.isNullOrEmpty(schemaConfig.getPrimaryKey())) {
			String primaryKey = "`" + schemaConfig.getPrimaryKey().replace(",", "`,`") + "`";
			String rowNumber = "row_num_src" + rowNumCounter.incrementAndGet();
			String rowNumberExpression = "ROW_NUMBER() OVER (PARTITION BY " + primaryKey + " ORDER BY proctime() DESC) AS " + rowNumber;
			String dedupQuery = "SELECT * FROM (SELECT *, " + rowNumberExpression + " FROM $.baseTable) WHERE " + rowNumber + " <= 1";

			if (StringUtil.isNullOrEmpty(combinedTransformExpression)) {
				combinedTransformExpression = dedupQuery;
			} else {
				combinedTransformExpression = combinedTransformExpression.replace("$.baseTable", "(" + dedupQuery + ")");
			}
		}

		// log some detail infos.
		log.info("binaryTables: {} isBinary: {} with {}", binaryTables, isStreamVarbinaryMode, topic);
		log.info("binlogTables: {} isBinlog: {} with {}", binlogTables, isBinlogSource, topic);
		log.info("connectConfig: {}", connectConfig);
		log.info("schemaConfig: {}", schemaConfig);
		log.info("isDeDup: {}", isDeDup);
		log.info("needOld: {}", needOld);

		// 6. choose different schema type.
		TemplateSchemeType schemeType = TemplateSchemeType.NORMAL;
		if (connector.equals(C_HBASE.getName())) {
			schemeType = TemplateSchemeType.HBASE;
			schemaConfig.getExtInfo().put(COL_FAMILY, globalMetaBaseDomain.getReDataSourceMetadataDto().getColumnFamily());
		} else if (isStreamVarbinaryMode || isBinlogSource) {
			schemeType = connector.equals(C_ROCKET_MQ.getName()) ? TemplateSchemeType.DMQ_BINARY : TemplateSchemeType.BINARY;
		}

		// 7. build source schema
		SchemaInfo schemaInfo = buildSchema(schemeType);

		// 8. Pandora Box. Build combined sql, parse json / binlog / data mock....
		CombinedSqls combinedSqls = generatePandoraSqls(combinedTransformExpression, schemaConfig, schemeType, isStreamSource, needWaterMark);

		// 9. gen source code by source template.
		writer.write(schemaInfo.sourceTemplate
			.replace("#transformFunction", getResultFunction())
			.replace("#schema", schemaInfo.schema)
			.replace("#drillData", schemaInfo.drillDataString)
			.replace("#isOfflineSource", offlineTables.contains(tableName) ? "true" : "false")
			.replace("#needPreCombine", String.valueOf(StringUtils.isNotEmpty(combinedSqls.preCombineExpression)))
			.replace("#preCombinedViewName", preCombineViewName)
			.replace("#preCombinedTransformExpression", inlineStr(combinedSqls.preCombineExpression))
			.replace("#combinedTransformExpression", inlineStr(combinedSqls.combineExpression))
			.replace("#tableName", tableName)
			.replace("#config", TemplateSqlUtil.generateConfigStr(connectConfig, connector, true, schemeType))
			.replace("#viewName", viewName)
		);
		log.info("transform source " + tableName + " takes about " + (System.currentTimeMillis() - startTime) + " ms.");
		return getResultFunction();
	}

	private SchemaInfo buildSchema(TemplateSchemeType schemeType) throws IOException {
		SchemaInfo schemaInfo = SchemaInfo.getInstance();
		if (isDataDrill && Objects.nonNull(drillData)) {
			schemaInfo.sourceTemplate = TemplateSqlUtil.readTemplate("SourceDataDrill.template");
			log.info("drillData is:{}", drillData);
			schemaInfo.drillDataString = (isStreamVarbinaryMode || isBinlogSource) ?
				Joiner.on(",").join(drillData.stream().map(data -> "Row.of(\"\",\"" + data.replace("\\", "\\\\").replace("\"", "\\\"") + "\".getBytes(),\"\",0L,0L,0L)").toArray())
				: Joiner.on(",").join(drillData.stream().map(data -> "Row.of(" + getJsonValues(data) + ")").toArray());
			schemaInfo.schema = TemplateSqlUtil.generateDataDrillColumnStr(schemaConfig, isStreamVarbinaryMode || isBinlogSource, drillData);
		} else {
			schemaInfo.sourceTemplate = TemplateSqlUtil.readTemplate("Source.template");
			schemaInfo.schema = TemplateSqlUtil.generateColumnStr(schemaConfig, schemeType, true, needAggDelay, needWaterMark);
		}
		return schemaInfo;
	}

	private String inlineStr(String str) {
		if (!StringUtil.isNullOrEmpty(str)) {
			return str.replace("\n", " ");
		}
		return str;
	}

	@Override
	public Boolean validate() {
		if (connectConfig == null || StringUtil.isNullOrEmpty(connectConfig.getProperty("tableId"))) {
			return false;
		}
		return true;
	}

	private void validateSchema(SchemaConfig schemaConfig) {
		if (needAggDelay && delayTables.contains(tableName)) {
			Preconditions.checkState(!StringUtil.isNullOrEmpty(schemaConfig.getPrimaryKey()) && isBinlogSource, "状态聚合类延迟仅支持binlog且需设置主键.");
		}
	}

	private void produceBinlogParseSqls(InnerSqls innerSqls, String pk) {
		if (isDataDrill) {
			// DATA DRILL MODE BINLOG PARSER
			innerSqls.binlogParserSql = String.format("SELECT * FROM (SELECT #selectExpression, CAST(JsonValue(cast(`message` as varchar),'$.es') AS BIGINT) AS %s,dts_operation_flag as %s " +
						"FROM `%s`,  LATERAL TABLE (binlogParser (cast(`message` as varbinary))) as tmp (data,old_data,dts_operation_flag,dts_db_name,dts_table_name,dts_before_flag,dts_after_flag) ) #prefilter",
					BINLOG_TS, BINLOG_RECORD_TYPE, tableName)
				.replace("#selectExpression", TemplateSqlUtil.generateBinlogSchemaStr(schemaConfig, needOld, binlogOldColumnPrefix))
				.replace("#prefilter", StringUtils.isEmpty(preCombinedFilterForBinlog) ? "" : " where " + preCombinedFilterForBinlog);

		} else if (needAggDelay) {
			// AGG DELAY MODE BINLOG PARSER
			String tableStr = StringUtils.isEmpty(innerSqls.preCombineExpression) ? TemplateSqlUtil.wrapWithBackQuote(tableName) : innerSqls.preCombineExpression.replace("$.baseTable", TemplateSqlUtil.wrapWithBackQuote(tableName));
			innerSqls.preCombineExpression = ("SELECT * FROM (SELECT #selectExpression, `offset` as " + OFFSET + ", `partition` as " + PARTITION + ", `ts` as " + TS + ", CAST(JsonValue(cast(`message` as varchar),'$.es') AS BIGINT) AS " + BINLOG_TS + ",dts_operation_flag as " + BINLOG_RECORD_TYPE + " FROM #tableName,  LATERAL TABLE (binlogParser (`message`)) as tmp (data,old_data,dts_operation_flag,dts_db_name,dts_table_name,dts_before_flag,dts_after_flag)) #prefilter")
				.replace("#tableName", tableStr)
				.replace("#selectExpression", TemplateSqlUtil.generateBinlogSchemaStr(schemaConfig, needOld, binlogOldColumnPrefix))
				.replace("#prefilter", StringUtils.isEmpty(preCombinedFilterForBinlog) ? "" : " where " + preCombinedFilterForBinlog);
			innerSqls.binlogParserSql = (" SELECT #basePK,#selectExpression   FROM (  SELECT window_start, window_end, #basePK, #selectLVExpression,dewuLastValue(unix_timestamp(cast(" + TS + " as varchar))) as FLK_WAT_TS   FROM TABLE(   HOP(TABLE #tableName, DESCRIPTOR(" + TS + "), INTERVAL '#delayTime' SECONDS, INTERVAL '#windowTime' SECONDS))       GROUP BY window_start, window_end, #basePK     ) where FLK_WAT_TS + #delayTime < UNIX_TIMESTAMP(cast(window_end as varchar))")
				.replace("#basePK", pk)
				.replace("#selectExpression", TemplateSqlUtil.generateBinlogSelectSchemaStr(schemaConfig, needOld, binlogOldColumnPrefix))
				.replace("#selectLVExpression", TemplateSqlUtil.generateBinlogSelectLVSchemaStr(schemaConfig, needOld, binlogOldColumnPrefix))
				.replace("#delayTime", String.valueOf(delaySeconds))
				.replace("#windowTime", String.valueOf(delaySeconds * 2))
				.replace("#tableName", preCombineViewName);
		} else {
			// NORMAL MODE BINLOG PARSER
			String tableStr = StringUtils.isEmpty(innerSqls.preCombineExpression) ? TemplateSqlUtil.wrapWithBackQuote(tableName) : preCombineViewName;
			String formattedSql = needWaterMark ? ("SELECT * FROM (SELECT #selectExpression, `offset` as " + OFFSET + ", `partition` as " + PARTITION + ", `ts` as " + TS + ", CAST(JsonValue(cast(`message` as varchar),'$.es') AS BIGINT) AS " + BINLOG_TS + ",dts_operation_flag as " + BINLOG_RECORD_TYPE + " FROM #tableName,  LATERAL TABLE (binlogParser (`message`)) as tmp (data,old_data,dts_operation_flag,dts_db_name,dts_table_name,dts_before_flag,dts_after_flag)) #prefilter")
				: ("SELECT * FROM (SELECT #selectExpression, `offset` as " + OFFSET + ", `partition` as " + PARTITION + ", unix_timestamp(cast(ts as varchar)) " + TS + ", CAST(JsonValue(cast(`message` as varchar),'$.es') AS BIGINT) AS " + BINLOG_TS + ",dts_operation_flag as " + BINLOG_RECORD_TYPE + " FROM #tableName,  LATERAL TABLE (binlogParser (`message`)) as tmp (data,old_data,dts_operation_flag,dts_db_name,dts_table_name,dts_before_flag,dts_after_flag)) #prefilter");
			innerSqls.binlogParserSql = formattedSql.replace("#tableName", tableStr)
				.replace("#selectExpression", TemplateSqlUtil.generateBinlogSchemaStr(schemaConfig, needOld, binlogOldColumnPrefix))
				.replace("#prefilter", StringUtils.isEmpty(preCombinedFilterForBinlog) ? "" : " where " + preCombinedFilterForBinlog);

		}

		log.info("innerSqls: " + innerSqls);
	}


	private CombinedSqls generatePandoraSqls(String combinedTransformExpression, SchemaConfig schemaConfig, TemplateSchemeType schemeType, boolean isStreamSource, boolean needWaterMark) {
		validateSchema(schemaConfig);

		InnerSqls innerSqls = InnerSqls.getNewInstance();
		boolean isBinlogKafkaMode = !isStreamVarbinaryMode && isBinlogSource;
		boolean isJsonKafkaMode = schemeType.equals(TemplateSchemeType.NORMAL) && isStreamSource;

		if (chaosRuleInfo != null) {
			try {
				buildTransSql(innerSqls);
			} catch (Exception e) {
				throw new RuntimeException("error when build chaos rules: " + e.getMessage(), e);
			}
		}

		if (isBinlogKafkaMode) {
			produceBinlogParseSqls(innerSqls, schemaConfig.getPrimaryKey());
		}

		String combinedSql;
		if (StringUtil.isNullOrEmpty(combinedTransformExpression)) {
			combinedSql = isBinlogKafkaMode ?
				innerSqls.binlogParserSql :
				String.format("SELECT * FROM `%s`", tableName);
		} else {
			combinedSql = isBinlogKafkaMode ?
				combinedTransformExpression.replace("$.baseTable", "(" + innerSqls.binlogParserSql + ")") :
				combinedTransformExpression.replace("$.baseTable", tableName);
		}

		return new CombinedSqls(combinedSql, innerSqls.preCombineExpression.replace("$.baseTable", tableName));
	}

	private void buildTransSql(InnerSqls innerSqls) throws Exception {
		// build chaos expr
		if (null != chaosRules) {
			String chaosRuleType = chaosRules.getDatagenRuleType();

			// (1 数据篡改，2 数据注入)
			if ("1".equals(chaosRuleType)) {
				String select = buildSelectString(SimpleExpressionParser.parse(chaosRules.getDatagenRuleDetail()));
				String mockTemp = "SELECT #selectExpr,* FROM $.baseTable".replace("#selectExpr", select);
				if (StringUtils.isNotEmpty(combinedTransformExpression)) {
					combinedTransformExpression = combinedTransformExpression.replace("$.baseTable", "(" + mockTemp + ")");
				} else {
					combinedTransformExpression = mockTemp;
				}
			} else {
				innerSqls.preCombineExpression = "SELECT * FROM (SELECT * FROM $.baseTable UNION ALL SELECT * FROM FLK_TE_CHAOS_DATA_BUS WHERE JSON_VALUE(cast(message as varchar),'$." + FLK_TE_MSG_FLAG + "') = '#CHAOS_FLAG')".replace("#CHAOS_FLAG", chaosRuleInfo.getChaosPlanTask().getId() + "_" + chaosRules.getTableId());
			}
		}
	}

	private String buildSelectString(ExprInfo mockExpr) {
		Map<String, String> fieldsInfos = mockExpr.fieldsInfos;
		List<String> exprs = new ArrayList<>();
		for (String key : fieldsInfos.keySet()) {
			exprs.add(fieldsInfos.get(key) + " as " + TemplateSqlUtil.wrapWithSimpleBackQuote(key));
		}
		return String.join(", ", exprs);
	}


	private SchemaConfig generateBinlogSchemaConfig() {
		SchemaConfig binlogSourceSchemaConfig = new SchemaConfig();
		List<ColumnInfo> binlogSourceColumnInfo = new ArrayList();
		binlogSourceColumnInfo.add(new ColumnInfo("data", "VARCHAR"));
		binlogSourceColumnInfo.add(new ColumnInfo("type", "VARCHAR"));
		binlogSourceColumnInfo.add(new ColumnInfo("old", "VARCHAR"));
		binlogSourceSchemaConfig.setColumns(binlogSourceColumnInfo);
		return binlogSourceSchemaConfig;
	}

	private String getCluster(Long tableId) {
		GlobalMetaBaseDomain globalMetaBaseDomain = getGlobalMetaManager().getTableMetaByTableId(tableId);
		ReDataSourceDto reDataSourceDto = globalMetaBaseDomain.getReDataSourceDto();
		return reDataSourceDto.getDataSourceName();
	}

	@Override
	public String toString() {
		return "SourceTransformation{" +
			"isVarbinarySource=" + isStreamVarbinaryMode +
			", isBinlogSource=" + isBinlogSource +
			", isDeDup=" + isDeDup +
			", binaryTables=" + binaryTables +
			", binlogTables=" + binlogTables +
			", deDupTables=" + deDupTables +
			", globalSourceDep=" + globalSourceDep +
			", connectConfig=" + connectConfig +
			", schemaConfig=" + schemaConfig +
			", combinedTransformExpression='" + combinedTransformExpression + '\'' +
			", tableName='" + tableName + '\'' +
			", viewName='" + viewName + '\'' +
			", binlogOldColumnPrefix='" + binlogOldColumnPrefix + '\'' +
			'}';
	}

	@ToString
	static class InnerSqls {
		String binlogParserSql = "";
		String preCombineExpression = "";

		public static InnerSqls getNewInstance() {
			return new InnerSqls();
		}
	}

	static class CombinedSqls {
		String combineExpression = "";
		String preCombineExpression = "";

		public CombinedSqls(String combineExpression, String preCombineExpression) {
			this.combineExpression = combineExpression;
			this.preCombineExpression = preCombineExpression;
		}
	}

	static class SchemaInfo {
		String schema = "";
		String sourceTemplate = "";
		String drillDataString = "";

		public static SchemaInfo getInstance() {
			return new SchemaInfo();
		}
	}
}

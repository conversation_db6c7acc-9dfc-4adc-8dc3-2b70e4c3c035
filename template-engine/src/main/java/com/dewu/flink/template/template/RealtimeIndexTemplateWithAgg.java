package com.dewu.flink.template.template;

import com.dewu.flink.template.meta.metric.service.dto.TeMetricMetadataInfoDto;
import com.dewu.flink.template.transform.*;
import com.dewu.flink.template.translator.generator.ParamTool;
import com.dewu.flink.template.utils.TemplateSqlUtil;
import com.dewu.utils.StringUtils;
import com.google.common.collect.Sets;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.calcite.sql.SqlNode;
import org.apache.calcite.sql.SqlNodeList;
import org.apache.calcite.sql.SqlSelect;
import org.apache.calcite.sql.parser.SqlParseException;
import org.apache.calcite.sql.parser.SqlParser;
import org.apache.flink.api.java.tuple.Tuple2;
import org.apache.flink.configuration.ConfigOption;
import org.apache.flink.table.api.SqlDialect;
import org.apache.flink.util.CollectionUtil;
import org.apache.flink.util.Preconditions;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

import static com.dewu.flink.template.translator.generator.TemplateOption.*;
import static com.dewu.flink.template.utils.SqlParserUtils.getCurrentSqlParserConfig;

@EqualsAndHashCode(callSuper = true)
@Data
public class RealtimeIndexTemplateWithAgg extends AbstractTemplate {

	private static final Logger logger = LoggerFactory.getLogger(RealtimeIndexTemplateWithAgg.class);
	private final static Set<ConfigOption<?>> mustOpts = Sets.newHashSet(
		DIM_IDS,
		METRIC_IDS,
		SINK_TABLE_ID
	);

	private String dims;
	private String metrics;
	private String extSql;

	public static RealtimeIndexTemplateWithAgg apply(Map<String, String> params, Properties jobConfigs, String jobName) {
		ParamTool tool = ParamTool.apply(params);
		logger.info("jobConfigs:" + jobConfigs);
		tool.checkNonNullParam(mustOpts);

		RealtimeIndexTemplateWithAgg realtimeIndexTemplate = new RealtimeIndexTemplateWithAgg();

		realtimeIndexTemplate.setDims(tool.getString(DIM_IDS));
		realtimeIndexTemplate.setMetrics(tool.getString(METRIC_IDS));
		realtimeIndexTemplate.setSinkTableId(tool.getString(SINK_TABLE_ID));
		realtimeIndexTemplate.setJobName(jobName);
		realtimeIndexTemplate.setExtSql(tool.getString(EXT_SQL));
		realtimeIndexTemplate.setJobConfigurations(jobConfigs);

		return realtimeIndexTemplate;
	}

	@Override
	public String generateTransformationStr() throws IOException {
		List<String> dimNames = new ArrayList<>();
		List<String> metricIDs = Arrays.asList(metrics.split(","));
		List<String> metricNames = new ArrayList<>();
		List<TeMetricMetadataInfoDto> metrics =
			metricIDs.stream().map(metricId -> {
				TeMetricMetadataInfoDto metric = getGlobalMetaManager().getMetric(Long.parseLong(metricId));
				String metricDims = metric.getTeMetricGroupMetadataInfoDto().getTeCommonAttributesIds2();
				if (!arrEqual(metricDims, dims)) {
					throw new RuntimeException("dim not matched when create index template with " + dims + " but found " + metricDims + " for metric " + metric.getMetricName());
				}
				return metric;
			}).collect(Collectors.toList());

		Map<Tuple2<Long, String>, List<TeMetricMetadataInfoDto>> groupedMetrics =
			metrics.stream().collect(Collectors.groupingBy(metric -> new Tuple2<>(metric.getTeMetricGroupMetadataInfoDto().getReDataSourceMetadataId(), metric.getTeMetricGroupMetadataInfoDto().getMetricGroupName())));
		List<Transformation> sourceTransformations = new ArrayList<>();
		Map<String, Transformation> regSources = new HashMap<>();
		for (Map.Entry<Tuple2<Long, String>, List<TeMetricMetadataInfoDto>> entry : groupedMetrics.entrySet()) {
			Tuple2<Long, String> k = entry.getKey();
			List<TeMetricMetadataInfoDto> v = entry.getValue();

			List<String> requiredMetrics = new ArrayList<>();
			for (TeMetricMetadataInfoDto teMetricMetadataInfoDto : v) {
				requiredMetrics.add(teMetricMetadataInfoDto.getMetricName());
				if (CollectionUtil.isNullOrEmpty(dimNames)) {
					dimNames = Arrays.asList(teMetricMetadataInfoDto.getTeMetricGroupMetadataInfoDto().getTeCommonAttributesIds().split(","));
				}
			}
			metricNames.addAll(requiredMetrics);
			String groupName = k.f1;
			String tableId = String.valueOf(k.f0);
			Transformation sourceTransformation;
			if (regSources.containsKey(tableId)) {
				sourceTransformation = regSources.get(tableId);
			} else {
				SourceTransformation tmp = new SourceTransformation();
				tmp.setConnectConfig(addStartTsConfig(wrapSimpleKeyPairConfig(TABLE_ID.key(), tableId)));
//			sourceTransformation.setCombinedTransformExpression(String.format("SELECT %s FROM ( SELECT *,ROW_NUMBER() OVER (PARTITION BY %s ORDER BY proctime() desc) AS row_num FROM $.baseTable) WHERE row_num <= 1", selectExpr, dimNames));
//			sourceTransformation.setCombinedTransformExpression(String.format("select *, %s from $.baseTable", buildParseExpr(requiredMetrics, dimNames)));
				tmp.setGlobalMetaManager(getGlobalMetaManager());
				tmp.setNonNullContext(getJobConfigurations());
				TopnTransformation finalTransform = new TopnTransformation();
				finalTransform.setN(1);
				finalTransform.setOrderByExpression(" ts desc ");
				finalTransform.setSelectExpression("`ts`,`dim_value`,`job`,`metric_value`,`track_id`,`unique_key`,`metric_name`,`ext`,`dim_name`," + buildParseDimExpr(dimNames));
				finalTransform.setGlobalMetaManager(getGlobalMetaManager());
				finalTransform.setInput(tmp);
				finalTransform.setPartitionExpression(TemplateSqlUtil.formatSelectField("metric_name, dim_value"));
				regSources.put(tableId, finalTransform);
				sourceTransformation = finalTransform;
			}
			String filterExpr = " metric_name ='" + groupName + "' AND " + buildNonNullFilter(dimNames);
			Transformation transform = addCalcExpression("*", filterExpr, sourceTransformation);
			if (!transform.validate()) {
				throw new RuntimeException("realtime index template source validate failed.");
			}
			sourceTransformations.add(transform);
		}

		Optional<Transformation> transformation;
		if (sourceTransformations.size() > 1) {
			logger.info("begin to build join chain for realtime index template.");
			transformation = sourceTransformations.stream().reduce((transformation1, transformation2) -> {
				UnionTransformation unionTransformation = new UnionTransformation();
				unionTransformation.setLeft(transformation1);
				unionTransformation.setRight(transformation2);

				if (!unionTransformation.validate()) {
					throw new RuntimeException("JoinTransformation 验证失败 : " + unionTransformation);
				}

				return unionTransformation;
			});

		} else {
			transformation = sourceTransformations.stream().findFirst();
		}

		if (!transformation.isPresent()) {
			throw new RuntimeException("realtime index template source gen a empty transform.");
		}

		MergeIndexTransformation mergeIndexTransformation = new MergeIndexTransformation();
		mergeIndexTransformation.setInput(transformation.get());
		mergeIndexTransformation.setGlobalMetaManager(globalMetaManager);
		mergeIndexTransformation.setDimNames(buildUdatfSelect(dimNames));
		mergeIndexTransformation.setFinalSelect(buildUdatfSelect(dimNames) + "," + buildUdatfSelect(metricNames));
		mergeIndexTransformation.setResultCols(buildUdatfOutputColList(metricNames));
		mergeIndexTransformation.setUdatfClass("com.alibaba.blink.udx.udaf.MergeAdsIndexWithMetricValue.class");

//		List<String> metricNameTransform = metricNames.stream().map(metric -> TemplateSqlUtil.wrapWithBackQuote(metric) + " as realtime_" + metric).collect(Collectors.toList());
//		String finalTransExpr = String.join(",", metricNameTransform) + "," + TemplateSqlUtil.formatSelectField(String.join(",", dimNames));
//
//		TopnTransformation finalTransform = new TopnTransformation();
//		finalTransform.setN(1);
//		finalTransform.setOrderByExpression(" proctime() desc ");
//		finalTransform.setSelectExpression(finalTransExpr);
//		finalTransform.setGlobalMetaManager(getGlobalMetaManager());
//		finalTransform.setInput(mergeIndexTransformation);
//		finalTransform.setPartitionExpression(TemplateSqlUtil.formatSelectField(String.join(",", dimNames)));

//		List<String> finalSelects = metricNames.stream().map(metric -> "realtime_" + metric).collect(Collectors.toList());
		GroupByTransformation groupByTransformation = new GroupByTransformation();
		String finalNonNullSelect = String.join(",", buildNonNullFormattedSelectExpr(metricNames)) + "," + TemplateSqlUtil.formatSelectField(String.join(",", dimNames));
		groupByTransformation.setSelectExpression(finalNonNullSelect);
		groupByTransformation.setInput(mergeIndexTransformation);
		groupByTransformation.setGroupByExpression(TemplateSqlUtil.formatSelectField(String.join(",", dimNames)));

		if (StringUtils.isNotBlank(extSql)) {
			// 解析配置
			SqlParser.Config parserConfig = getCurrentSqlParserConfig(SqlDialect.DEFAULT);
			if(!extSql.contains("$.baseTable")){
				throw new RuntimeException("only support to select from $.baseTable. use $.baseTable to replace the temp table pls.");
			}
			SqlParser parser = SqlParser.create(extSql.replace("$.baseTable", "testTable"), parserConfig);

			Set<String> finalDims = new HashSet<>();
			try {
				SqlNode sqlNode = parser.parseQuery();
				if (sqlNode instanceof SqlSelect) {
					if(!extSql.contains("$.baseTable")){
						SqlNode fromNode = ((SqlSelect) sqlNode).getFrom();
						throw new RuntimeException(String.format("use $.baseTable to replace the temp table pls. only support to select from $.baseTable but found %s. ", fromNode.toString()));
					}
					SqlNodeList selectList = ((SqlSelect) sqlNode).getSelectList();
					for (SqlNode sexpr : selectList.getList()) {
						logger.info("meet " + sexpr.toString() + " with "+ dimNames);
						for (String dim : dimNames) {
							if (sexpr.toString().contains("`" + dim + "`")) {
								String[] tmp = sexpr.toString().trim().split(" ");
								finalDims.add(tmp[tmp.length - 1]);
							}
						}
					}
				} else {
					throw new RuntimeException("Only enable select stmt in index template ext-sql.");
				}
				setSinkPrimaryKeys(String.join(",", finalDims));
			} catch (SqlParseException e) {
				throw new RuntimeException("not a valid select stmt in index template  ext-sql.");
			}
		} else {
			setSinkPrimaryKeys(dimNames.get(0));
		}
		Transformation sinkTransformation = generateSinkTransformation(groupByTransformation, extSql);
		if (!sinkTransformation.validate()) {
			return "SinkTransformation 验证失败";
		}
		jobConfigurations.put("MergeAdsTemplateJson.needKeys", String.join(",",metricNames));
		return buildStrWithSink(sinkTransformation);
	}

	private List<String> buildNonNullSelectExpr(List<String> expr) {
		List<String> rst = new ArrayList<>();
		for (String field : expr) {
			rst.add("LAST_VALUE(`" + field + "`) as `" + field + "`");
		}
		return rst;
	}

	private List<String> buildNonNullFormattedSelectExpr(List<String> expr) {
		List<String> rst = new ArrayList<>();
		for (String field : expr) {
			rst.add("IDXTPLLastNonNullFunction(`" + field + "`) as `realtime_" + field + "`");
		}
		return rst;
	}

	private static String wrapEmptyChar(String str) {
		return " " + str + " ";
	}

	private String buildUdatfSelect(List<String> cols) {
		return cols.stream().map(name -> "$(\"" + name + "\")").collect(Collectors.joining(","));
	}

	private String buildUdatfOutputColList(List<String> cols) {
		return cols.stream().map(name -> "\"" + name + "\"").collect(Collectors.joining(","));
	}

	private String buildParseDimExpr(List<String> dimNames) {
		List<String> selectColumns = new ArrayList<>();
		dimNames.forEach(dim -> selectColumns.add(String.format("JsonValue(dim_value,'%s') as `%s`", dim, dim)));
		return String.join(",", selectColumns);
	}

	private String buildNonNullFilter(List<String> dimNames) {
		List<String> selectColumns = new ArrayList<>();
		dimNames.forEach(dim -> selectColumns.add("`" + dim + "` is not null"));
		return "(" + String.join(" or ", selectColumns) + ")";
	}

	private String buildParseExpr(String dimNames) {
		List<String> selectColumns = new ArrayList<>();
		Arrays.stream(dimNames.split(",")).forEach(dim -> {
			selectColumns.add(String.format("JsonValue(dim_value,'%s') as `%s`", dim, dim));
		});
		return String.join(",", selectColumns);
	}

	private String buildJoinConditionExpr(String dimNames) {
		String[] dimArr = dimNames.split(",");
		Preconditions.checkArgument(StringUtils.isNotBlank(dimNames), "dimNames shall not be empty when building join condition for each metricGroups");
		return Arrays.stream(dimArr).map(dim -> "tl.`" + dim + "` = tr.`" + dim + "`").collect(Collectors.joining(" and "));
	}

	private String buildSelectExpr(String dimNames) {
		String[] dimArr = dimNames.split(",");
		Preconditions.checkArgument(StringUtils.isNotBlank(dimNames), "dimNames shall not be empty when building join select for each metricGroups");
		return Arrays.stream(dimArr).map(dim -> "tl.`" + dim + "`").collect(Collectors.joining(","));
	}

	/**
	 * a and b should not be blank
	 *
	 * @param a
	 * @param b
	 * @return
	 */
	public boolean arrEqual(String a, String b) {
		if (StringUtils.isBlank(a) || StringUtils.isBlank(b)) {
			return false;
		}
		Set<String> aArr = Arrays.stream(a.split(",")).collect(Collectors.toSet());
		Set<String> bArr = Arrays.stream(b.split(",")).collect(Collectors.toSet());
		return aArr.equals(bArr);
	}

}

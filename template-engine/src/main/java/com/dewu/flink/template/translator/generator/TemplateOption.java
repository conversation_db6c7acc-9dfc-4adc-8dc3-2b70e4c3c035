package com.dewu.flink.template.translator.generator;

import org.apache.flink.configuration.ConfigOption;

import static com.dewu.flink.template.translator.generator.Constants.EMPTY_STRING;
import static org.apache.flink.configuration.ConfigOptions.key;

/**
 * @Description: options for template
 * @author: zhiping.lin
 * @date: 2022/8/16
 */
public class TemplateOption {

	public static final ConfigOption<String> JOB_NAME =
			key("jobName")
					.stringType()
					.noDefaultValue()
					.withDescription(
							"the uniq name for each job.");

	public static final ConfigOption<String> SOURCE_TABLE_IDS =
			key("sourceTableIds")
					.stringType()
					.noDefaultValue()
					.withDescription(
							"the table ids for multi input scene.");


	public static final ConfigOption<String> TEMPLATE_JOB_ID =
			key("dynamic.rules.job.id")
					.stringType()
					.defaultValue("")
					.withDescription(
							"the job id for dynamic template.");


	public static final ConfigOption<String> DELAY_SOURCE_TABLES =
			key("delay.sources")
					.stringType()
					.noDefaultValue()
					.withDescription(
							"the table to be delayed.");

	public static final ConfigOption<String> SINK_TABLE_IDS =
			key("sinkTableIds")
					.stringType()
					.noDefaultValue()
					.withDescription(
							"the table ids for user-defined sql scene.");

	public static final ConfigOption<String> EXT_SELECT_STR =
			key("extSelectExpr")
					.stringType()
					.defaultValue("")
					.withDescription(
							"ext final select expr for sql.");

	public static final ConfigOption<String> SQL_TEMPLATE_MODE =
			key("sqlTemplateMode")
					.stringType()
					.defaultValue("b2s")
					.withDescription(
							"mode for sql template: b2s, rule");

	public static final ConfigOption<String> TABLE_VIEW_NAME =
			key("tableViewName")
					.stringType()
					.defaultValue("{}")
					.withDescription(
							"source table view names.");

	public static final ConfigOption<String> DDL_MAPPINGS =
			key("ddlMappings")
					.stringType()
					.defaultValue("{}")
					.withDescription(
							"ddl mappings.");

	public static final ConfigOption<String> SQL =
			key("sql")
					.stringType()
					.noDefaultValue()
					.withDescription(
							"the raw sql for user-defined sql scene.");

	public static final ConfigOption<String> EXT_SQL =
			key("extSql")
					.stringType()
					.defaultValue("")
					.withDescription(
							"the ext sql for template final result table.");

	public static final ConfigOption<String> LEFT_TABLE_ID =
			key("leftTableId")
					.stringType()
					.noDefaultValue()
					.withDescription(
							"the left table id for two input scene.");


	public static final ConfigOption<String> DIM_IDS =
			key("dimIDs")
					.stringType()
					.noDefaultValue()
					.withDescription(
							"the ids for dims");


	public static final ConfigOption<String> METRIC_IDS =
			key("metricIDs")
					.stringType()
					.noDefaultValue()
					.withDescription(
							"the ids for metrics.");


	public static final ConfigOption<String> TABLE_ID =
			key("tableId")
					.stringType()
					.noDefaultValue()
					.withDescription(
							"the uniq table id.");

	public static final ConfigOption<String> RIGHT_TABLE_ID =
			key("rightTableId")
					.stringType()
					.noDefaultValue()
					.withDescription(
							"the right table id for two input scene.");

	public static final ConfigOption<String> MODULE_NAME =
			key("moduleName")
					.stringType()
					.noDefaultValue()
					.withDescription(
							"the uniq name for a module which has been defined in platform.");


	public static final ConfigOption<String> SINK_SCHEMA_PRIMARY_KEY =
			key("sinkSchemaPrimaryKey")
					.stringType()
					.noDefaultValue()
					.withDescription(
							"the primary key for a sink which affects retract result.");

	public static final ConfigOption<String> JOIN_CONDITION =
			key("joinCondition")
					.stringType()
					.noDefaultValue()
					.withDescription(
							"condition expr for a common join.");

	public static final ConfigOption<String> JOIN_FILTER =
			key("joinFilter")
					.stringType()
					.defaultValue(EMPTY_STRING)
					.withDescription(
							"filter after a join.");

	public static final ConfigOption<String> AGG_FILTER =
			key("aggFilter")
					.stringType()
					.defaultValue(EMPTY_STRING)
					.withDescription(
							"filter after a join.");

	public static final ConfigOption<String> LEFT_TABLE_FILTER =
			key("leftTableFilter")
					.stringType()
					.defaultValue(EMPTY_STRING)
					.withDescription(
							"filter after a join.");

	public static final ConfigOption<String> RIGHT_TABLE_FILTER =
			key("rightTableFilter")
					.stringType()
					.defaultValue(EMPTY_STRING)
					.withDescription(
							"filter after a join.");

	public static final ConfigOption<String> JOIN_TYPE =
			key("joinType")
					.stringType().noDefaultValue()
					.withDescription(
							"join type : INNER JOIN, LEFT JOIN ...");

	public static final ConfigOption<String> SELECT_EXPR =
			key("selectExpr")
					.stringType()
					.defaultValue("")
					.withDescription(
							"select expressions defines which fields will be inserted into sink.");

	public static final ConfigOption<String> RAW_FLINK_SQL =
			key("flink.sql.raw.enable")
					.stringType()
					.defaultValue("false")
					.withDescription(
							"if use raw flink sql");

	public static final ConfigOption<Long> CACHE_TTL =
			key("cacheTTL")
					.longType()
					.defaultValue(86400000L)
					.withDescription(
							"job state ttl(ms).");

	public static final ConfigOption<Integer> DELAY_MINUTES =
			key("delayMinutes")
					.intType()
					.defaultValue(0)
					.withDescription(
							"delay minutes");

	public static final ConfigOption<Boolean> JOIN_KEY_DUPLICATE =
			key("joinKeyDuplicate")
					.booleanType()
					.defaultValue(false)
					.withDescription(
							"job state ttl(ms).");

	public static final ConfigOption<String> SINK_TABLE_ID =
			key("sinkTableId")
					.stringType()
					.noDefaultValue()
					.withDescription(
							"sink table.");

	public static final ConfigOption<String> GROUP_BY_EXPR =
			key("groupByExpr")
					.stringType()
					.noDefaultValue()
					.withDescription(
							"group by expr for agg.");

	public static final ConfigOption<String> SOURCE_COMBINED_TRANSFORM_EXPR =
			key("combinedTransSql")
					.stringType()
					.defaultValue(EMPTY_STRING)
					.withDescription(
							"user define sql fot Source .");

	public static final ConfigOption<String> LEFT_COMBINED_TRANSFORM_EXPR =
			key("leftCombinedTransSql")
					.stringType()
					.noDefaultValue()
					.withDescription(
							"user define sql fot JoinTemplate left Source .");

	public static final ConfigOption<String> RIGHT_COMBINED_TRANSFORM_EXPR =
			key("rightCombinedTransSql")
					.stringType()
					.noDefaultValue()
					.withDescription(
							"user define sql fot JoinTemplate right Source .");


	public static final ConfigOption<String> HAVING_EXPR =
			key("havingExpr")
					.stringType()
					.defaultValue(EMPTY_STRING)
					.withDescription(
							"having expr for agg.");

	public static final ConfigOption<String> TOPN_PRE_FILTER =
			key("topNPreFilter")
					.stringType()
					.defaultValue(EMPTY_STRING)
					.withDescription(
							"source -> filter -> topn.");

	public static final ConfigOption<String> PARTITION_EXPR =
			key("partitionExpr")
					.stringType()
					.noDefaultValue()
					.withDescription(
							"partition by expr for topn.");

	public static final ConfigOption<String> ORDER_BY_EXPR =
			key("orderByExpr")
					.stringType()
					.noDefaultValue()
					.withDescription(
							"order by expr.");

	public static final ConfigOption<Integer> TOPN_NUM =
			key("topNum")
					.intType()
					.defaultValue(1)
					.withDescription(
							"topN num");

	public static final ConfigOption<String> SOURCE_TABLE_ID =
			key("sourceTableId")
					.stringType()
					.noDefaultValue()
					.withDescription(
							"single source table id");

	public static final ConfigOption<String> DELAY_TABLES =
			key("delay.tables")
					.stringType()
					.noDefaultValue()
					.withDescription(
							"stream sources to be delayed.");

	public static final ConfigOption<String> JOB_EXTRA_EXTEND_CODE =
			key("jobExtraExtendCode")
					.stringType()
					.noDefaultValue()
					.withDescription(
							"job contains sub jobs type code");

	public static final ConfigOption<String> JOB_STATUS =
			key("status")
					.stringType()
					.noDefaultValue()
					.withDescription(
							"job contains sub jobs type code");

	public static final ConfigOption<String> SUB_SINK_TABLE_IDS =
			key("subSinkTableIds")
					.stringType()
					.noDefaultValue()
					.withDescription(
							"job contains sub jobs type code");

	public static final ConfigOption<Boolean> SUB_TASK_ENABLE =
			key("subTaskEnable")
					.booleanType()
					.defaultValue(false)
					.withDescription(
							"whether enable sub task");

	public static final ConfigOption<String> MUST_BE_EXIST_COLUMNS =
			key("mustBeExistColumns")
					.stringType()
					.noDefaultValue()
					.withDescription(
							"usr specify must be exist columns");


	public static final ConfigOption<String> OPTION_CONFIGS =
			key("optionConfigs")
					.stringType()
					.defaultValue("")
					.withDescription(
							"option configs.");


	public static final ConfigOption<String> DATA_MODIFY_TIME =
			key("dataModifyTime")
					.stringType()
					.defaultValue("")
					.withDescription(
							"data source produce time.");


}

package com.dewu.flink.template.template.b2s;

import com.dewu.flink.template.template.Template;
import org.apache.calcite.sql.SqlNode;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


public abstract class SqlTemplate implements Template {

	Map<String, String> tableMeta;
	Map<String, String> sinkPrimaryKey;
	String sql;
	String extSelectExpr="";

	public static String getString(SqlNode sqlNode) {
		return null == sqlNode ? "" : sqlNode.toString();
	}

	public SqlTemplate(Map<String, String> tableMeta, Map<String, String> sinkPrimaryKey, String sql, String extSelectExpr) {
		this.tableMeta = tableMeta;
		this.sinkPrimaryKey = null == sinkPrimaryKey ? new HashMap<>() : sinkPrimaryKey;
		this.sql = sql;
		this.extSelectExpr = extSelectExpr;
	}
}

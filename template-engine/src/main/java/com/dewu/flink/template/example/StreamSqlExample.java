/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.dewu.flink.template.example;

import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.flink.table.api.EnvironmentSettings;
import org.apache.flink.table.api.Table;
import org.apache.flink.table.api.bridge.java.StreamTableEnvironment;

import java.util.Arrays;
import java.util.Objects;

import static org.apache.flink.table.api.Expressions.$;


public class StreamSqlExample {

    // *************************************************************************
    //     PROGRAM
    // *************************************************************************

    public static void main(String[] args) throws Exception {

        final ParameterTool params = ParameterTool.fromArgs(args);
        String planner = params.has("planner") ? params.get("planner") : "blink";

        // set up execution environment
        StreamExecutionEnvironment env = StreamExecutionEnvironment.getExecutionEnvironment();
        StreamTableEnvironment tEnv;
        if (Objects.equals(planner, "blink")) { // use blink planner in streaming mode
            EnvironmentSettings settings =
                    EnvironmentSettings.newInstance().inStreamingMode().useBlinkPlanner().build();
            tEnv = StreamTableEnvironment.create(env, settings);
        } else if (Objects.equals(planner, "flink")) { // use flink planner in streaming mode
            EnvironmentSettings settings =
                    EnvironmentSettings.newInstance().inStreamingMode().useOldPlanner().build();
            tEnv = StreamTableEnvironment.create(env, settings);
        } else {
            System.err.println(
                    "The planner is incorrect. Please run 'StreamSQLExample --planner <planner>', "
                            + "where planner (it is either flink or blink, and the default is blink) indicates whether the "
                            + "example uses flink planner or blink planner.");
            return;
        }

        tEnv.executeSql("CREATE TABLE `source_dw_ord_sub_order` (\n" +
                "  `messageKey`  VARBINARY,\n" +
                "  `message`  VARBINARY,\n" +
                "  `topic`  VARCHAR METADATA VIRTUAL,\n" +
                "  `partition`  INTEGER METADATA VIRTUAL,\n" +
                "  `offset`  BIGINT METADATA VIRTUAL\n" +
                ") WITH (\n" +
                "  'connector' = 'kafka',\n" +
                "  'topic' = 'dwd_ord_sub_order_extend',\n" +
                "  'properties.group.id' = 'dwd_sub_order_extend_refund_info_libra',\n" +
                "  'properties.bootstrap.servers' = '10.252.41.249:9092,10.252.41.252:9092,10.252.41.251:9092,10.252.41.250:9092',\n" +
                "  'scan.startup.mode' = 'latest-offset',\n" +
                "  'scan.topic-partition-discovery.interval' = '30 s',\n" +
                "  'key.format' = 'raw',\n" +
                "  'key.fields' = 'messageKey',\n" +
                "  'value.format' = 'raw',\n" +
                "  'value.fields-include' = 'EXCEPT_KEY'\n" +
                ")");

        tEnv.executeSql("CREATE VIEW dw_ord_sub_order AS\n" +
                "SELECT\n" +
                "    cast (`message` as varchar) as sub_order_no,\n" +
                "    cast (`message` as varchar) as sub_order_status,\n" +
                "    cast (`message` as varchar) as buyer_user_id\n" +
                "from source_dw_ord_sub_order");

        tEnv.executeSql("CREATE TABLE `dw_ord_sub_order_print` (\n" +
                "  `sub_order_no`  VARCHAR,\n" +
                "  `sub_order_status`  VARCHAR,\n" +
                "  buyer_user_id VARCHAR,\n" +
                "  PRIMARY KEY (`sub_order_no`) NOT ENFORCED\n" +
                ") WITH (\n" +
                "  'connector' = 'print'\n" +
                ")");

        tEnv.executeSql("insert\n" +
                "  into dw_ord_sub_order_print\n" +
                "select\n" +
                "  sub_order_no,\n" +
                "  sub_order_status,\n" +
                "  buyer_user_id\n" +
                "from\n" +
                "  dw_ord_sub_order");

//        tEnv.createTemporaryTable("kafka_sink_order", TableDescriptor.forConnector("kafka")
//                .schema(Schema.newBuilder()
//                        .column("userid", DataTypes.BIGINT())
//                        .column("product",DataTypes.STRING())
//                        .column("amount",DataTypes.BIGINT())
//                        .build())
//                .option(KafkaConnectorOptions.PROPS_BOOTSTRAP_SERVERS, "***********:9092")
//                .option(KafkaConnectorOptions.TOPIC.key(), "hulin_test")
//                .option(KafkaConnectorOptions.VALUE_FORMAT,"json")
//                .build());

    }

    // *************************************************************************
    //     USER DATA TYPES
    // *************************************************************************

    /** Simple POJO. */
    public static class Order {
        public Long userid;
        public String product;
        public int amount;

        public Order() {}

        public Order(Long userid, String product, int amount) {
            this.userid = userid;
            this.product = product;
            this.amount = amount;
        }

        @Override
        public String toString() {
            return "Order{"
                    + "userid="
                    + userid
                    + ", product='"
                    + product
                    + '\''
                    + ", amount="
                    + amount
                    + '}';
        }
    }
    public static class Product {
        public String product;
        public int price;
        public String color;

        public Product() {}

        public Product(String product, int price,String color) {
            this.product = product;
            this.price = price;
            this.color = color;
        }

        @Override
        public String toString() {
            return "Product{"
                    + "product="
                    + product
                    + ", price='"
                    + price
                    + '\''
                    + ", color="
                    + color
                    + '}';
        }
    }
}

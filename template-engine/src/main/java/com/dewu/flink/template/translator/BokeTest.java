package com.dewu.flink.template.translator;

import lombok.Data;
import lombok.ToString;

import java.util.ArrayList;
import java.util.List;
import java.util.Random;

@Data
@ToString
 class User implements Cloneable{
	private Long id;
	private String name;

	//实现浅拷贝
	@Override
	protected User clone() throws CloneNotSupportedException {
		return (User) super.clone();
	}
}


/**
 * <p>Title:用户对象生成工厂</p>
 * <p>Description:</p>
 *
 * <AUTHOR>
 * @date
 */
 class UserFactory {
	//只需要new一次对象
	private static User userProtoType = new User();

	public static User getInstance() throws CloneNotSupportedException {
		User user = userProtoType.clone();
		return user;
	}
}


public class BokeTest {
	public static void main(String[] args) throws CloneNotSupportedException {
		List<User> list = new ArrayList<>();
		for(;;){
			User userX = UserFactory.getInstance();
			userX.setId( new Random().nextLong() );
			userX.setName( "xxx" );
			list.add( userX );
		}
	}
}

package com.dewu.flink.template.example;

import com.dewu.annotation.AnonymousAccess;
import com.dewu.annotation.Log;
import com.dewu.flink.template.translator.generator.TableCodeGenerator;
import com.dewu.flink.template.translator.generator.TemplateCodeGenerator;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.Map;
import java.util.UUID;


/**
 * <AUTHOR>
 * @date 2022-07-26
 **/
@RestController
@RequiredArgsConstructor
@Api(tags = "job管理")
@RequestMapping("/api/codeGenerateTest")
public class TableCodeGenerateController {
//
//	@Autowired
//	private final TableCodeGenerator tableCodeGenerator;
//	@Autowired
//	private final TemplateCodeGenerator templateCodeGenerator;
//
//
//	@GetMapping("/operation")
//	@Log("根据 operation json 生成 job")
//	@ApiOperation("根据 operation json 生成 job")
////    @PreAuthorize("@el.check('templateEngineJobInfo:list')")
//	@AnonymousAccess
//	public ResponseEntity<Object> generateJobFromOperation(String name) throws IOException {
//		String json = "{\"transformationWrappers\":[{\"inputIds\":[1],\"name\":\"SinkTransformation\",\"id\":0,\"transformation\":\"{\\\"schemaConfig\\\":{\\\"primaryKey\\\":\\\"pt\\\"},\\\"connectConfig\\\":{\\\"tableId\\\":\\\"1199\\\"}}\"},{\"inputIds\":[2],\"name\":\"CalcTransformation\",\"id\":1,\"transformation\":\"{\\\"filterExpression\\\":\\\"\\\",\\\"selectExpression\\\":\\\"`product` AS `key`, CAST(`price` AS BIGINT) AS `value`, CAST('20220919' AS VARCHAR) AS `pt`\\\"}\"},{\"inputIds\":[],\"name\":\"SourceTransformation\",\"id\":2,\"transformation\":\"{\\\"connectConfig\\\":{\\\"tableId\\\":\\\"890\\\"}}\"}],\"optionConfigs\":\"\"}";
//		return new ResponseEntity<>(tableCodeGenerator.generate(json, "Test", null, UUID.randomUUID().toString()), HttpStatus.OK);
//	}
//
//	@PostMapping("/template")
//	@Log("根据 template json 生成 job")
//	@ApiOperation("根据 template json 生成 job")
////    @PreAuthorize("@el.check('templateEngineJobInfo:list')")
//	@AnonymousAccess
//	public ResponseEntity<Object> generateJobFromTemplate(@RequestBody String json) throws IOException {
////		json = "{\"name\":\"GroupByTemplate\",\"template\":\"{\\\"sinkConnectConfig\\\":{\\\"connector\\\":\\\"upsert-kafka\\\",\\\"tableId\\\":\\\"872\\\"},\\\"sinkSchemaConfig\\\":{\\\"columns\\\":null,\\\"primaryKey\\\":[\\\"userid\\\"]},\\\"sourceConnectConfig\\\":{\\\"connector\\\":\\\"kafka\\\",\\\"tableId\\\":\\\"340\\\"},\\\"filterExpression\\\":\\\"userid=1\\\",\\\"selectExpression\\\":\\\"userid,max(product) as product,max(amount) as amount\\\",\\\"havingExpression\\\":\\\"amount=4\\\",\\\"groupByExpression\\\":\\\"userid\\\"}\"}\n";
//		return new ResponseEntity<>(templateCodeGenerator.generate(json, "Test", null, UUID.randomUUID().toString()), HttpStatus.OK);
//	}
}
package com.dewu.flink.template.transform;

import com.dewu.flink.template.config.SchemaConfig;
import com.dewu.flink.template.meta.GlobalMetaBaseDomain;
import com.dewu.flink.template.utils.TemplateSchemeType;
import com.dewu.flink.template.utils.TemplateSqlUtil;
import com.google.common.base.Joiner;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.annotation.JsonIgnore;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import org.apache.flink.shaded.netty4.io.netty.util.internal.StringUtil;
import org.apache.flink.util.CollectionUtil;

import java.io.IOException;
import java.io.StringWriter;
import java.util.*;

import static com.dewu.flink.template.translator.generator.LibraConnectorType.C_HBASE;
import static com.dewu.flink.template.utils.JsonUtil.getJsonValues;

@JsonIgnoreProperties(ignoreUnknown = true)
public class LookupJoinTransformation extends OneInputTransformation {
	private String joinConditionExpression;
	private String selectExpression;
	private Properties connectConfig;
	private SchemaConfig schemaConfig;
	private String joinType = "inner";
	private String leftViewAlias = "tl";
	private String rightViewAlias = "rl";
	private boolean isDataDrill = false;
	private List<String> drillData = new ArrayList<>();

	private String viewName = "view_" + UUID.randomUUID().toString().replace("-", "");
	@JsonIgnore
	private String tableName = "lookup_table_" + UUID.randomUUID().toString().replace("-", "");

	public LookupJoinTransformation() {
	}

	@Override
	public Boolean isFieldNeed(String fieldName) {
		return selectExpression.toLowerCase().contains(fieldName.toLowerCase()) || joinConditionExpression.toLowerCase().contains(fieldName.toLowerCase());
	}

	public Properties getConnectConfig() {
		return connectConfig;
	}

	public SchemaConfig getSchemaConfig() {
		return schemaConfig;
	}

	public String getJoinConditionExpression() {
		return joinConditionExpression;
	}

	public String getSelectExpression() {
		return selectExpression;
	}

	public String getJoinType() {
		return joinType;
	}

	public String getLeftViewAlias() {
		return leftViewAlias;
	}

	public String getRightViewAlias() {
		return rightViewAlias;
	}

	public void setConnectConfig(Properties connectConfig) {
		this.connectConfig = connectConfig;
	}

	public void setSchemaConfig(SchemaConfig schemaConfig) {
		this.schemaConfig = schemaConfig;
	}

	public void setJoinConditionExpression(String joinKeyExpression) {
		this.joinConditionExpression = joinKeyExpression;
	}

	public void setSelectExpression(String selectExpression) {
		this.selectExpression = selectExpression;
	}

	public void setJoinType(String joinType) {
		this.joinType = joinType;
	}

	public void setLeftViewAlias(String leftViewAlias) {
		this.leftViewAlias = leftViewAlias;
	}

	public void setRightViewAlias(String rightViewAlias) {
		this.rightViewAlias = rightViewAlias;
	}

	public void setDataDrill(boolean dataDrill) {
		isDataDrill = dataDrill;
	}


	public void setDrillData(List<String> drillData) {
		this.drillData = drillData;
	}

	public void setViewName(String viewName) {
		this.viewName = viewName;
	}

	public void setTableName(String tableName) {
		this.tableName = tableName;
	}

	@Override
	public String transformStr(StringWriter writer) throws IOException {
		if (getResultFunction() != null) {
			return getResultFunction();
		}
		String leftFunction = this.getInput().transformStr(writer);
		setResultFunction("lookup_join_" + UUID.randomUUID().toString().replace("-", ""));
		if (schemaConfig == null) {
			schemaConfig = new SchemaConfig();
			schemaConfig.setPrimaryKey(TemplateSqlUtil.extractFieldStrFromJoinExpression(wrapWithEmptyStr(joinConditionExpression.replaceAll("\\s*=\\s*", "=")
				.replaceAll("\\s*\\)\\s*", ")")
				.replaceAll("\\s*\\(\\s*", "(")
				.replaceAll("`", "")), "tl\\.(\\S+?)=|=tl\\.(\\S+?)[\\s|\\)]".replace("tl", leftViewAlias)));
		} else if (StringUtil.isNullOrEmpty(schemaConfig.getPrimaryKey())) {
			schemaConfig.setPrimaryKey(TemplateSqlUtil.extractFieldStrFromJoinExpression(wrapWithEmptyStr(joinConditionExpression.replaceAll("\\s*=\\s*", "=")
				.replaceAll("\\s*\\)\\s*", ")")
				.replaceAll("\\s*\\(\\s*", "(")
				.replaceAll("`", "")), "tr\\.(\\S+?)=|=tr\\.(\\S+?)[\\s|\\)]".replace("tr", rightViewAlias)));
		}
		GlobalMetaBaseDomain globalMetaBaseDomain = getGlobalMetaManager().getTableMetaByTableId(Long.valueOf(connectConfig.getProperty("tableId")));
		updateSchemaConfig(connectConfig, schemaConfig, globalMetaBaseDomain);
		updateConnectConfig(connectConfig, globalMetaBaseDomain, LookupJoinTransformation.class);
		connectConfig.remove("tableId");
		TemplateSchemeType schemeType = null;
		if (connectConfig.get("connector").equals(C_HBASE.getName())) {
			schemeType = TemplateSchemeType.HBASE;
			schemaConfig.getExtInfo().put("cf", globalMetaBaseDomain.getReDataSourceMetadataDto().getColumnFamily());
		} else {
			schemeType = TemplateSchemeType.NORMAL;
		}

		String schema;
		if (isDataDrill && Objects.nonNull(drillData)) {
			schema = TemplateSqlUtil.generateDataDrillColumnStr(schemaConfig, false, drillData);
		} else {
			schema = TemplateSqlUtil.generateColumnStr(schemaConfig, schemeType, false);
		}
		if (isDataDrill && !CollectionUtil.isNullOrEmpty(drillData)) {
			String drillDataString = Joiner.on(",").join(drillData.stream().map(data -> "Row.of(" + getJsonValues(data) + ")").toArray());
			writer.write(TemplateSqlUtil.readTemplate("LookupJoinDataDrill.template")
				.replace("#transformFunction", getResultFunction())
				.replace("#leftFunction", leftFunction)
				.replace("#viewName", viewName)
				.replace("#columnStr", TemplateSqlUtil.generateColumnStr(schemaConfig, schemeType, false))
				.replace("#primaryKeyStr", TemplateSqlUtil.generatePrimaryKeyStr(schemaConfig, schemeType))
				.replace("#tableName", tableName)
				.replace("#config", TemplateSqlUtil.generateConfigStr(connectConfig))
				.replace("#joinType", joinType)
				.replace("#selectExpression", selectExpression)
				.replace("#joinConditionExpression", joinConditionExpression)
				.replace("#leftViewAlias", leftViewAlias)
				.replace("#rightViewAlias", rightViewAlias)
				.replace("#schema", schema)
				.replace("#drillData", drillDataString)
			);
		} else {
			writer.write(TemplateSqlUtil.readTemplate("LookupJoin.template")
				.replace("#transformFunction", getResultFunction())
				.replace("#leftFunction", leftFunction)
				.replace("#viewName", viewName)
				.replace("#columnStr", schema)
				.replace("#primaryKeyStr", TemplateSqlUtil.generatePrimaryKeyStr(schemaConfig, schemeType))
				.replace("#tableName", tableName)
				.replace("#config", TemplateSqlUtil.generateConfigStr(connectConfig))
				.replace("#joinType", joinType)
				.replace("#selectExpression", selectExpression)
				.replace("#joinConditionExpression", joinConditionExpression)
				.replace("#leftViewAlias", leftViewAlias)
				.replace("#rightViewAlias", rightViewAlias)
			);
		}
		return getResultFunction();
	}

	@Override
	public Boolean validate() {
		if (StringUtil.isNullOrEmpty(joinConditionExpression)) {
			return false;
		}
		if (StringUtil.isNullOrEmpty(selectExpression)) {
			return false;
		}
		if (connectConfig == null || StringUtil.isNullOrEmpty(connectConfig.getProperty("connector")) || StringUtil.isNullOrEmpty(connectConfig.getProperty("tableId"))) {
			return false;
		}
		if (!"inner".equals(joinType.toLowerCase().replace(" ", "")) && !"".equals(joinType.toLowerCase().replace(" ", "")) && !"left".equals(joinType.toLowerCase().replace(" ", ""))) {
			return false;
		}
		return true;
	}


	private static String wrapWithEmptyStr(String str) {
		return " " + str + " ";
	}

//    private String[] extractPrimaryKeyFromJoinExpression(String joinExpression){
//        Pattern p = Pattern.compile("r\\..*? |r\\..*?\\)|r\\..*?$");
//        Matcher m = p.matcher(joinExpression);
//        Set<String> primaryKeySet = new HashSet<String>();
//        while (m.find()){
//            primaryKeySet.add(m.group().replace(" ","").replace(")","").replace("r.",""));
//        }
//        String[] primaryKey = new String[primaryKeySet.size()];
//        return primaryKeySet.toArray(primaryKey);
//    }

}

package com.dewu.flink.template.transform;

import com.dewu.flink.template.utils.TemplateSqlUtil;
import com.google.common.base.Joiner;
import org.apache.flink.api.java.tuple.Tuple2;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.annotation.JsonIgnore;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.io.StringWriter;
import java.util.*;
import java.util.stream.Collectors;

import static com.dewu.flink.template.translator.generator.Constants.VIEW_PREFIX;
import static com.dewu.flink.template.utils.TemplateSqlUtil.wrapWithBackQuote;

@JsonIgnoreProperties(ignoreUnknown = true)
public class RawFlinkSqlTransformation extends DynamicInputTransformation {
	private List<String> sqls;
	@JsonIgnore
	private String viewName = "view_" + UUID.randomUUID().toString().replace("-", "");
	private static final Logger logger = LoggerFactory.getLogger(RawFlinkSqlTransformation.class);
	private String lastView = "";

	public RawFlinkSqlTransformation() {
	}

	public List<String> getSqls() {
		return sqls;
	}

	public void setSqls(List<String> sqls) {
		this.sqls = sqls;
	}

	public void setLastView(String lastView) {
		this.lastView = lastView;
	}

	public String getViewName() {
		return viewName;
	}

	public void setViewName(String viewName) {
		this.viewName = viewName;
	}

	@Override
	public Boolean isFieldNeed(String fieldName) {
		return null;
	}

	@Override
	public String transformStr(StringWriter writer) throws IOException {
		long startTime = System.currentTimeMillis();
		if (getResultFunction() != null) {
			return getResultFunction();
		}
		LinkedHashMap<String, String> inputFunctions = new LinkedHashMap<>();
		for (Map.Entry<String, Transformation> entry : getInputs().entrySet()) {
			inputFunctions.put(wrapWithBackQuote(VIEW_PREFIX + entry.getKey()), entry.getValue().transformStr(writer));
		}

		String tableReg = buildTableReg(inputFunctions);

		setResultFunction("sql_" + UUID.randomUUID().toString().replace("-", ""));

		writer.write(TemplateSqlUtil.readTemplate("RawFlinkSql.template")
				.replace("#transformFunction", getResultFunction())
				.replace("#tableReg", tableReg)
//                .replace("#calcExpression",calcExpression));
				.replace("#viewName", viewName)
				.replace("#lastView", lastView)
				.replace("#sqlQuery", buildSqlQuery(sqls))
		);
		long endTime = System.currentTimeMillis();
		logger.info("transform rawFlinkSqlTransformation takes about " + (endTime - startTime) + " ms.");
		return getResultFunction();
	}

	private String buildSqlQuery(List<String> sqls) {
		List<String> collect = sqls.stream().map(sql -> String.format("tEnv.executeSql(\"%s\");", sql)).collect(Collectors.toList());
		return String.join("\n", collect);
	}

	private String buildTableReg(Map<String, String> inputFunctions) {
		StringBuilder stringBuilder = new StringBuilder("        Table retTable = null;\n");
		String format =
//			"        tEnv.createTemporaryView(\"#viewName\",#inputFunction(tEnv));\n";
			"        retTable = #inputFunction(tEnv);\n";
		inputFunctions.forEach((key, value) -> stringBuilder.append(format.replace("#viewName", key).replace("#inputFunction", value)));
		return stringBuilder.toString();
	}

	@Override
	public Boolean validate() {
		return true;
	}
}

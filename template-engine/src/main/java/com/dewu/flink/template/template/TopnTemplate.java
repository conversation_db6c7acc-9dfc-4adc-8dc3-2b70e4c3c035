package com.dewu.flink.template.template;

import com.dewu.flink.template.transform.*;
import com.dewu.flink.template.translator.generator.ParamTool;
import com.google.common.collect.Sets;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.flink.configuration.ConfigOption;

import java.io.IOException;
import java.util.Map;
import java.util.Properties;
import java.util.Set;

import static com.dewu.flink.template.translator.generator.TemplateOption.*;

@EqualsAndHashCode(callSuper = true)
@Data
public class TopnTemplate extends OneInputTemplate {

	private final static Set<ConfigOption<?>> mustOpts = Sets.newHashSet(
		SOURCE_TABLE_ID,
		SINK_TABLE_ID,
		SELECT_EXPR,
		PARTITION_EXPR,
		ORDER_BY_EXPR
	);


	private String partitionExpression;
	private String orderByExpression;


	private Integer n;

	public static TopnTemplate apply(Map<String, String> params, Properties jobConfigs, String jobName) {
		ParamTool tool = ParamTool.apply(params);
		tool.checkNonNullParam(mustOpts);
		TopnTemplate topnTemplate = new TopnTemplate();
		topnTemplate.setJobName(jobName);
		topnTemplate.setSourceTableId(tool.getString(SOURCE_TABLE_ID));
		topnTemplate.setSinkTableId(tool.getString(SINK_TABLE_ID));
		topnTemplate.setSinkPrimaryKeys(tool.getString(SINK_SCHEMA_PRIMARY_KEY).replaceAll("\\s+", ""));
		topnTemplate.setSourceCombinedExpression(tool.getString(SOURCE_COMBINED_TRANSFORM_EXPR));
		topnTemplate.setSelectExpression(tool.getString(SELECT_EXPR));
		topnTemplate.setPartitionExpression(tool.getString(PARTITION_EXPR));
		topnTemplate.setOrderByExpression(tool.getString(ORDER_BY_EXPR));
		topnTemplate.setN(tool.getInteger(TOPN_NUM));
		topnTemplate.setFilterExpression(tool.getString(TOPN_PRE_FILTER));
		topnTemplate.setJobConfigurations(jobConfigs);

		return topnTemplate;
	}

	@Override
	public String generateTransformationStr() throws IOException {

		SourceTransformation sourceTransformation = generateSourceTransformation();
		if (!sourceTransformation.validate()) {
			return "SourceTransformation 验证失败";
		}

		TopnTransformation topnTransformation = new TopnTransformation();
		topnTransformation.setInput(addCalcExpression(null, getFilterExpression(), sourceTransformation));
		topnTransformation.setPartitionExpression(partitionExpression);
		topnTransformation.setOrderByExpression(orderByExpression);
		topnTransformation.setSelectExpression(getSelectExpression());
		topnTransformation.setN(n);
		if (!topnTransformation.validate()) {
			return "TopnTransformation 验证失败";
		}

		Transformation sinkTransformation = generateSinkTransformation(topnTransformation);
		if (!sinkTransformation.validate()) {
			return "SinkTransformation 验证失败";
		}
		return buildStrWithSink(sinkTransformation);
	}
}

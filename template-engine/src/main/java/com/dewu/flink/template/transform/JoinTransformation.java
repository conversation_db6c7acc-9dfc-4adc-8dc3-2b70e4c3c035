package com.dewu.flink.template.transform;

import com.dewu.flink.template.utils.TemplateSqlUtil;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import org.apache.flink.shaded.netty4.io.netty.util.internal.StringUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.io.StringWriter;
import java.util.UUID;

@JsonIgnoreProperties(ignoreUnknown = true)
public class JoinTransformation extends TwoInputTransformation {
	private static final Logger log = LoggerFactory.getLogger(JoinTransformation.class);
	private String joinType = "inner";
	private Boolean isDeduplicateByJoinKey = false;
	private String joinConditionExpression;
	private String selectExpression;
	private String leftInputView = "left_input_view_" + UUID.randomUUID().toString().replace("-", "");
	private String rightInputView = "right_input_view_" + UUID.randomUUID().toString().replace("-", "");
	private String leftDeduplicateView = "left_deduplicate_view_" + UUID.randomUUID().toString().replace("-", "");
	private String rightDeduplicateView = "right_deduplicate_view_" + UUID.randomUUID().toString().replace("-", "");
	private String leftViewAlias = "tl";
	private String rightViewAlias = "tr";

	public JoinTransformation() {
	}

	@Override
	public Boolean isFieldNeed(String fieldName) {
		return null;
	}

	public String getJoinType() {
		return joinType;
	}

	public String getJoinConditionExpression() {
		return joinConditionExpression;
	}

	public String getSelectExpression() {
		return selectExpression;
	}

	public void setDeduplicateByJoinKey(Boolean deduplicateByJoinKey) {
		isDeduplicateByJoinKey = deduplicateByJoinKey;
	}

	public Boolean getIsDeduplicateByJoinKey() {
		return isDeduplicateByJoinKey;
	}

	public String getLeftViewAlias() {
		return leftViewAlias;
	}

	public String getRightViewAlias() {
		return rightViewAlias;
	}

	public void setJoinType(String joinType) {
		this.joinType = joinType;
	}

	public void setJoinConditionExpression(String joinConditionExpression) {
		this.joinConditionExpression = joinConditionExpression;
	}

	public void setSelectExpression(String selectExpression) {
		this.selectExpression = selectExpression;
	}

	public void setIsDeduplicateByJoinKey(Boolean deduplicateByJoinKey) {
		this.isDeduplicateByJoinKey = deduplicateByJoinKey;
	}

	public void setLeftViewAlias(String leftViewAlias) {
		this.leftViewAlias = leftViewAlias;
	}

	public void setRightViewAlias(String rightViewAlias) {
		this.rightViewAlias = rightViewAlias;
	}

	public static void main(String[] args) {
		//tl\.(.*)?+=
		//=tl\.(\S*)[\s|\)]
		System.out.println(TemplateSqlUtil.extractFieldStrFromJoinExpression(
//			"(tl.id= tr.name) and ( tr.tt =tl.asdf ) or tr.tl = tl.tr and tl.tr=tr.tl and tr.tr= tl.tl or tl.tl=tr.tr"
			" " + "`tl`.`id` = `tr`.`id`"
				.replaceAll("\\s*=\\s*", "=")
				.replaceAll("\\s*\\)\\s*", ")")
				.replaceAll("\\s*\\(\\s*", "(")
				.replaceAll("`", "") + " ", "tr\\.(\\S+?)=|=tr\\.(\\S+?)[\\s|\\)]"));
	}

	@Override
	public String transformStr(StringWriter writer) throws IOException {
		if (getResultFunction() != null) {
			return getResultFunction();
		}
		String leftFunction = this.getLeft().transformStr(writer);
		String rightFunction = this.getRight().transformStr(writer);
		setResultFunction("join_" + UUID.randomUUID().toString().replace("-", ""));
		log.info("meet join condition and try to extract join fields : " + joinConditionExpression + " with " + leftViewAlias + " and " + rightViewAlias);
		String leftFieldStr = TemplateSqlUtil.extractFieldStrFromJoinExpression(wrapWithEmptyStr(joinConditionExpression.replaceAll("\\s*=\\s*", "=")
			.replaceAll("\\s*\\)\\s*", ")")
			.replaceAll("\\s*\\(\\s*", "(")
			.replaceAll("`", "")), "tl\\.(\\S+?)=|=tl\\.(\\S+?)[\\s|\\)]".replace("tl", leftViewAlias));
		String rightFieldStr = TemplateSqlUtil.extractFieldStrFromJoinExpression(wrapWithEmptyStr(joinConditionExpression.replaceAll("\\s*=\\s*", "=")
			.replaceAll("\\s*\\)\\s*", ")")
			.replaceAll("\\s*\\(\\s*", "(")
			.replaceAll("`", "")), "tr\\.(\\S+?)=|=tr\\.(\\S+?)[\\s|\\)]".replace("tr", rightViewAlias));

		writer.write(TemplateSqlUtil.readTemplate("Join.template")
			.replace("#transformFunction", getResultFunction())
			.replace("#leftFunction", leftFunction)
			.replace("#rightFunction", rightFunction)
			.replace("#leftInputView", leftInputView)
			.replace("#rightInputView", rightInputView)
			.replace("#joinType", joinType)
			.replace("#selectExpression", selectExpression)
			.replace("#joinConditionExpression", joinConditionExpression)
			.replace("#leftDeduplicateView", leftDeduplicateView)
			.replace("#rightDeduplicateView", rightDeduplicateView)
			.replace("#leftPartitionStr", leftFieldStr)
			.replace("#rightPartitionStr", rightFieldStr)
			.replace("#leftView", isDeduplicateByJoinKey ? leftDeduplicateView : leftInputView)
			.replace("#rightView", isDeduplicateByJoinKey ? rightDeduplicateView : rightInputView)
			.replace("#isDeduplicateByJoinKey", isDeduplicateByJoinKey.toString())
			.replace("#leftAlias", leftViewAlias)
			.replace("#rightAlias", rightViewAlias)
		);

		return getResultFunction();
	}


	private static String wrapWithEmptyStr(String str) {
		return " " + str + " ";
	}

	@Override
	public Boolean validate() {
		if (StringUtil.isNullOrEmpty(joinConditionExpression)) {
			return false;
		}
		if (StringUtil.isNullOrEmpty(selectExpression)) {
			return false;
		}
		if (!"inner".equals(joinType.toLowerCase().replace(" ", "")) && !"".equals(joinType.toLowerCase().replace(" ", "")) && !"left".equals(joinType.toLowerCase().replace(" ", "")) && !"right".equals(joinType.toLowerCase().replace(" ", ""))) {
			return false;
		}
		return true;
	}
}

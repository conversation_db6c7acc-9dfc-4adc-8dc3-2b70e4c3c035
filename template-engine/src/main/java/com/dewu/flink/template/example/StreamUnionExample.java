/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.dewu.flink.template.example;

import com.dewu.flink.template.config.SchemaConfig;
import com.dewu.flink.template.transform.*;
import com.dewu.flink.template.translator.generator.TableCodeParam;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.ObjectMapper;

import java.util.ArrayList;
import java.util.List;
import java.util.Properties;

public class StreamUnionExample {
    public static void main(String[] args) throws Exception {
        int cnt = 0;
        ObjectMapper mapper = new ObjectMapper();
        List<TransformationWrapper> transformations= new ArrayList();

        Properties productSourceKafkaConnectConfig = new Properties();
        productSourceKafkaConnectConfig.put("connector","kafka");
        productSourceKafkaConnectConfig.put("tableId","890");
        SourceTransformation productKafkaSourceTransformation = new SourceTransformation();
        productKafkaSourceTransformation.setConnectConfig(productSourceKafkaConnectConfig);
        transformations.add(new TransformationWrapper(String.valueOf(cnt++),"SourceTransformation",new String[]{},mapper.writeValueAsString(productKafkaSourceTransformation)));

        CalcTransformation calcTransformation1 = new CalcTransformation();
        calcTransformation1.setFilterExpression("color='red'");
        transformations.add(new TransformationWrapper(String.valueOf(cnt++),"CalcTransformation",new String[]{"0"},mapper.writeValueAsString(calcTransformation1)));

        CalcTransformation calcTransformation2 = new CalcTransformation();
        calcTransformation2.setFilterExpression("color='black'");
        transformations.add(new TransformationWrapper(String.valueOf(cnt++),"CalcTransformation",new String[]{"0"},mapper.writeValueAsString(calcTransformation2)));



        UnionTransformation unionTransformation = new UnionTransformation();
        transformations.add(new TransformationWrapper(String.valueOf(cnt++),"UnionTransformation",new String[]{"1","2"},mapper.writeValueAsString(unionTransformation)));


        String kafkaSinkPrimaryKey = "color";
        SchemaConfig sinkSchemaConfig = new SchemaConfig();
        sinkSchemaConfig.setPrimaryKey(kafkaSinkPrimaryKey);
        Properties sinkKafkaConnectConfig = new Properties();
        sinkKafkaConnectConfig.put("tableId","872");
        sinkKafkaConnectConfig.put("connector","upsert-kafka");
        SinkTransformation kafkaSinkTransformation = new SinkTransformation();
        kafkaSinkTransformation.setConnectConfig(sinkKafkaConnectConfig);
        kafkaSinkTransformation.setSchemaConfig(sinkSchemaConfig);
        transformations.add(new TransformationWrapper(String.valueOf(cnt++),"SinkTransformation",new String[]{"3"},mapper.writeValueAsString(kafkaSinkTransformation)));

        TableCodeParam tableCodeParam = new TableCodeParam();
        tableCodeParam.setTransformationWrappers(transformations);
        tableCodeParam.setOptionConfigs("");

        System.out.println(mapper.writeValueAsString(tableCodeParam));
    }
}

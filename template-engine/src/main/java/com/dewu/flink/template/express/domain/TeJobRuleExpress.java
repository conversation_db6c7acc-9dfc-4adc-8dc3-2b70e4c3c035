/*
*  Copyright 2019-2020 <PERSON>
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.dewu.flink.template.express.domain;

import com.dewu.flink.template.rule.base.express.FactoryExpression;
import lombok.Builder;
import lombok.Data;
import cn.hutool.core.bean.BeanUtil;
import io.swagger.annotations.ApiModelProperty;
import cn.hutool.core.bean.copier.CopyOptions;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import javax.persistence.*;
import javax.validation.constraints.*;
import java.sql.Timestamp;
import java.io.Serializable;

import static com.dewu.flink.template.rule.base.express.FactoryExpression.AVIATOR_EXPRESSION_TYPE;

/**
* @website https://el-admin.vip
* @description /
* <AUTHOR>
* @date 2023-02-08
**/
@Entity
@DynamicInsert
@DynamicUpdate
@Data
@Table(name="te_job_rule_express")
public class TeJobRuleExpress implements Serializable {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "`id`")
    @ApiModelProperty(value = "自增id")
    private Long id;

    @Column(name = "`rule_express`",nullable = false)
    @NotBlank
    @ApiModelProperty(value = "表达式")
    private String ruleExpress;

    @Column(name = "`te_job_info_id`",nullable = false)
    @NotNull
    @ApiModelProperty(value = "作业id")
    private String teJobInfoId;

    @Column(name = "`te_job_rule_group_id`")
    @ApiModelProperty(value = "规则组id")
    private Long teJobRuleGroupId;

    @Column(name = "`create_time`")
    @ApiModelProperty(value = "创建时间")
    private Timestamp createTime;

    @Column(name = "`modify_time`")
    @ApiModelProperty(value = "修改时间")
    private Timestamp modifyTime;

    @Column(name = "`re_data_source_metadata_id`",nullable = false)
    @NotNull
    @ApiModelProperty(value = "规则输出目标表")
    private String reDataSourceMetadataId;

    @Column(name = "`is_used`",nullable = false)
    @NotNull
    @ApiModelProperty(value = "规则是否生效")
    private String isUsed;

    @Column(name = "`rule_type`",nullable = false)
    @NotNull
    @ApiModelProperty(value = "规则类型，0打标，1过滤")
    private Long ruleType;

    @Column(name = "`expr_type`",nullable = false)
    @NotNull
    @ApiModelProperty(value = "规则引擎类型, aviator, QLExpress")
    private String exprType = FactoryExpression.AVIATOR_EXPRESSION_TYPE;

    @Column(name = "`rule_tag`",nullable = false)
    @NotNull
    @ApiModelProperty(value = "规则输出标签")
    private String ruleTag;

    public void copy(TeJobRuleExpress source){
        BeanUtil.copyProperties(source,this, CopyOptions.create().setIgnoreNullValue(true));
    }

    public static TeJobRuleExpress buildInUsedEtt(String teJobInfoId, String ruleExpress, Long ruleType, String exprType, String ruleTag){
        TeJobRuleExpress teJobRuleExpress = new TeJobRuleExpress();
        teJobRuleExpress.setRuleExpress(ruleExpress);
        teJobRuleExpress.setExprType(exprType);
        teJobRuleExpress.setRuleTag(ruleTag);
        teJobRuleExpress.setRuleType(ruleType);
        teJobRuleExpress.setTeJobInfoId(teJobInfoId);
        teJobRuleExpress.setReDataSourceMetadataId("-1");
        teJobRuleExpress.setTeJobRuleGroupId(-1L);
        teJobRuleExpress.setIsUsed("1");
        return teJobRuleExpress;
    }
}

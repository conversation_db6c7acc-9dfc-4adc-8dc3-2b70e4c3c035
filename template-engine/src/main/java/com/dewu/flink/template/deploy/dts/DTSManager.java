package com.dewu.flink.template.deploy.dts;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.dewu.flink.template.binlog.service.TeWhiteTableForBinlogService;
import com.dewu.flink.template.deploy.dmq.CreateKafkaTopicRequest;
import com.dewu.flink.template.deploy.dmq.DmqKafkaManager;
import com.dewu.flink.template.deploy.dmq.KafkaTopicDetail;
import com.dewu.flink.template.deploy.dmq.QueryKafkaTopicRequest;
import com.dewu.flink.template.job.bot.feishu.BotClient;
import com.dewu.utils.HttpClientUtil;
import com.dewu.utils.PageUtil;
import com.dewu.utils.SecurityUtils;
import com.shizhuang.sign.SignOtherUtils;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.api.java.tuple.Tuple2;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.node.ObjectNode;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.junit.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.stereotype.Component;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ScheduledThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.function.BiFunction;
import java.util.function.Consumer;
import java.util.stream.Collectors;

@Data
@Component
@RequiredArgsConstructor
@Slf4j
public class DTSManager implements DTSOpenApi {

    private static final Logger LOG = LoggerFactory.getLogger(DTSManager.class);

    @Autowired
    private BotClient botClient;

    @Autowired
    private DmqKafkaManager dmqKafkaManager;

    @Autowired
    private TeWhiteTableForBinlogService teWhiteTableForBinlogService;

    @Value("${dts.manager.url.prefix}")
    private String dtsManagerUrlPrefix;

    @Value("${dts.manager.appId}")
    private String appId;
    @Value("${dts.manager.appSecret}")
    private String appSecret;

    @Value("${dts.manager.url.base}")
    private String dtsManagerUrlBase;

    @Value("${dts.manager.targetId}")
    private String dtsManagerMqTargetId;

    @Value("${dts.manager.alarmId}")
    private String dtsManagerAlarmId;

    @Value("${dts.manager.domainId}")
    private String dtsManagerDomainId;

    @Value("${dts.manager.libraDept}")
    private String dtsManagerLibraDept;


    private String appIdRequestJsonParams;

    private String dtsManagerUrl;

    private String appToken;

    private static final String MQ_AUTHENTICATE_TOKEN = "/dts/open-api/authenticate/token";
    private static final String MQ_TASK_QUERY = "/dts/open-api/v2/mq-task/query";
    private static final String MQ_SUB_TASK_QUERY = "/dts/open-api/v2/mq-task/items/query";
    private static final String MQ_TASK_GET = "/dts/open-api/v2/mq-task/get";
    private static final String MQ_TASK_CREATE = "/dts/open-api/v2/mq-task/create";
    private static final String MQ_TASK_MODIFY = "/dts/open-api/v2/mq-task/modify";
    private static final String MQ_TASK_START = "/dts/open-api/v2/mq-task/start";
    private static final String MQ_TASK_RESTART = "/dts/open-api/v2/mq-task/restart";
    private static final String MQ_TASK_ITEMS_CREATE = "/dts/open-api/v2/mq-task/items/create";
    private static final String MQ_TASK_ITEMS_DELETE = "/dts/open-api/v2/mq-task/items/delete";
    private static final String MQ_METADATA_REGEX_MATCH = "/dts/open-api/metadata/regex-match";
    private static final String MQ_METADATA_TOPIC_PARTITION = "/dts/open-api/metadata/topic-partition";
    private static final String MQ_METADATA_DATASOURCE = "/dts/open-api/metadata/datasources";

    private final ObjectMapper objectMapper = new ObjectMapper();


    @Override
    public void afterPropertiesSet() throws Exception {
        ObjectNode objectNode = objectMapper.createObjectNode();
        objectNode.put("appId", appId);
        objectNode.put("appSecret", appSecret);
        appIdRequestJsonParams = objectNode.toString();
        dtsManagerUrl = dtsManagerUrlBase + dtsManagerUrlPrefix;
        ScheduledThreadPoolExecutor executor = new ScheduledThreadPoolExecutor(1, r -> new Thread(r, "dts-authenticate-token"));
        executor.scheduleAtFixedRate(() -> appToken = getAppToken(), 0L, 100, TimeUnit.MINUTES);
    }

    @Override
    public Object queryMqTaskInfo(QueryMqTaskRequest qr, Pageable pageable) throws Exception {
        HashMap<String, Object> params = new HashMap<>();
        params.put("creator", appId);
        params.put("pageSize", 50000);
        String topic = qr.getTopic();
        if (topic != null) {
            params.put("topic", topic);
        }
        HttpClientUtil.HttpClientResult httpResult = getHttpResult(MQ_TASK_QUERY, HttpGet.METHOD_NAME, params);
        if (httpResult.getCode() == 200) {
            String content = httpResult.getContent();
            JSONObject contentObject = JSONObject.parseObject(content);
            if (!contentObject.getString("code").equals("200")) {
                throw new RuntimeException(contentObject.getString("message"));
            }
            JSONArray data = contentObject.getJSONObject("data").getJSONArray("content");
            if (data != null) {
                List<JSONObject> results = new ArrayList<>();
                data.forEach(
                        d -> {
                            JSONArray taskItems = ((JSONObject) d).getJSONArray("taskItems");
                            if (taskItems.size() > 0) {
                                taskItems.forEach(e -> {
                                    JSONObject taskItem = (JSONObject) e;
                                    taskItem.put("taskName", ((JSONObject) d).getString("taskName"));
                                    taskItem.put("taskNo", ((JSONObject) d).getString("taskNo"));
                                    taskItem.put("auditStatus", ((JSONObject) d).getString("auditStatus"));
                                    taskItem.put("runningStatus", ((JSONObject) d).getString("runningStatus"));
                                    taskItem.put("sourceId", ((JSONObject) d).getString("sourceId"));
                                    try {
                                        queryDataSourceName(((JSONObject) d).getInteger("sourceId")).ifPresent(sourceName -> taskItem.put("sourceName", sourceName));
                                    } catch (Exception ex) {
                                        ex.printStackTrace();
                                    }
                                    results.add(taskItem);
                                });
                            }
                        }
                );
                //sort modify time desc
                results.sort((o1, o2) -> {
                    SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd hh:mm:ss");
                    try {
                        long diff = simpleDateFormat.parse(o1.getString("modifyTime")).getTime() - simpleDateFormat.parse(o2.getString("modifyTime")).getTime();
                        if (diff > 0) {
                            return -1;
                        } else if (diff < 0) {
                            return 1;
                        } else {
                            return 0;
                        }
                    } catch (ParseException e) {
                        throw new IllegalArgumentException("o1 or o2 modifyTime error o1:" + o1.getString("modifyTime") + " o2:" + o2.getString("modifyTime"));
                    }
                });


                // process
                int pageNumber = pageable.getPageNumber();
                int pageSize = pageable.getPageSize();
                int total = results.size();
                int totalPageSize = total / pageSize + (total % pageSize == 0 ? 0 : 1);
                if (pageNumber > totalPageSize) {
                    throw new RuntimeException("无效的分页参数：" + pageSize + " totalPageSize：" + totalPageSize);
                }
                if (pageSize < total) {
                    int start = pageNumber * pageSize;
                    int end = start + pageSize;
                    PageImpl<JSONObject> page = new PageImpl<>(results.subList(start, Math.min(end, total)), pageable, total);
                    return PageUtil.toPage(page);
                } else {
                    PageImpl<JSONObject> page = new PageImpl<>(results, pageable, total);
                    return PageUtil.toPage(page);
                }
            } else {
                PageImpl<JSONObject> page = new PageImpl<>(new ArrayList<>(), pageable, 0);
                return PageUtil.toPage(page);
            }
        } else {
            throw new RuntimeException(MQ_TASK_QUERY + " 网络异常 请稍后重试");
        }
    }

    public Object queryMqTaskItemInfo(QueryMqTaskRequest qr, Pageable pageable) throws Exception {
        HashMap<String, Object> params = new HashMap<>();
        params.put("creator", appId);
        params.put("pageNo", pageable.getPageNumber());
        params.put("pageSize", pageable.getPageSize());
        String topic = qr.getTopic();
        if (topic != null) {
            params.put("topic", topic);
        }
        HttpClientUtil.HttpClientResult httpResult = getHttpResult(MQ_SUB_TASK_QUERY, HttpPost.METHOD_NAME, params);
        if (httpResult.getCode() == 200) {
            String content = httpResult.getContent();
            JSONObject contentObject = JSONObject.parseObject(content);
            if (!contentObject.getString("code").equals("200")) {
                throw new RuntimeException(contentObject.getString("message"));
            }
            JSONObject dataObject = contentObject.getJSONObject("data");
            Integer total = dataObject.getInteger("total");
            JSONArray data = dataObject.getJSONArray("content");

            PageImpl<JSONObject> page;
            if (data != null) {
                 page = new PageImpl<>(data.toJavaList(JSONObject.class), pageable,total);
            } else {
                 page = new PageImpl<>(new ArrayList<>(), pageable, 0);
            }
            return PageUtil.toPage(page);

        } else {
            throw new RuntimeException(MQ_TASK_QUERY + " 网络异常 请稍后重试");
        }
    }


    @Override
    public Object queryDataSources(String instanceName) throws Exception {
        HashMap<String, Object> params = new HashMap<>();
        HttpClientUtil.HttpClientResult httpResult = getHttpResult(MQ_METADATA_DATASOURCE, HttpGet.METHOD_NAME, params);
        if (httpResult.getCode() != 200) {
            throw new RuntimeException(MQ_METADATA_DATASOURCE + " 网络异常 请稍后重试");
        } else {
            String content = httpResult.getContent();
            String data = JSONObject.parseObject(content).getString("data");
            JSONArray array = JSONObject.parseArray(data);
            List<Object> result = array.stream().filter(e -> ((JSONObject) e).getString("name").equals(instanceName)).collect(Collectors.toList());
            return createResult("200", "", result.size() > 0 ? result.get(0) : "");
        }
    }

    @Override
    public Optional<Integer> queryDataSourceId(String sourceName) throws Exception {
        HashMap<String, Object> params = new HashMap<>();
        HttpClientUtil.HttpClientResult httpResult = getHttpResult(MQ_METADATA_DATASOURCE, HttpGet.METHOD_NAME, params);
        if (httpResult.getCode() != 200) {
            throw new RuntimeException(MQ_METADATA_DATASOURCE + " 网络异常 请稍后重试");
        } else {
            String content = httpResult.getContent();
            String data = JSONObject.parseObject(content).getString("data");
            JSONArray array = JSONObject.parseArray(data);
            List<Integer> result = array.stream().filter(e -> ((JSONObject) e).getString("name").equals(sourceName)).map(item -> ((JSONObject) item).getIntValue("value")).collect(Collectors.toList());
            if (result.size() > 0) {
                return Optional.of(result.get(0));
            } else {
                return Optional.empty();
            }
        }
    }

    @Override
    public Optional<String> queryDataSourceName(Integer sourceId) throws Exception {
        HashMap<String, Object> params = new HashMap<>();
        HttpClientUtil.HttpClientResult httpResult = getHttpResult(MQ_METADATA_DATASOURCE, HttpGet.METHOD_NAME, params);
        if (httpResult.getCode() != 200) {
            throw new RuntimeException(MQ_METADATA_DATASOURCE + " 网络异常 请稍后重试");
        } else {
            String content = httpResult.getContent();
            String data = JSONObject.parseObject(content).getString("data");
            JSONArray array = JSONObject.parseArray(data);
            List<String> result = array.stream().filter(e -> ((JSONObject) e).getInteger("value").equals(sourceId)).map(item -> ((JSONObject) item).getString("name")).collect(Collectors.toList());
            if (result.size() > 0) {
                return Optional.of(result.get(0));
            } else {
                return Optional.empty();
            }
        }
    }

    @Override
    public Object checkRegexMatch(String instanceId, String tableRegex) throws Exception {
        return checkRegexMqMetadataRegexMatch(instanceId, tableRegex);
    }

    @Override
    public Object createMqTaskItem(CreateMqTaskItem ci) throws Exception {
        // check task Id exist
        String itemName = ci.getItemName();
        String instanceId = ci.getInstanceId();
        String partition = ci.getPartition();
        String tableRegex = obtainTableRegex(ci.getDatabase(), ci.isDatabaseSplit(), ci.getTable(), ci.isTableSplit());
        String topic = ci.getTopic();

        UserDetails currentUser = SecurityUtils.getCurrentUser();
        String username = currentUser.getUsername();
        if ("admin".equals(username)) {
            username = "linzhiping";
        }
        //1.check regex-match
        checkRegexMqMetadataRegexMatch(instanceId, tableRegex);

        //2.check topic exist or create
        String topicNew = checkTopicExistOrCreate(topic, username, partition);

        //3.check InstanceTask is running
        List<Object> tasks = checkInstanceTaskIsExist(instanceId);

        if (tasks.size() > 0) {
            String id = ((JSONObject) tasks.get(0)).getString("id");
            log.info("create sub task:{}", itemName);
            //create sub task
            return createSubTasks(itemName, id, tableRegex, topicNew, username, partition);
        } else {
            log.info("create task include subtask:{}", itemName);
            //create task include subtask;
            return createMqTask(itemName, instanceId, tableRegex, topicNew, username, partition);
        }

    }


    @Override
    public void startMqTask(StartMqTaskRequest sr) throws Exception {
        HashMap<String, Object> params = new HashMap<>();
        String taskId = sr.getTaskId();
        String remark = sr.getRemark();
        String operatorName = sr.getOperatorName();

        params.put("taskId", taskId);
        params.put("operatorName", operatorName);
        params.put("lastVersion", true);
        params.put("remark", remark);
        HttpClientUtil.HttpClientResult result = getHttpResult(MQ_TASK_START, HttpPost.METHOD_NAME, params);
        if (result.getCode() == 200) {
            String content = result.getContent();
            JSONObject contentObject = JSONObject.parseObject(content);
            if (!contentObject.getString("code").equals("200")) {
                throw new RuntimeException(contentObject.getString("message"));
            }
        } else {
            throw new RuntimeException(MQ_TASK_START + " 网络异常 请稍后重试");
        }
    }

    @Override
    public void restartMqTask(StartMqTaskRequest sr) throws Exception {
        HashMap<String, Object> params = new HashMap<>();
        String taskId = sr.getTaskId();
        String remark = sr.getRemark();
        String operatorName = sr.getOperatorName();

        params.put("taskId", taskId);
        params.put("operatorName", operatorName);
        params.put("lastVersion", true);
        params.put("remark", remark);
        HttpClientUtil.HttpClientResult result = getHttpResult(MQ_TASK_RESTART, HttpPost.METHOD_NAME, params);
        if (result.getCode() == 200) {
            String content = result.getContent();
            JSONObject contentObject = JSONObject.parseObject(content);
            if (!contentObject.getString("code").equals("200")) {
                throw new RuntimeException(contentObject.getString("message"));
            }
        } else {
            throw new RuntimeException(MQ_TASK_START + " 网络异常 请稍后重试");
        }

    }

    private Object checkRegexMqMetadataRegexMatch(String instanceId, String tableRegex) throws Exception {
        HashMap<String, Object> regexMap = new HashMap<>();
        regexMap.put("dataSourceId", instanceId);
        regexMap.put("tableRegex", tableRegex);
        HttpClientUtil.HttpClientResult regexResult = getHttpResult(MQ_METADATA_REGEX_MATCH, HttpPost.METHOD_NAME, regexMap);
        if (regexResult.getCode() == 200) {
            String content = regexResult.getContent();
            JSONObject contentObject = JSONObject.parseObject(content);
            if (contentObject.getString("code").equals("200")) {
                return contentObject.getJSONArray("data");
            } else {
                throw new RuntimeException(contentObject.getString("message"));
            }
        }
        throw new RuntimeException(MQ_METADATA_REGEX_MATCH + " 网络异常 请稍后重试");
    }

    private String checkTopicExistOrCreate(String topic, String username, String partition) throws Exception {
        QueryKafkaTopicRequest qr = new QueryKafkaTopicRequest();
        qr.setTopic(topic);
        if (dmqKafkaManager.queryKafkaTopic(qr).isPresent()) {
            // 添加白名单机制
            if (teWhiteTableForBinlogService.checkWhite(topic)) {
                return topic;
            } else {
                throw new RuntimeException(topic + " topic 已经存在,无需创建,如有特殊需要，请找管理员加白名单！");
            }
        } else {
            // create topic
            CreateKafkaTopicRequest createKafkaTopicRequest = new CreateKafkaTopicRequest();
            createKafkaTopicRequest.setPartition(Integer.parseInt(partition));
            createKafkaTopicRequest.setTopic(topic);
            createKafkaTopicRequest.setUsername(username);
            dmqKafkaManager.createKafkaTopic(createKafkaTopicRequest);
            return topic;
        }
    }

    public String obtainTableRegex(String dbName, Boolean splitDB, String tableName, Boolean splitTable) {
        String regex = dbName;
        regex += splitDB ? "_\\d+\\." : "\\.";
        regex += tableName;
        if (splitTable) {
            regex += "_\\d+";
        }
        return regex;
    }

    private List<Object> checkInstanceTaskIsExist(String instanceId) throws Exception {
        HashMap<String, Object> params = new HashMap<>();
        params.put("creator", appId);
        params.put("pageSize", "5000");

        HttpClientUtil.HttpClientResult httpResult = getHttpResult(MQ_TASK_QUERY, HttpGet.METHOD_NAME, params);
        if (httpResult.getCode() == 200) {
            String content = httpResult.getContent();
            JSONObject contentObject = JSONObject.parseObject(content);
            if ("200".equals(contentObject.getString("code"))) {
                JSONArray contentArray = contentObject.getJSONObject("data").getJSONArray("content");
                if (contentArray == null || contentArray.size() == 0) {
                    return new ArrayList<>();
                }
                return contentArray.stream().filter(c -> instanceId.equals(((JSONObject) c).getString("sourceId"))).collect(Collectors.toList());
            }

        }
        throw new RuntimeException(MQ_TASK_QUERY + " 网络异常 请稍后重试");
    }


    private Object createSubTasks(String itemName,
                                  String taskId,
                                  String tableRegex,
                                  String topic,
                                  String username,
                                  String partition) throws Exception {
        HashMap<String, Object> params = new HashMap<>();
        params.put("taskId", taskId);
        params.put("itemName", itemName);
        params.put("tableRegex", tableRegex);
        params.put("topic", topic);
        params.put("partition", partition);
        params.put("partitionKey", "$pk$");
        params.put("creator", username);
        HttpClientUtil.HttpClientResult httpResult = getHttpResult(MQ_TASK_ITEMS_CREATE, HttpPost.METHOD_NAME, params);

        if (httpResult.getCode() == 200) {
            JSONObject contentObject = JSONObject.parseObject(httpResult.getContent());

            LOG.info("dts接口返回结果{}",contentObject.toJSONString());

            if (!contentObject.getString("code").equals("200")) {
                throw new RuntimeException(contentObject.getString("message"));
            }else{
                return JSONObject.parseObject(httpResult.getContent());
            }
        }
        throw new RuntimeException(MQ_TASK_ITEMS_CREATE + " 网络异常 请稍后重试");
    }


    /**
     * {
     * "taskName": "DCHECK_T1_dcheck", // 任务名
     * "sourceId": 1, // 上游数据源 ID
     * "targetId": 15, // 下游 MQ 配置 ID
     * "alarmId": 6, // 告警群配置 ID
     * "domainId": 64, // 业务域 ID
     * "startTime": null, // 同步起始位点
     * "blackArr": [], // 黑名单正则列表（数组
     * "remark": "DCHECK 自动创建规则，业务域编码: dcheck", // 备注
     * "delayWarn": 30, // 延迟告警阈值(秒
     * "delayFault": 120, // 延迟故障阈值（秒
     * "warnChargeArr": ["xxx","yyy"], // 延迟告警负责人
     * "faultChargeArr": ["xxx","yyy"], // 延迟故障负责人
     * "taskLevel": "P4", // 任务级别
     * "jarId": 2, // jar 包版本 ID，如果为 null 则使用最新版本 jar
     * "libraParallel": 1, // 任务并行度
     * "libraSlot": 1, // 每个 taskmanager 多少个 slot
     * "libraDept": 0, // 成本分摊所在部门 ID
     * "taskItems": [] // 创建订阅任务同时附带配置明细（可选
     * }
     *
     * @param
     * @return
     * @throws Exception
     */
    public Object createMqTask(String itemName,
                               String instanceId,
                               String tableRegex,
                               String topic,
                               String username,
                               String partition) throws Exception {
        HashMap<String, Object> params = new HashMap<>();
        params.put("taskName", "模版平台自动创建任务-" + instanceId);
        params.put("sourceId", instanceId);
        params.put("targetId", dtsManagerMqTargetId);
        params.put("alarmId", dtsManagerAlarmId);
        params.put("domainId", dtsManagerDomainId);
        params.put("startTime", null);
        params.put("remark", "TEMPLATE_ENGINE 自动创建");
        params.put("delayWarn", "30");
        params.put("delayFault", "120");
        params.put("taskLevel", "P4");
        params.put("jarId", null);
        params.put("libraParallel", "1");
        params.put("libraSlot", "1");
        params.put("libraDept", dtsManagerLibraDept);
        JSONArray warnArray = new JSONArray();
        warnArray.add("wanglin");
        params.put("warnChargeArr", warnArray);
        params.put("faultChargeArr", warnArray);
        params.put("compressType","gzip");

        JSONObject subTaskParams = new JSONObject();
        subTaskParams.put("itemName", itemName);
        subTaskParams.put("tableRegex", tableRegex);
        subTaskParams.put("topic", topic);
        subTaskParams.put("partition", partition);
        subTaskParams.put("partitionKey", "$pk$");
        subTaskParams.put("creator", username);
        JSONArray array = new JSONArray();
        array.add(subTaskParams);
        params.put("taskItems", array);

        HttpClientUtil.HttpClientResult httpResult = getHttpResult(MQ_TASK_CREATE, HttpPost.METHOD_NAME, params);
        if (httpResult.getCode() == 200) {
            JSONObject contentObject = JSONObject.parseObject(httpResult.getContent());

            LOG.info("dts接口返回结果{}",contentObject.toJSONString());

            if (!contentObject.getString("code").equals("200")) {
                throw new RuntimeException(contentObject.getString("message"));
            }else{
                return JSONObject.parseObject(httpResult.getContent());
            }
        } else {
            throw new RuntimeException(MQ_TASK_CREATE + " 网络异常 请稍后重试");
        }
    }

    private Map<String, Object> createResult(String code, String message, Object data) {
        Map<String, Object> result = new HashMap<>();
        result.put("code", code);
        result.put("mes", message);
        result.put("data", data);
        return result;
    }


    private String getAppToken() {
        String token = null;
        HttpClientUtil.HttpClientResult httpClientResult = null;
        try {
            httpClientResult = HttpClientUtil.doPost(dtsManagerUrl + MQ_AUTHENTICATE_TOKEN, null, appIdRequestJsonParams);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        int code = httpClientResult.getCode();

        if (code == 200) {
            String content = httpClientResult.getContent();
            token = JSONObject.parseObject(JSONObject.parseObject(content).getString("data")).getString("token");
        }
        return token;
    }


    //gatewayPath:dtsManagerUrlPrefix+url
    private void setGatewaySignAndDtsToken(String gatewayPath, Map<String, String> headers) {
        if (appToken == null) {
            String newToken = getAppToken();
            if (newToken == null) {
                throw new RuntimeException("get dts app token error");
            }
            this.appToken = newToken;
        }
        headers.put("appId", "1");
        headers.put("time", String.valueOf(System.currentTimeMillis() / 1000L));
        String sign = SignOtherUtils.sign(gatewayPath, headers);
        headers.put("token", sign);
        headers.put("X-DTS-Token", appToken);
    }

    private HttpClientUtil.HttpClientResult getHttpResult(String mainUrl, String httpRequestMethod, HashMap<String, Object> params) throws Exception {
        Map<String, String> headers = new TreeMap<>();
        setGatewaySignAndDtsToken(dtsManagerUrlPrefix + mainUrl, headers);
        HttpClientUtil.HttpClientResult httpClientResult;
        if (HttpGet.METHOD_NAME.equals(httpRequestMethod)) {
            HashMap<String, String> stringParam = new HashMap<>();
            params.forEach((key, value) -> stringParam.put(key, Objects.toString(value)));
            httpClientResult = HttpClientUtil.doGet(dtsManagerUrl + mainUrl, headers, stringParam);

        } else {
            JSONObject jsonObject = new JSONObject();
            params.forEach(jsonObject::put);
            httpClientResult = HttpClientUtil.doPost(dtsManagerUrl + mainUrl, headers, jsonObject.toJSONString());
        }
        if (!MQ_METADATA_DATASOURCE.equals(mainUrl)&& !MQ_TASK_QUERY.equals(mainUrl)) {
            log.info("call {} with params {} and return {}", mainUrl, params, httpClientResult);
        }
        return httpClientResult;
    }


    public static void main(String[] args) throws Exception {
        Map<String, String> appParams = new HashMap<>();
        appParams.put("appId", "TEMPLATE_ENGINE");
        appParams.put("appSecret", "DC38F5729A1E440886BBCDA6CF6C7485");
        HttpClientUtil.HttpClientResult httpClientResult1 = HttpClientUtil.doPost("https://t1-ms3.dewu.net/api/v1/internal/dts-manager/dts/open-api/authenticate/token", null, "{\n" +
                "    \"appId\":\"TEMPLATE_ENGINE\",\n" +
                "    \"appSecret\":\"DC38F5729A1E440886BBCDA6CF6C7485\"\n" +
                "}");
        String content = httpClientResult1.getContent();
        JSONObject jsonObject = JSONObject.parseObject(content);
        String token = JSONObject.parseObject(jsonObject.getString("data")).getString("token");

//		HashMap<String, String> subTaskParams = new HashMap<>();
//		JSONArray array = new JSONArray();
//		JSONObject subTaskParams = new JSONObject();
//		array.add(subTaskParams);
//		HashMap<String, Object> params = new HashMap<>();

//		params.put("taskName", "模版平台测试1");
//		params.put("sourceId", "3");
//		params.put("targetId", "24");
//		params.put("alarmId", "6");
//		params.put("domainId", "26");
//		params.put("startTime", null);
//		params.put("remark", "TEMPLATE_ENGINE 自动创建");
//		params.put("delayWarn", "30");
//		params.put("delayFault", "120");
//		params.put("taskLevel", "P4");
//		params.put("jarId", null);
//		params.put("libraParallel", "2");
//		params.put("libraSlot", "2");
//		params.put("libraDept", "26");
//		subTaskParams.put("taskId", "800");
//		subTaskParams.put("itemName", "bigdata_ads_sink.ads_delivery_soft_attr_metric");
//		subTaskParams.put("tableRegex", "bigdata_ads_sink\\.ads_delivery_soft_attr_metric");
//		subTaskParams.put("topic", "test");
//		subTaskParams.put("partition", "6");
//		subTaskParams.put("partitionKey", "$pk$");
//		params.put("taskItems", array);
        DTSManager dtsManager = new DTSManager();
        dtsManager.setAppToken(token);
//		String get = getHttpResult("/api/v1/internal/dts-manager", MQ_TASK_QUERY, HttpGet.METHOD_NAME, dtsManager, params);
//		regexMap.put("mqId", "24");
//		regexMap.put("topic", "binlog");

        HashMap<String, Object> pa = new HashMap<>();
        pa.put("creator", "TEMPLATE_ENGINE");
//        pa.put("pageSize", 20);
//        pa.put("pageNo", 0);
//        pa.put("taskId", 2);
        String result = getHttpResult(MQ_SUB_TASK_QUERY, HttpPost.METHOD_NAME, dtsManager, pa);
        JSONObject resultObject = JSONObject.parseObject(result);
        JSONArray data = resultObject.getJSONObject("data").getJSONArray("content");
        List<JSONObject> results = new ArrayList<>();
        if (data.size() > 0) {
            data.forEach(
                    d -> {
                        JSONArray taskItems = ((JSONObject) d).getJSONArray("taskItems");
                        if (taskItems.size() > 0) {
                            taskItems.forEach(e -> {
                                JSONObject taskItem = (JSONObject) e;
                                taskItem.put("taskName", ((JSONObject) d).getString("taskName"));
                                taskItem.put("taskNo", ((JSONObject) d).getString("taskNo"));
                                taskItem.put("auditStatus", ((JSONObject) d).getString("auditStatus"));
                                taskItem.put("runningStatus", ((JSONObject) d).getString("runningStatus"));
                                taskItem.put("sourceId", ((JSONObject) d).getString("sourceId"));
                                results.add(taskItem);
                            });
                        }
                    }
            );
        }

        results.sort((o1, o2) -> {
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd hh:mm:ss");
            try {
                long diff = simpleDateFormat.parse(o1.getString("modifyTime")).getTime() - simpleDateFormat.parse(o2.getString("modifyTime")).getTime();
                if (diff > 0) {
                    return -1;
                } else if (diff < 0) {
                    return 1;
                } else {
                    return 0;
                }
            } catch (ParseException e) {
                throw new IllegalArgumentException("o1 or o2 modifyTime error o1:" + o1.getString("modifyTime") + " o2:" + o2.getString("modifyTime"));
            }
        });


        int pageNumber = 1;
        int pageSize = 10;
        int total = results.size();
        int totalPageSize = total / pageSize + (total % pageSize == 0 ? 0 : 1);
        if (pageNumber > totalPageSize) {
            throw new RuntimeException("无效的分页参数：" + pageSize + " totalPageSize：" + totalPageSize);
        }
        if (pageSize < total) {
            int start = pageNumber * pageSize;
            List<JSONObject> objects = results.subList(start, Math.min(start + pageSize, total));
            System.out.println(objects);
        }


    }

    private static String getHttpResult(String doRequestUrl, String httpRequest, DTSManager dtsManager, HashMap<String, Object> params) throws Exception {
        Map<String, String> headers = new TreeMap<>();
        dtsManager.setGatewaySignAndDtsToken("/api/v1/internal/dts-manager" + doRequestUrl, headers);
        HttpClientUtil.HttpClientResult httpClientResult;
        if ("GET".equals(httpRequest)) {
            HashMap<String, String> stringParam = new HashMap<>();
            params.forEach((key, value) -> stringParam.put(key, Objects.toString(value)));
            httpClientResult = HttpClientUtil.doGet("https://t1-ms3.dewu.net/api/v1/internal/dts-manager" + doRequestUrl, headers, stringParam);

        } else {
            JSONObject jsonObject = new JSONObject();
            params.forEach(jsonObject::put);

            httpClientResult = HttpClientUtil.doPost("https://t1-ms3.dewu.net/api/v1/internal/dts-manager" + doRequestUrl, headers, jsonObject.toJSONString());
        }

        if (httpClientResult.getCode() == 200) {
            return httpClientResult.getContent();
        } else {
            return httpClientResult.toString();
        }

    }

    @Test
    public void test() {
        CompletableFuture<String> completableFuture = new CompletableFuture<>();
        completableFuture.complete("test");

        CompletableFuture<Void> voidCompletableFuture = completableFuture.thenAccept(new Consumer<String>() {
            @Override
            public void accept(String s) {
                try {
                    tes(s);
                } catch (Exception e) {
                    throw new RuntimeException(e);
                }
            }
        });

        voidCompletableFuture.whenComplete((v, e) -> {
            if (e != null) {
                System.out.println(e);
            }
        });
    }


    public void tes(String s) throws Exception {
        System.out.println(s);
        throw new RuntimeException(s);
    }


}



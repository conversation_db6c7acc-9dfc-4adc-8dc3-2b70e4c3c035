package com.dewu.flink.template.transform;


import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import java.io.IOException;
import java.io.StringWriter;

@EqualsAndHashCode(callSuper = true)
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class DataMockedSourceTransformation extends AbstractTransformation {
	SourceTransformation innerSourceTransformation;
	SourceTransformation chaosTableSourceTransformation;
	AbstractTransformation finalTransformation;
	String chaosRuleType = "1";
	String chaosRuls = "";

	public DataMockedSourceTransformation(SourceTransformation innerSourceTransformation, SourceTransformation chaosTableSourceTransformation, String chaosRuleType, String chaosRuls) {
		this.innerSourceTransformation = innerSourceTransformation;
		this.chaosTableSourceTransformation = chaosTableSourceTransformation;
		this.chaosRuleType = chaosRuleType;
		this.chaosRuls = chaosRuls;

		// (1 数据篡改，2 数据注入)
		if ("1".equals(chaosRuleType)) {
			CalcTransformation calcTransformation = new CalcTransformation();
			String selectExpr = "";
			calcTransformation.setSelectExpression(selectExpr);
			calcTransformation.setInput(innerSourceTransformation);
			finalTransformation = calcTransformation;
		} else {
			UnionTransformation unionTransformation = new UnionTransformation();
			unionTransformation.setLeft(innerSourceTransformation);
			unionTransformation.setRight(chaosTableSourceTransformation);
			unionTransformation.setRightFilterExpression("");
			unionTransformation.setRightSelectExpression("");
			finalTransformation = unionTransformation;
		}
	}

	private String buildChmodDataSelectExpr(){
		return "";
	}

	private String buildInjectDataSelectExpr(){
		return "";
	}

	@Override
	public Boolean isFieldNeed(String fieldName) {
		return false;
	}

	@Override
	public String transformStr(StringWriter writer) throws IOException {
		// (1 数据篡改，2 数据注入)
		if ("1".equals(chaosRuleType)) {
			CalcTransformation calcTransformation = new CalcTransformation();
			String selectExpr = "";
			calcTransformation.setSelectExpression(selectExpr);
			calcTransformation.setInput(innerSourceTransformation);
		} else {
			UnionTransformation unionTransformation = new UnionTransformation();
			unionTransformation.setLeft(innerSourceTransformation);
			unionTransformation.setRight(chaosTableSourceTransformation);

		}
		return null;
	}

	@Override
	public Boolean validate() {
		return true;
	}
}

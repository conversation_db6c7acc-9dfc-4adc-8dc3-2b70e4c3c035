package com.dewu.flink.template.transform;

import com.dewu.flink.template.config.SchemaConfig;
import com.dewu.flink.template.meta.GlobalMetaBaseDomain;
import com.dewu.flink.template.utils.Constant;
import com.dewu.flink.template.utils.TemplateSchemeType;
import com.dewu.flink.template.utils.TemplateSqlUtil;
import lombok.Data;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.annotation.JsonIgnore;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import org.apache.flink.shaded.netty4.io.netty.util.internal.StringUtil;
import org.apache.flink.util.CollectionUtil;
import org.apache.flink.util.StringUtils;

import java.io.IOException;
import java.io.StringWriter;
import java.util.*;

import static com.dewu.flink.template.translator.generator.LibraConnectorType.*;
import static com.dewu.flink.template.utils.TemplateSqlUtil.formatConnectorType;

// executeInsert()
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class SinkTransformation extends OneInputTransformation {
	private String jobType;
	private boolean isVarbinaryMode = false;
	private String targetBinaryMessageCol = "";
	private String targetBinaryMessageKeyCol = "";
	private boolean isOriginalSourceSink = false;
	private Properties connectConfig;
	private SchemaConfig schemaConfig;
	private List<String> binaryKafkaTables = new ArrayList<>();
	private String extSql = "";
	private String dedupKey = "";
	private boolean isDynamicRule = false;
	private boolean sinkDedup = false;
	@JsonIgnore
	private String tableName = "sink_" + UUID.randomUUID().toString().replace("-", "");
	@JsonIgnore
	private String extSqlViewName = "view_" + UUID.randomUUID().toString().replace("-", "");
	@JsonIgnore
	private String viewName = "view_" + UUID.randomUUID().toString().replace("-", "");
	private List<String> drillData = new ArrayList<>();
	private boolean isDataDrill = false;
	private String tableIdentifier;

	public SinkTransformation() {
	}

	public SinkTransformation(String jobType) {
		this.jobType = jobType;
	}

	@Override
	public Boolean isFieldNeed(String fieldName) {
		return true;
	}

	public Properties getconnectConfig() {
		return connectConfig;
	}

	public SchemaConfig getSchemaConfig() {
		return schemaConfig;
	}

	public void setConnectConfig(Properties connectConfig) {
		this.connectConfig = connectConfig;
	}

	public void setSchemaConfig(SchemaConfig schemaConfig) {
		this.schemaConfig = schemaConfig;
	}

	public String getExtSql() {
		return extSql;
	}

	public void setExtSql(String extSql) {
		this.extSql = extSql;
	}

	public boolean getIsDynamicRule() {
		return isDynamicRule;
	}

	public void setIsDynamicRule(boolean isDynamicRule) {
		this.isDynamicRule = isDynamicRule;
	}

	@Override
	public String transformStr(StringWriter writer) throws IOException {
		if (getResultFunction() != null) {
			return getResultFunction();
		}
		String inputFunction = this.getInput().transformStr(writer);
		setResultFunction("sink_" + UUID.randomUUID().toString().replace("-", ""));
		boolean existsExtSql = !StringUtil.isNullOrEmpty(extSql);

		if (isDynamicRule) {
			Properties connectConfig1 = new Properties();
			connectConfig1.putAll(connectConfig);
			connectConfig1.put("connector", "dynamic-kafka");
			String replace = TemplateSqlUtil.readTemplate("DynamicSink.template")
				.replace("#transformFunction", getResultFunction())
				.replace("#inputFunction", inputFunction)
				.replace("#tableName", Constant.DYNAMIC_TABLE_NAME)
				.replace("#existsExtSql", String.valueOf(existsExtSql))
				.replace("#extSqlView", extSqlViewName)
				.replace("#extSql", existsExtSql ? extSql.replace("$.baseTable", extSqlViewName) : "")
				.replace("#viewName", viewName)
				.replace("#config", TemplateSqlUtil.generateDynamicConfigStr(connectConfig1))
				.replace("#schema", "ip STRING,topic STRING,`partition` INT,message STRING");
			writer.write(replace);

		} else {
			GlobalMetaBaseDomain globalMetaBaseDomain = getGlobalMetaManager().getTableMetaByTableId(Long.valueOf(connectConfig.getProperty("tableId")));
			TemplateSchemeType schemeType = null;
			String cf = "";
			updateConnectConfig(connectConfig, globalMetaBaseDomain, SinkTransformation.class, false);
			String connector = connectConfig.getProperty("connector");
			boolean isStreamConnector = connector.equals(C_KAFKA.getName()) || connector.equals(C_UPSERT_KAFKA.getName()) || connector.equals(C_ORIGIN_UPSERT_KAFKA.getName()) || connector.equals(C_ROCKET_MQ.getName());
			if ((isStreamConnector && isVarbinaryMode) || binaryKafkaTables.contains(tableIdentifier != null ? tableIdentifier : tableName)) {
				schemeType = TemplateSchemeType.BINARY;
			} else if (connector.equals(C_HBASE.getName())) {
				schemeType = TemplateSchemeType.HBASE;
				cf = globalMetaBaseDomain.getReDataSourceMetadataDto().getColumnFamily();
				schemaConfig.getExtInfo().put("cf", cf);
			} else {
				schemeType = TemplateSchemeType.NORMAL;
			}
			if (StringUtils.isNullOrWhitespaceOnly(schemaConfig.getPrimaryKey()) || "FLK_AUTO_PFLAG".equals(schemaConfig.getPrimaryKey())) {
				schemaConfig.setPrimaryKey(Optional.ofNullable(globalMetaBaseDomain.getReDataSourceMetadataDto().getUniqueKey()).orElse(""));
			}
			connectConfig.remove("tableId");

			if (!StringUtils.isNullOrWhitespaceOnly(jobType) && jobType.startsWith("SYNC_")) {
				setOriginalSourceSink(true);
			}

			if (!isDataDrill) {
				writer.write(TemplateSqlUtil.readTemplate("Sink.template")
					.replace("#transformFunction", getResultFunction())
					.replace("#connectorType", formatConnectorType(connector))
					.replace("#inputFunction", inputFunction)
					.replace("#isOriginalSourceSink", String.valueOf(isOriginalSourceSink))
					.replace("#isBinarySink", String.valueOf(schemeType.equals(TemplateSchemeType.BINARY)))
					.replace("#isHbaseSink", String.valueOf(schemeType.equals(TemplateSchemeType.HBASE)))
					.replace("#targetBinaryMessageCol", targetBinaryMessageCol)
					.replace("#targetBinaryMessageKeyCol", StringUtils.isNullOrWhitespaceOnly(schemaConfig.getPrimaryKey()) ? targetBinaryMessageKeyCol : schemaConfig.getPrimaryKey())
					.replace("#rawPrimaryKeyStr", schemaConfig.getPrimaryKey())
					.replace("#columnStr", TemplateSqlUtil.generateColumnStr(schemaConfig, schemeType, false))
					.replace("#primaryKeyStr", TemplateSqlUtil.generatePrimaryKeyStr(schemaConfig, schemeType))
					.replace("#tableName", tableName)
					.replace("#existsExtSql", String.valueOf(existsExtSql))
					.replace("#extSqlView", extSqlViewName)
					.replace("#extSql", existsExtSql ? extSql.replace("$.baseTable", extSqlViewName) : "")
					.replace("#sinkDedup", String.valueOf(sinkDedup))
					.replace("#dedupKey", dedupKey)
					.replace("#viewName", viewName)
					.replace("#config", TemplateSqlUtil.generateConfigStr(connectConfig, connector, false, schemeType))
					.replace("#cf", cf)
				);
			} else {
				boolean hasExpectData = !CollectionUtil.isNullOrEmpty(drillData);
				String templateSink = TemplateSqlUtil.readTemplate("SinkDataDrill.template")
					.replace("#transformFunction", getResultFunction())
					.replace("#inputFunction", inputFunction)
					.replace("#isOriginalSourceSink", String.valueOf(isOriginalSourceSink))
					.replace("#isBinarySink", String.valueOf(schemeType.equals(TemplateSchemeType.BINARY)))
					.replace("#isHbaseSink", String.valueOf(schemeType.equals(TemplateSchemeType.HBASE)))
					.replace("#targetBinaryMessageCol", targetBinaryMessageCol)
					.replace("#targetBinaryMessageKeyCol", targetBinaryMessageKeyCol)
					.replace("#rawPrimaryKeyStr", schemaConfig.getPrimaryKey())
					.replace("#columnStr", TemplateSqlUtil.generateColumnStr(schemaConfig, schemeType, false))
					.replace("#primaryKeyStr", TemplateSqlUtil.generatePrimaryKeyStr(schemaConfig, schemeType))
//					.replace("#primaryKey", schemaConfig.getPrimaryKey())
					.replace("#tableName", tableIdentifier != null ? tableIdentifier : tableName)
					.replace("#existsExtSql", String.valueOf(existsExtSql))
					.replace("#extSqlView", extSqlViewName)
					.replace("#extSql", existsExtSql ? extSql.replace("$.baseTable", extSqlViewName) : "")
					.replace("#viewName", viewName)
					.replace("#isRawMode", String.valueOf(TemplateSchemeType.BINARY.equals(schemeType)))
					.replace("#config", TemplateSqlUtil.generateConfigStr(connectConfig, connector, false, schemeType))
					.replace("#hasExpectData", String.valueOf(hasExpectData))

//					.replace("#sinkResult", sinkResult)
					.replace("#sinkUniqueName", tableIdentifier != null ? tableIdentifier : tableName)
					.replace("#sinkType", connector);
				writer.write(templateSink);

			}
		}
		return getResultFunction();
	}


	@Override
	public Boolean validate() {
		if (connectConfig == null || StringUtil.isNullOrEmpty(connectConfig.getProperty("tableId"))) {
			return false;
		}
		if (schemaConfig == null) {
			return false;
		}
		return true;
	}
}

/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.dewu.flink.template.example;

import com.dewu.flink.template.template.GroupByTemplate;
import com.dewu.flink.template.template.TemplateWrapper;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.ObjectMapper;

import java.util.Properties;


public class StreamGroupByTemplateExample {
	public static void main(String[] args) throws Exception {

		GroupByTemplate groupByTemplate = new GroupByTemplate();

		groupByTemplate.setSourceTableId("340");
		groupByTemplate.setFilterExpression("userid=1");
		groupByTemplate.setSelectExpression("userid,max(product) as product,max(amount) as amount");
		groupByTemplate.setGroupByExpression("userid");
		groupByTemplate.setHavingExpression("amount=4");

		String kafkaSinkPrimaryKey = "userid";
		groupByTemplate.setSinkPrimaryKeys(kafkaSinkPrimaryKey);
		groupByTemplate.setSinkTableId("872");

		ObjectMapper mapper = new ObjectMapper();
		System.out.println(mapper.writeValueAsString(new TemplateWrapper("GroupByTemplate", mapper.writeValueAsString(groupByTemplate))));
	}
}

package com.dewu.flink.template.template.b2s.converbean;

import com.dewu.flink.template.utils.JsonUtil;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.core.JsonProcessingException;

import java.util.HashMap;
import java.util.Map;

/**
 * @Description: XXX
 * @author: zhiping.lin
 * @date: 2022/8/24
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class SinkBean extends AbstractTransformBean {
	String table;
	String primaryKey;
	String tableId;

	public SinkBean(String tableName, String tableId, String primaryKey) {
		this.setId(id_id.getAndIncrement());
		this.name = "SinkTransformation";
		this.primaryKey = primaryKey;
		this.table = tableName;
		this.tableId = tableId;
	}

	@Override
	public String wrapTransformation() throws JsonProcessingException {
		Map<String, Object> connectConfig = new HashMap<>();
		Map<String, Object> schemaConfig = new HashMap<>();
		Map<String, Object> transform = new HashMap<>();
		connectConfig.put("tableId", tableId);
		schemaConfig.put("primaryKey", primaryKey);
		transform.put("connectConfig", connectConfig);
		transform.put("schemaConfig", schemaConfig);
		return JsonUtil.writeString(transform);
	}

	@Override
	public String toString() {
		try {
			return getString();
		} catch (JsonProcessingException e) {
			e.printStackTrace();
		}
		return null;
	}
}

package com.dewu.flink.template.transform;

import com.dewu.flink.template.utils.TemplateSqlUtil;
import com.dewu.utils.StringUtils;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.annotation.JsonIgnore;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import org.apache.flink.shaded.netty4.io.netty.util.internal.StringUtil;

import java.io.IOException;
import java.io.StringWriter;
import java.util.Arrays;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * {
 * "pythonPath": "/usr/bin/python3.8",
 * "projectPath": "/opt/algo",
 * "pyUDF": "algo_udf.FlinkUdf()",
 * "pemMode": "multi_thread",
 * "otherPath": "/usr/lib/python3.8/site-packages",
 * "algoConfig": "{}",
 * "image": ""
 * }
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class AlgoTransformation extends OneInputTransformation {
	private String selectExpression;
	private String pythonPath = "/usr/bin/python3.8";
	private String projectPath = "/opt/flink/algo";
	private String pyUDF = "algo_udf.FlinkUdf()";
	private String pemMode = "multi_thread";
	private String otherPath = "/usr/lib/python3.8/site-packages";
	private String algoConfig = "";
	private String image = "";
	private String harbor_prefix = "";
	private String tag = "";
	@JsonIgnore
	private String viewName = "algo_" + UUID.randomUUID().toString().replace("-", "");

	public AlgoTransformation() {
	}

	@Override
	public Boolean isFieldNeed(String fieldName) {
		return null;
	}

	public String getPythonPath() {
		return pythonPath;
	}

	public void setPythonPath(String pythonPath) {
		this.pythonPath = pythonPath;
	}

	public String getProjectPath() {
		return projectPath;
	}

	public void setProjectPath(String projectPath) {
		this.projectPath = projectPath;
	}

	public String getPyUDF() {
		return pyUDF;
	}

	public void setPyUDF(String pyUDF) {
		this.pyUDF = pyUDF;
	}

	public String getPemMode() {
		return pemMode;
	}

	public void setPemMode(String pemMode) {
		this.pemMode = pemMode;
	}

	public String getOtherPath() {
		return otherPath;
	}

	public void setOtherPath(String otherPath) {
		this.otherPath = otherPath;
	}

	public String getAlgoConfig() {
		return algoConfig;
	}

	public void setAlgoConfig(String algoConfig) {
		this.algoConfig = algoConfig;
	}

	public String getImage() {
		return image;
	}

	public void setImage(String image) {
		this.image = image;
	}

	public String getHarbor_prefix() {
		return harbor_prefix;
	}

	public void setHarbor_prefix(String harbor_prefix) {
		this.harbor_prefix = harbor_prefix;
	}

	public String getTag() {
		return tag;
	}

	public void setTag(String tag) {
		this.tag = tag;
	}

	@Override
	public String transformStr(StringWriter writer) throws IOException {
		if (getResultFunction() != null) {
			return getResultFunction();
		}
		selectExpression = buildSelectExpr();
		String inputFunction = this.getInput().transformStr(writer);
		setResultFunction("algo_" + UUID.randomUUID().toString().replace("-", ""));
		writer.write(TemplateSqlUtil.readTemplate("Algo.template")
			.replace("#transformFunction", getResultFunction())
			.replace("#selectExpression", selectExpression)
			.replace("#lateralViewExpression", buildLateralExpr())
			.replace("#inputFunction", inputFunction)
			.replace("#viewName", viewName));
		return getResultFunction();
	}

	public String buildImage() {
		return harbor_prefix + image + ":" + tag;
	}

	private String buildSelectExpr() {
		return "json_value(outputMsg,'$.task_name') as task_name, " +
			"json_value(outputMsg,'$.tm_name') as tm_name, " +
			"json_value(outputMsg,'$.output') as output, " +
			"json_value(outputMsg,'$.input') as input, " +
			"json_value(outputMsg,'$.start_time') as start_time, " +
			"json_value(outputMsg,'$.cost_millis') as cost_millis, " +
			"json_value(outputMsg,'$.model_code') as model_code, " +
			"json_value(outputMsg,'$.code') as code, " +
			"json_value(outputMsg,'$.track_id') as track_id, " +
			"json_value(outputMsg,'$.udf_uuid') as udf_uuid, " +
			"json_value(outputMsg,'$.kubernetes_image') as kubernetes_image, " +
			"json_value(outputMsg,'$.ts') as ts";
	}

	private String buildOtherPath() {
		if (StringUtils.isEmpty(otherPath)) {
			return "''";
		}
		if (otherPath.contains("'") || otherPath.contains("\"")) {
			throw new RuntimeException("python path 中不应该有单引号，请检查参数");
		}
		return Arrays.stream(otherPath.split(",")).filter(StringUtils::isNotEmpty).map(path -> "'" + path + "'").collect(Collectors.joining(","));
	}

	private String buildLateralExpr() {
		return
			"lateral table(" +
				"PythonExec(" +
				"    inputMsg," +
				"    '" + pythonPath + "'," +
				"    '" + projectPath + "'," +
				"    '" + pyUDF + "'," +
				"    '" + pemMode + "'," +
				"    '" + algoConfig + "'," +
				buildOtherPath() +
				"    )) as T (" +
				"    outputMsg" +
				")";
	}

	@Override
	public Boolean validate() {
		return true;
	}
}

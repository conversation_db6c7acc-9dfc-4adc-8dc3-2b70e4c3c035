package com.dewu.flink.template.express.domain;

import com.dewu.flink.template.rule.base.express.FactoryExpression;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class CheckDataBean {


    @ApiModelProperty(value = "演练数据")
    private String checkData;

    @ApiModelProperty(value = "表达式")
    private String ruleExpress;

    @ApiModelProperty(value = "表达式")
    private String exprType = FactoryExpression.AVIATOR_EXPRESSION_TYPE;
}

package com.dewu.flink.template.template.b2s.converbean;

import com.dewu.flink.template.utils.JsonUtil;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.core.JsonProcessingException;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * @Description: XXX
 * @author: zhiping.lin
 * @date: 2022/8/25
 */
public abstract class AbstractTransformBean {
	static AtomicInteger id_id = new AtomicInteger();

	protected String selectExpression;
	protected int[] inputIds = new int[0];
	protected int id;
	protected String alias;
	protected String name;
	protected String uniqKey;

	public static AtomicInteger getId_id() {
		return id_id;
	}

	public static void setId_id(AtomicInteger id_id) {
		AbstractTransformBean.id_id = id_id;
	}

	public String getSelectExpression() {
		return selectExpression;
	}

	public void setSelectExpression(String selectExpression) {
		this.selectExpression = selectExpression;
	}

	public String getUniqKey() {
		return uniqKey;
	}

	public void setUniqKey(String uniqKey) {
		this.uniqKey = uniqKey;
	}

	public int[] getInputIds() {
		return inputIds;
	}

	public void setInputIds(int[] inputIds) {
		this.inputIds = inputIds;
	}

	public int getId() {
		return id;
	}

	public void setId(int id) {
		this.id = id;
	}

	public String getAlias() {
		return alias;
	}

	public void setAlias(String alias) {
		this.alias = alias;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getString() throws JsonProcessingException {
		Map<String, Object> configs = new HashMap<>();
		configs.put("id", id);
		configs.put("name", name);
		configs.put("inputIds", inputIds);
		configs.put("transformation", wrapTransformation());
		return JsonUtil.writeString(configs);
	}

	public abstract String wrapTransformation() throws JsonProcessingException;
}

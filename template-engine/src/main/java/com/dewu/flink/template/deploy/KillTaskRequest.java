package com.dewu.flink.template.deploy;

import com.dewu.flink.template.common.Request;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Author: WangLin
 * Date: 2022/7/24 下午2:25
 * Description: 下线作业请求参数
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class KillTaskRequest implements Request {

	/**
	 * 操作者
	 */
	private String operator;

	/**
	 * 作业id
	 */
	private String taskId;

	public KillTaskRequest(String taskId) {
		this.taskId = taskId;
	}
}

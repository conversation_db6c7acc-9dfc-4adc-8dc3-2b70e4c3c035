package com.dewu.flink.template.template;

import com.dewu.flink.template.chaos.ChaosRuleInfo;
import com.dewu.flink.template.config.SchemaConfig;
import com.dewu.flink.template.job.service.dto.TeJobDataDrillDto;
import com.dewu.flink.template.meta.GlobalMetaManager;
import com.dewu.flink.template.transform.CalcTransformation;
import com.dewu.flink.template.transform.DelayEmitTransformation;
import com.dewu.flink.template.transform.SinkTransformation;
import com.dewu.flink.template.transform.Transformation;
import com.dewu.flink.template.utils.JsonUtil;
import com.dewu.flink.template.utils.LibraTableMappingManager;
import com.dewu.flink.template.utils.TemplateSqlUtil;
import com.google.common.collect.BiMap;
import com.google.common.collect.HashBiMap;
import com.mysql.cj.util.StringUtils;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.annotation.JsonIgnore;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import org.apache.flink.shaded.netty4.io.netty.util.internal.StringUtil;
import org.apache.flink.util.CollectionUtil;

import java.io.IOException;
import java.io.StringWriter;
import java.util.*;

import static com.dewu.flink.template.translator.generator.TemplateOption.TABLE_ID;
import static com.dewu.flink.template.utils.Constant.DYNAMIC_JOB_NAME;

@Slf4j
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public abstract class AbstractTemplate implements Template {
	@JsonIgnore
	protected String jobType = "default";
	protected String jobName;
	protected String jobId = "";
	private String className;
	private String sinkTableId;
	private String sinkPrimaryKeys;
	// libra, b2s, rule, dqc
	protected String templateMode = "b2s";
	@JsonIgnore
	protected GlobalMetaManager globalMetaManager;
	protected LibraTableMappingManager libraTableMappingManager;
	protected Properties jobConfigurations;
	protected TeJobDataDrillDto teJobDataDrillDto;
	protected ChaosRuleInfo chaosRules;
	public Map<String, String> sinkPriKeyMeta = new HashMap<>();
	public Map<String, String> table2ViewNames = new HashMap<>();
	public Map<String, String> ddlMappings = new HashMap<>();
	// <name, id>
	public BiMap<String, String> tableMeta = HashBiMap.create();
	// <id, name>
	public BiMap<String, String> tableMetaInverse;

	String dimTimeTableId;
	String dimTimeTable;
	Set<String> sourceTableSet;

	public Transformation addCalcExpression(String selectExpression, String filterExpression, Transformation input) {
		Transformation output;
		if (!StringUtil.isNullOrEmpty(filterExpression) || !StringUtil.isNullOrEmpty(selectExpression)) {
			output = new CalcTransformation();
			((CalcTransformation) output).setInput(input);
			((CalcTransformation) output).setFilterExpression(filterExpression);
			((CalcTransformation) output).setSelectExpression(selectExpression);
		} else {
			output = input;
		}
		return output;
	}

	public Transformation addDelayEmit(Transformation input, Integer delayMinutes, String tableId, GlobalMetaManager globalMetaManager) {
		DelayEmitTransformation output = new DelayEmitTransformation();
		output.setInput(input);
		output.setDelayMinutes(delayMinutes);
		output.setDelayJoinTableId(tableId);
		output.setGlobalMetaManager(globalMetaManager);
		return output;
	}

	public Transformation generateSinkTransformation(Transformation input) {
		return generateSinkTransformation(input, "");
	}

	public Transformation generateSinkTransformation(Transformation input, String extSql) {
		return generateSinkTransformation(input, extSql, "");
	}

	public Transformation generateSinkTransformation(Transformation input, String extSql, String tableName) {
		SinkTransformation sinkTransformation = new SinkTransformation(jobType);
		sinkTransformation.setConnectConfig(wrapSimpleKeyPairConfig(TABLE_ID.key(), sinkTableId));
		if (!StringUtil.isNullOrEmpty(tableName)) {
			sinkTransformation.setTableName(tableName);
		}
		String primaryKey = "";
		if (jobName.trim().toUpperCase().startsWith("RT_DCHECK") || jobName.trim().toUpperCase().startsWith("DCHECK")) {
			primaryKey = "dCheckSubjectCode";
		} else {
			primaryKey = StringUtils.isEmptyOrWhitespaceOnly(sinkPrimaryKeys) ? "FLK_AUTO_PFLAG" : sinkPrimaryKeys;
		}
		SchemaConfig schemaConfig = new SchemaConfig(null, primaryKey);

		sinkTransformation.setSchemaConfig(schemaConfig);
		sinkTransformation.setInput(input);
		sinkTransformation.setExtSql(extSql);
		sinkTransformation.setGlobalMetaManager(getGlobalMetaManager());
		sinkTransformation.setDataDrill(teJobDataDrillDto.getIsDataDrill());
		jobConfigurations.setProperty(DYNAMIC_JOB_NAME, this.jobName);
		sinkTransformation.setNonNullContext(jobConfigurations);
		List<TeJobDataDrillDto.DrillData> expectData = teJobDataDrillDto.getExpectData();
		if (!CollectionUtil.isNullOrEmpty(expectData)) {
			sinkTransformation.setDrillData(expectData.get(0).getData());
		}
		Object o = this.jobConfigurations.get("dynamic.rule");
		sinkTransformation.setIsDynamicRule("true".equals(o == null ? "false" : o));
		return sinkTransformation;
	}

	public static Properties wrapSimpleKeyPairConfig(String key, String value) {
		Properties props = new Properties();
		props.setProperty(key, value);
		return props;
	}

	public Properties addStartTsConfig(Properties properties) {
		if (jobConfigurations.containsKey("scan.startup.mode")) {
			switch (jobConfigurations.getProperty("scan.startup.mode")) {
				case "timestamp":
					properties.setProperty("scan.startup.mode", "timestamp");
					properties.setProperty("scan.startup.timestamp-millis", getJobConfigurations().get("scan.startup.timestamp-millis").toString());
					break;
				case "earliest-offset":
					properties.setProperty("scan.startup.mode", "earliest-offset");
					break;
				default:
					properties.setProperty("scan.startup.mode", "latest-offset");
			}
		}
		return properties;
	}

	public static Properties wrapEmptyConfig() {
		return new Properties();
	}

	public static String buildWithSink(Transformation sinkTransformation, GlobalMetaManager globalMetaManager, String templateMode, TeJobDataDrillDto teJobDataDrillDto, String className, String jobName, String jobId, Properties jobConfigurations) throws IOException {
		StringWriter writer = new StringWriter();
		String udfList = globalMetaManager.getDeployInfo().getUdfJarList();
		StringBuilder udfStr = new StringBuilder();
		if (!"libra".equals(templateMode)) {
			Map<String, String> kvs = JsonUtil.parseObject(udfList, Map.class);
			for (Map.Entry<String, String> kv : kvs.entrySet()) {
				udfStr.append("tEnv.executeSql(\"CREATE FUNCTION if not exists ").append(kv.getKey()).append(" AS '").append(kv.getValue()).append("'\");");
			}
		}
		String globalsinkFunctionName = sinkTransformation.transformStr(writer);
		Boolean isDataDrill = teJobDataDrillDto.getIsDataDrill();
		String sinkFunctions = (isDataDrill ? "\tList<DataDrillSink> " + globalsinkFunctionName + " = " : "") + "#sinkFunction(tEnv);".replace("#sinkFunction", globalsinkFunctionName);
		String jobConfigurationStr = TemplateSqlUtil.generateJobConfigurationStr(jobConfigurations);
		String sinkExplainFunctions = ("\t\toperations.add(#sinkFunction(tEnv));\n".replace("#sinkFunction", globalsinkFunctionName.replace("sink", "sink_explain")));

		String ret = TemplateSqlUtil.readTemplate(isDataDrill ? "TotalDataDrill.template" : "Total.template").
			replace("#udfExpr", udfStr.toString())
			.replace("#sinkFunctions", sinkFunctions)
			.replace("#globalsinkFunctionName", globalsinkFunctionName)
			.replace("#sinkExplainFunctions", sinkExplainFunctions)
			.replace("#transformationFunction", writer.toString())
			.replace("#className", className)
			.replace("#jobConfigurationStr", jobConfigurationStr)
			.replace("#jobName", jobName)
			.replace("#jobId", null==jobId?"-1":jobId);

		writer.close();
		log.info(ret);
		System.out.println(ret);
		return ret;
	}

	protected String buildStrWithSink(Transformation sinkTransformation) throws IOException {
		return buildWithSink(sinkTransformation, getGlobalMetaManager(), getTemplateMode(), getTeJobDataDrillDto(), getClassName(), getJobName(), getJobId(), getJobConfigurations());
	}

}

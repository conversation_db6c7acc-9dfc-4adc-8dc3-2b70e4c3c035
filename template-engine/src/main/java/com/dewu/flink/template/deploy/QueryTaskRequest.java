package com.dewu.flink.template.deploy;

import com.dewu.flink.template.common.Request;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * Author: WangLin
 * Date: 2022/9/14 下午19:18
 * Description: 批量查询作业状态请求参数
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class QueryTaskRequest implements Request {
	/**
	 * 作业id数组
	 */
	private String[] taskIdList;

	/**
	 * 项目空间名称
	 */
	private String projectName;

	/**
	 * 作业名数
	 */
	private String taskName;

	public QueryTaskRequest(String[] taskIdList, String projectName) {
		this.taskIdList = taskIdList;
		this.projectName = projectName;
	}

	public QueryTaskRequest(String taskName) {
		this.taskName = taskName;
	}

}

/*
*  Copyright 2019-2020 <PERSON>
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.dewu.flink.template.lineage.rest;

import com.dewu.annotation.Log;
import com.dewu.flink.template.lineage.domain.TeAlgoImageLineage;
import com.dewu.flink.template.lineage.service.TeAlgoImageLineageService;
import com.dewu.flink.template.lineage.service.dto.TeAlgoImageLineageQueryCriteria;
import org.springframework.data.domain.Pageable;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import io.swagger.annotations.*;
import java.io.IOException;
import javax.servlet.http.HttpServletResponse;

/**
* @website https://el-admin.vip
* <AUTHOR>
* @date 2024-05-22
**/
@RestController
@RequiredArgsConstructor
@Api(tags = "CiLineageController管理")
@RequestMapping("/api/teAlgoImageLineage")
public class TeAlgoImageLineageController {

    private final TeAlgoImageLineageService teAlgoImageLineageService;

    @Log("导出数据")
    @ApiOperation("导出数据")
    @GetMapping(value = "/download")
    @PreAuthorize("@el.check('teAlgoImageLineage:list')")
    public void exportTeAlgoImageLineage(HttpServletResponse response, TeAlgoImageLineageQueryCriteria criteria) throws IOException {
        teAlgoImageLineageService.download(teAlgoImageLineageService.queryAll(criteria), response);
    }

    @GetMapping
    @ApiOperation("查询CiLineageController")
    @PreAuthorize("@el.check('teAlgoImageLineage:list')")
    public ResponseEntity<Object> queryTeAlgoImageLineage(TeAlgoImageLineageQueryCriteria criteria, Pageable pageable){
        return new ResponseEntity<>(teAlgoImageLineageService.queryAll(criteria,pageable),HttpStatus.OK);
    }

    @PostMapping
    @Log("新增CiLineageController")
    @ApiOperation("新增CiLineageController")
    @PreAuthorize("@el.check('teAlgoImageLineage:add')")
    public ResponseEntity<Object> createTeAlgoImageLineage(@Validated @RequestBody TeAlgoImageLineage resources){
        return new ResponseEntity<>(teAlgoImageLineageService.create(resources),HttpStatus.CREATED);
    }

    @PutMapping
    @Log("修改CiLineageController")
    @ApiOperation("修改CiLineageController")
    @PreAuthorize("@el.check('teAlgoImageLineage:edit')")
    public ResponseEntity<Object> updateTeAlgoImageLineage(@Validated @RequestBody TeAlgoImageLineage resources){
        teAlgoImageLineageService.update(resources);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }

    @DeleteMapping
    @Log("删除CiLineageController")
    @ApiOperation("删除CiLineageController")
    @PreAuthorize("@el.check('teAlgoImageLineage:del')")
    public ResponseEntity<Object> deleteTeAlgoImageLineage(@RequestBody Long[] ids) {
        teAlgoImageLineageService.deleteAll(ids);
        return new ResponseEntity<>(HttpStatus.OK);
    }
}

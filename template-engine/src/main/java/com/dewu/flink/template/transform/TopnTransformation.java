package com.dewu.flink.template.transform;

import com.dewu.flink.template.utils.TemplateSqlUtil;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.annotation.JsonIgnore;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import org.apache.flink.shaded.netty4.io.netty.util.internal.StringUtil;

import java.io.IOException;
import java.io.StringWriter;
import java.util.UUID;

// 有排名
@JsonIgnoreProperties(ignoreUnknown = true)
public class TopnTransformation extends OneInputTransformation {
    private String partitionExpression;
    private String selectExpression;
    private String orderByExpression;
    private Integer n;
    @JsonIgnore
    private String viewName = "topn_view_" + UUID.randomUUID().toString().replace("-","");

    public TopnTransformation() {
    }

    @Override
    public Boolean isFieldNeed(String Name) {
        return null;
    }

    public Integer getN() {
        return n == null ? 1 : n ;
    }

    public String getPartitionExpression() {
        return partitionExpression;
    }

    public String getSelectExpression() {
        return selectExpression;
    }

    public String getOrderByExpression() {
        return orderByExpression;
    }

    public void setN(Integer n) {
        this.n = n;
    }

    public void setPartitionExpression(String partitionExpression) {
        this.partitionExpression = partitionExpression;
    }

    public void setSelectExpression(String selectExpression) {
        this.selectExpression = selectExpression;
    }

    public void setOrderByExpression(String orderByExpression) {
        this.orderByExpression = orderByExpression;
    }

    @Override
    public String transformStr(StringWriter writer) throws IOException {
        if(getResultFunction() != null){
            return getResultFunction();
        }
        String inputFunction = this.getInput().transformStr(writer);
        setResultFunction("topn_"+ UUID.randomUUID().toString().replace("-",""));
        writer.write(TemplateSqlUtil.readTemplate("Topn.template")
                        .replace("#transformFunction",getResultFunction())
                        .replace("#viewName",viewName)
                        .replace("#inputFunction",inputFunction)
                        .replace("#selectStr", selectExpression)
                        .replace("#partitionStr",partitionExpression)
                        .replace("#orderByStr",orderByExpression)
                        .replace("#n",getN().toString()));
        return getResultFunction();
    }

    @Override
    public Boolean validate() {
        if(StringUtil.isNullOrEmpty(selectExpression)){
            return false;
        }
        if(StringUtil.isNullOrEmpty(partitionExpression)){
            return false;
        }
        if(StringUtil.isNullOrEmpty(orderByExpression)){
            return false;
        }
        return true;
    }
}

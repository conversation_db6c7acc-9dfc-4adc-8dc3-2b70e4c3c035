package com.dewu.flink.template.transform.domain;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import javax.persistence.*;
import java.io.Serializable;


/**
 *  transformation_config.
 */
@Entity
@DynamicInsert
@DynamicUpdate
@Data
@Table(name="transformation_config")
public class TransformationConfig implements Serializable {
	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Column(name = "`id`")
	@ApiModelProperty(value = "id")
	private Long id;


	@Column(name = "`name`",nullable = false)
	@ApiModelProperty(value = "名称")
	private String transformationName;

	@Column(name = "`type`",nullable = false)
	@ApiModelProperty(value = "归属类型")
	private String type;

	@Column(name = "`must_columns`",nullable = true)
	@ApiModelProperty(value = "必填配置Jsonarray")
	private String mustColumns;


	@Column(name = "`required_columns`",nullable = true)
	@ApiModelProperty(value = "非必填配置Jsonarray")
	private String requiredColumns;

	@Column(name = "`create_time`")
	@ApiModelProperty(value = "创建时间")
	private String createTime;

	@Column(name = "`modify_time`")
	@ApiModelProperty(value = "更新时间")
	private String modifyTime;

}

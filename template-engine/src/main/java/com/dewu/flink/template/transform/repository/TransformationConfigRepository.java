/*
 *  Copyright 2019-2020 <PERSON>
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */
package com.dewu.flink.template.transform.repository;

import com.dewu.flink.template.transform.domain.TransformationConfig;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

import java.util.List;
import java.util.Set;

/**
 * TransformationRepository for curd Transformation table
 */
public interface TransformationConfigRepository extends JpaRepository<TransformationConfig, Long>, JpaSpecificationExecutor<TransformationConfig> {

    /**
     * 根据任务id查询当前所有的transformation集合
     * @param name  任务id
     * @return /
     */
    List<TransformationConfig> findAllByTransformationName(String name);

    List<TransformationConfig> findAllByTransformationNameAndType(String name,String type);


    /**
     * 根据Id删除
     * @param ids /
     */
    void deleteAllByIdIn(Set<Long> ids);

}
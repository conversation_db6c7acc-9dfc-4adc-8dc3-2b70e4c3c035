package com.dewu.flink.template.deploy;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.dewu.exception.JobException;
import com.dewu.flink.template.job.domain.TaskMonitor;
import com.dewu.flink.template.job.domain.TaskStatus;
import com.dewu.flink.template.utils.JsonUtil;
import com.dewu.utils.HttpClientUtil;
import com.dewu.utils.StringUtils;
import com.dewu.utils.enums.ExceptionType;
import com.dewu.utils.enums.RealtimePlatformOpenApiEnum;
import com.google.common.base.Preconditions;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import scala.tools.nsc.Global;

import javax.xml.bind.DatatypeConverter;
import java.nio.charset.StandardCharsets;
import java.util.*;

/**
 * Author: WangLin
 * Date: 2022/7/22 上午11:46
 * Description: 得物自建实时平台任务部署管理
 */
@Component
@Slf4j
public class RealtimePlatformDeployManager implements DeployManager {

	private static final Logger log = LoggerFactory.getLogger(RealtimePlatformDeployManager.class);

	@Value("${dw.realtime-platform.appId}")
	private String appId;

	@Value("${dw.realtime-platform.appSecret}")
	private String appSecret;

	@Value("${dw.realtime-platform.baseUrl}")
	private String baseUrl;


	@Override
	public SubmitTaskResponse submitTask(SubmitTaskRequest sr) throws Exception {

		Preconditions.checkNotNull(sr, "参数不能为空");

		Preconditions.checkNotNull(sr.getTaskId(), "参数内的taskId不能为空");

		String encode = DatatypeConverter.printBase64Binary((appId + ":" + appSecret).getBytes(StandardCharsets.UTF_8));

		Map<String, String> map = new HashMap<>();
		map.put("Authorization", "Basic " + encode);

		String requestFullUrl = baseUrl + RealtimePlatformOpenApiEnum.SUBMIT.getUrl();

		String requestJson = JSON.toJSONString(sr);

		HttpClientUtil.HttpClientResult httpClientResult = HttpClientUtil.doPost(requestFullUrl, map, requestJson);

		String code = JSON.parseObject(httpClientResult.getContent()).getString("code");
		String msg = JSON.parseObject(httpClientResult.getContent()).getString("msg");

		SubmitTaskResponse submitTaskResponse = new SubmitTaskResponse();
		submitTaskResponse.setFlag("200".equals(code));
		submitTaskResponse.setMsg(msg);

		return submitTaskResponse;
	}

	@Override
	public KillTaskResponse killTask(KillTaskRequest kr) throws Exception {

		Preconditions.checkNotNull(kr, "参数不能为空");

		Preconditions.checkNotNull(kr.getTaskId(), "参数内的taskId不能为空");

		String encode = DatatypeConverter.printBase64Binary((appId + ":" + appSecret).getBytes(StandardCharsets.UTF_8));

		Map<String, String> map = new HashMap<>();
		map.put("Authorization", "Basic " + encode);

		String requestFullUrl = baseUrl + RealtimePlatformOpenApiEnum.KILL.getUrl();

		String requestJson = JSON.toJSONString(kr);

		HttpClientUtil.HttpClientResult httpClientResult = HttpClientUtil.doPost(requestFullUrl, map, requestJson);

		String code = JSON.parseObject(httpClientResult.getContent()).getString("code");
		String msg = JSON.parseObject(httpClientResult.getContent()).getString("msg");

		KillTaskResponse killTaskResponse = new KillTaskResponse();
		killTaskResponse.setFlag("200".equals(code));
		killTaskResponse.setMsg(msg);

		return killTaskResponse;
	}

	@Override
	public DeleteTaskResponse deleteTask(DeleteTaskRequest dr) throws Exception {

		Preconditions.checkNotNull(dr, "参数不能为空");

		Preconditions.checkNotNull(dr.getTaskId(), "参数内的taskId不能为空");

		String encode = DatatypeConverter.printBase64Binary((appId + ":" + appSecret).getBytes(StandardCharsets.UTF_8));

		Map<String, String> map = new HashMap<>();
		map.put("Authorization", "Basic " + encode);

		String requestFullUrl = baseUrl + RealtimePlatformOpenApiEnum.DELETE.getUrl() + "/" + dr.getTaskId();

		HttpClientUtil.HttpClientResult httpClientResult = HttpClientUtil.doPost(requestFullUrl, map, null);

		String code = JSON.parseObject(httpClientResult.getContent()).getString("code");
		String msg = JSON.parseObject(httpClientResult.getContent()).getString("msg");

		DeleteTaskResponse deleteTaskResponse = new DeleteTaskResponse();
		deleteTaskResponse.setFlag("200".equals(code));
		deleteTaskResponse.setMsg(msg);

		return deleteTaskResponse;
	}

	@Override
	public CreateTaskResponse createTask(CreateTaskRequest cr) throws Exception {

		log.info("createTask入参为{}",JSON.toJSONString(cr));

		Preconditions.checkNotNull(cr, "参数不能为空");

		//还有一些校验...todo

		String encode = DatatypeConverter.printBase64Binary((appId + ":" + appSecret).getBytes(StandardCharsets.UTF_8));

		Map<String, String> map = new HashMap<>();
		map.put("Authorization", "Basic " + encode);

		String requestFullUrl = baseUrl + RealtimePlatformOpenApiEnum.CREATE.getUrl();

		String requestJson = JSON.toJSONString(cr);

		HttpClientUtil.HttpClientResult httpClientResult = HttpClientUtil.doPost(requestFullUrl, map, requestJson);

		String code = JSON.parseObject(httpClientResult.getContent()).getString("code");
		String msg = JSON.parseObject(httpClientResult.getContent()).getString("msg");
		String taskId = JSON.parseObject(httpClientResult.getContent()).getString("data");

		CreateTaskResponse createTaskResponse = new CreateTaskResponse();
		System.out.println("deploy rst : " + taskId + " deployed with status " + code + ". msg [" + msg + "].");
		createTaskResponse.setFlag("200".equals(code));
		createTaskResponse.setMsg(msg);
		createTaskResponse.setTaskId(taskId);

		return createTaskResponse;
	}

	@Override
	public UpdataTaskResponse updateTask(UpdateTaskRequest ur) throws Exception {

		Preconditions.checkNotNull(ur, "参数不能为空");

		Preconditions.checkNotNull(ur.getTaskId(), "参数内的taskId不能为空");

		String encode = DatatypeConverter.printBase64Binary((appId + ":" + appSecret).getBytes(StandardCharsets.UTF_8));

		Map<String, String> map = new HashMap<>();
		map.put("Authorization", "Basic " + encode);

		String requestFullUrl = baseUrl + RealtimePlatformOpenApiEnum.UPDATE.getUrl();

		String requestJson = JSON.toJSONString(ur);

		HttpClientUtil.HttpClientResult httpClientResult = HttpClientUtil.doPost(requestFullUrl, map, requestJson);

		String code = JSON.parseObject(httpClientResult.getContent()).getString("code");
		String msg = JSON.parseObject(httpClientResult.getContent()).getString("msg");

		UpdataTaskResponse updataTaskResponse = new UpdataTaskResponse();
		updataTaskResponse.setFlag("200".equals(code));
		updataTaskResponse.setMsg(msg);

		return updataTaskResponse;
	}

	@Override
	public QueryTaskResponse queryJobStatus(QueryTaskRequest qr) throws Exception {
		Preconditions.checkNotNull(qr, "参数不能为空");

		Preconditions.checkNotNull(qr.getTaskIdList(), "参数内的taskId数组不能为空");

		String encode = DatatypeConverter.printBase64Binary((appId + ":" + appSecret).getBytes(StandardCharsets.UTF_8));

		Map<String, String> map = new HashMap<>();
		map.put("Authorization", "Basic " + encode);

		String requestFullUrl = baseUrl + RealtimePlatformOpenApiEnum.QUERY.getUrl();

		String requestJson = JSON.toJSONString(qr);

		HttpClientUtil.HttpClientResult httpClientResult = HttpClientUtil.doPost(requestFullUrl, map, requestJson);

		String code = JSON.parseObject(httpClientResult.getContent()).getString("code");
		String msg = JSON.parseObject(httpClientResult.getContent()).getString("msg");
		List<JSONObject> data = JSON.parseObject(httpClientResult.getContent()).getJSONArray("data").toJavaList(JSONObject.class);

		List<TaskStatus> taskStatusList = new ArrayList<>();

		data.forEach(item -> {
			String taskId = item.getString("taskId");
			Integer status = item.getInteger("status");
			TaskStatus taskStatus = new TaskStatus();
			taskStatus.setTaskId(taskId);
			taskStatus.setStatus(status);
			taskStatusList.add(taskStatus);
		});

		QueryTaskResponse queryTaskResponse = new QueryTaskResponse();
		queryTaskResponse.setFlag("200".equals(code));
		queryTaskResponse.setMsg(msg);
		queryTaskResponse.setTaskStatusList(taskStatusList);

		return queryTaskResponse;
	}

	@Override
	public QueryTaskResponseV2 queryLibraTask(QueryTaskRequest qr) throws Exception {
		Preconditions.checkNotNull(qr, "参数不能为空");

		String encode = DatatypeConverter.printBase64Binary((appId + ":" + appSecret).getBytes(StandardCharsets.UTF_8));

		Map<String, String> map = new HashMap<>();
		map.put("Authorization", "Basic " + encode);

		String requestFullUrl = baseUrl + RealtimePlatformOpenApiEnum.QUERY.getUrl();

		String requestJson = JSON.toJSONString(qr);

		HttpClientUtil.HttpClientResult httpClientResult = HttpClientUtil.doPost(requestFullUrl, map, requestJson);

		String content = httpClientResult.getContent();

		if (StringUtils.isEmpty(content)) {
			log.error("libra api 调用异常,url:{} \n requestJson:{}", requestFullUrl, requestJson);
			throw new RuntimeException("libra api 调用异常");
		}

		QueryTaskResponseV2 taskResponse = JsonUtil.parseObject(content, QueryTaskResponseV2.class);

		if (200 != taskResponse.getCode()) {
			log.error("libra api 调用异常,url:{} \n requestJson:{}", requestFullUrl, requestJson);
			throw new RuntimeException("libra api 调用异常");
		}

		return taskResponse;
	}

	@Override
	public BatchQueryTaskResponse batchQueryLibraTask(BatchQueryTaskRequest qr) throws Exception {
		Preconditions.checkNotNull(qr, "参数不能为空");

		String encode = DatatypeConverter.printBase64Binary((appId + ":" + appSecret).getBytes(StandardCharsets.UTF_8));

		Map<String, String> map = new HashMap<>();
		map.put("Authorization", "Basic " + encode);

		String requestFullUrl = baseUrl + RealtimePlatformOpenApiEnum.QUERY_LIST_PAGE.getUrl();

		String requestJson = JSON.toJSONString(qr);

		HttpClientUtil.HttpClientResult httpClientResult = HttpClientUtil.doPost(requestFullUrl, map, requestJson);

		String content = httpClientResult.getContent();

		if (StringUtils.isEmpty(content)) {
			log.error("libra api 调用异常,url:{} \n requestJson:{}", requestFullUrl, requestJson);
			throw new RuntimeException("libra api 调用异常");
		}

		BatchQueryTaskResponse taskResponse = JsonUtil.parseObject(content, BatchQueryTaskResponse.class);

		if (200 != taskResponse.getCode()) {
			log.error("libra api 调用异常,url:{} \n requestJson:{}", requestFullUrl, requestJson);
			throw new RuntimeException("libra api 调用异常");
		}

		return taskResponse;
	}

	@Override
	public QueryTaskMonitorResponse queryTaskMonitor(QueryTaskMonitorRequest qr) throws Exception {
		Preconditions.checkNotNull(qr, "参数不能为空");

		Preconditions.checkNotNull(qr.getTaskId(), "参数内的taskId不能为空");

		String encode = DatatypeConverter.printBase64Binary((appId + ":" + appSecret).getBytes(StandardCharsets.UTF_8));

		Map<String, String> map = new HashMap<>();
		map.put("Authorization", "Basic " + encode);

		String requestFullUrl = baseUrl + RealtimePlatformOpenApiEnum.MONITOR_LIST.getUrl();

		Map<String, String> paramMap = new HashMap<>();
		paramMap.put("taskId", qr.getTaskId());

		HttpClientUtil.HttpClientResult httpClientResult = HttpClientUtil.doGet(requestFullUrl, map, paramMap);

		String code = JSON.parseObject(httpClientResult.getContent()).getString("code");
		String msg = JSON.parseObject(httpClientResult.getContent()).getString("msg");

		QueryTaskMonitorResponse queryTaskMonitorResponse = new QueryTaskMonitorResponse();
		queryTaskMonitorResponse.setFlag("200".equals(code));
		queryTaskMonitorResponse.setMsg(msg);
		TaskMonitor taskMonitor = JSON.parseObject(JSON.parseObject(httpClientResult.getContent()).getString("data"), TaskMonitor.class);
		queryTaskMonitorResponse.setTaskMonitor(taskMonitor);

		return queryTaskMonitorResponse;
	}

	@Override
	public UpdateTaskMonitorResponse updateTaskMonitor(UpdateTaskMonitorRequest ur) throws Exception {
		Preconditions.checkNotNull(ur, "参数不能为空");

		Preconditions.checkNotNull(ur.getTaskMonitor(), "参数内的taskMonitor不能为空");

		String encode = DatatypeConverter.printBase64Binary((appId + ":" + appSecret).getBytes(StandardCharsets.UTF_8));

		Map<String, String> map = new HashMap<>();
		map.put("Authorization", "Basic " + encode);

		String requestFullUrl = baseUrl + RealtimePlatformOpenApiEnum.MONITOR_UPDATE.getUrl();

		HttpClientUtil.HttpClientResult httpClientResult = HttpClientUtil.doPost(requestFullUrl, map, JSON.toJSONString(ur.getTaskMonitor()));

		String code = JSON.parseObject(httpClientResult.getContent()).getString("code");
		String msg = JSON.parseObject(httpClientResult.getContent()).getString("msg");

		UpdateTaskMonitorResponse updateTaskMonitorResponse = new UpdateTaskMonitorResponse();
		updateTaskMonitorResponse.setFlag("200".equals(code));
		updateTaskMonitorResponse.setMsg(msg);

		return updateTaskMonitorResponse;
	}

	@Override
	public QueryTaskCheckpointResponse queryTaskCheckpoint(QueryTaskCheckpointRequest qr,String storageType) throws Exception {
		Preconditions.checkNotNull(qr, "参数不能为空");

		Preconditions.checkNotNull(qr.getTaskId(), "参数内的taskId不能为空");

		String encode = DatatypeConverter.printBase64Binary((appId + ":" + appSecret).getBytes(StandardCharsets.UTF_8));

		Map<String, String> map = new HashMap<>();
		map.put("Authorization", "Basic " + encode);

		String requestFullUrl;
		if(storageType.equals("oss")){
			requestFullUrl = baseUrl + RealtimePlatformOpenApiEnum.CHECKPOINT_LIST_OSS.getUrl() + qr.getTaskId();
		}else{
			requestFullUrl = baseUrl + RealtimePlatformOpenApiEnum.CHECKPOINT_LIST_HDFS.getUrl() + qr.getTaskId();
		}
		HttpClientUtil.HttpClientResult httpClientResult = HttpClientUtil.doGet(requestFullUrl, map, null);

		String code = JSON.parseObject(httpClientResult.getContent()).getString("code");
		String msg = JSON.parseObject(httpClientResult.getContent()).getString("msg");

		QueryTaskCheckpointResponse queryTaskCheckpointResponse = new QueryTaskCheckpointResponse();
		queryTaskCheckpointResponse.setFlag("200".equals(code));
		queryTaskCheckpointResponse.setMsg(msg);

		List<JSONObject> data = JSON.parseObject(httpClientResult.getContent()).getJSONArray("data").toJavaList(JSONObject.class);

		if (!data.isEmpty()) {
			queryTaskCheckpointResponse.setLatestCheckpointPath(Optional.ofNullable(data.get(0).getString("path")));
		} else {
			queryTaskCheckpointResponse.setLatestCheckpointPath(Optional.empty());
		}

		return queryTaskCheckpointResponse;
	}

	/**
	 * 查询作业最新一次checkpoint-默认存储在oss
	 *
	 * @param qr 请求参数
	 * @return QueryTaskCheckpointResponse
	 * @throws Exception
	 */
	@Override
	public QueryTaskCheckpointResponse queryTaskCheckpoint(QueryTaskCheckpointRequest qr) throws Exception {
		return queryTaskCheckpoint(qr,"oss");
	}
}

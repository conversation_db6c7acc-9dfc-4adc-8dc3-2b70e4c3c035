/*
 *  Copyright 2019-2020 <PERSON>
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */
package com.dewu.flink.template.express.service.impl;

import com.dewu.flink.template.express.domain.TeJobRuleExpress;
import com.dewu.flink.template.express.repository.TeJobRuleExpressRepository;
import com.dewu.flink.template.express.service.TeJobRuleExpressService;
import com.dewu.flink.template.express.service.dto.TeJobRuleExpressDto;
import com.dewu.flink.template.express.service.dto.TeJobRuleExpressQueryCriteria;
import com.dewu.flink.template.express.service.mapstruct.TeJobRuleExpressMapper;
import com.dewu.utils.FileUtil;
import com.dewu.utils.PageUtil;
import com.dewu.utils.QueryHelp;
import com.dewu.utils.ValidationUtil;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.*;

/**
 * <AUTHOR>
 * @website https://el-admin.vip
 * @description 服务实现
 * @date 2023-02-08
 **/
@Service
@RequiredArgsConstructor
public class TeJobRuleExpressServiceImpl implements TeJobRuleExpressService {
	private static final Logger log = LoggerFactory.getLogger(TeJobRuleExpressServiceImpl.class);

	private final TeJobRuleExpressRepository teJobRuleExpressRepository;
	private final TeJobRuleExpressMapper teJobRuleExpressMapper;

	@Override
	public Map<String, Object> queryAll(TeJobRuleExpressQueryCriteria criteria, Pageable pageable) {
		Page<TeJobRuleExpress> page = teJobRuleExpressRepository.findAll((root, criteriaQuery, criteriaBuilder) -> QueryHelp.getPredicate(root, criteria, criteriaBuilder), pageable);
		return PageUtil.toPage(page.map(teJobRuleExpressMapper::toDto));
	}

	@Override
	public List<TeJobRuleExpressDto> queryAll(TeJobRuleExpressQueryCriteria criteria) {
		return teJobRuleExpressMapper.toDto(teJobRuleExpressRepository.findAll((root, criteriaQuery, criteriaBuilder) -> QueryHelp.getPredicate(root, criteria, criteriaBuilder)));
	}

	@Override
	@Transactional
	public TeJobRuleExpressDto findById(Long id) {
		TeJobRuleExpress teJobRuleExpress = teJobRuleExpressRepository.findById(id).orElseGet(TeJobRuleExpress::new);
		ValidationUtil.isNull(teJobRuleExpress.getId(), "TeJobRuleExpress", "id", id);
		return teJobRuleExpressMapper.toDto(teJobRuleExpress);
	}

	@Override
	@Transactional
	public int countByMetadataId(Long metadataId) {
		int count = teJobRuleExpressRepository.countByMetadataId(metadataId);
		return count;
	}

	@Override
	public int countByMetadataIdWithoutId(Long id, Long metadataId) {
		int count = teJobRuleExpressRepository.countByMetadataIdWithoutId(id, metadataId);
		return count;
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public TeJobRuleExpressDto create(TeJobRuleExpress resources) {
		return teJobRuleExpressMapper.toDto(teJobRuleExpressRepository.save(resources));
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void update(TeJobRuleExpress resources) {
		TeJobRuleExpress teJobRuleExpress = teJobRuleExpressRepository.findById(resources.getId()).orElseGet(TeJobRuleExpress::new);
		ValidationUtil.isNull(teJobRuleExpress.getId(), "TeJobRuleExpress", "id", resources.getId());
		teJobRuleExpress.copy(resources);
		teJobRuleExpressRepository.save(teJobRuleExpress);
	}

	@Override
	public void deleteAll(Long[] ids) {
		for (Long id : ids) {
			teJobRuleExpressRepository.deleteById(id);
		}
	}

	@Override
	public void download(List<TeJobRuleExpressDto> all, HttpServletResponse response) throws IOException {
		List<Map<String, Object>> list = new ArrayList<>();
		for (TeJobRuleExpressDto teJobRuleExpress : all) {
			Map<String, Object> map = new LinkedHashMap<>();
			map.put("表达式", teJobRuleExpress.getRuleExpress());
			map.put("作业id", teJobRuleExpress.getTeJobInfoId());
			map.put("规则组id", teJobRuleExpress.getTeJobRuleGroupId());
			map.put("创建时间", teJobRuleExpress.getCreateTime());
			map.put("修改时间", teJobRuleExpress.getModifyTime());
			map.put("规则输出目标表", teJobRuleExpress.getReDataSourceMetadataId());
			list.add(map);
		}
		FileUtil.downloadExcel(list, response);
	}

	@Override
	public void updateStatus(Long id) {
		teJobRuleExpressRepository.updateStatus(id);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void updateJobRules(String jobId, Map<String, String> principles, String exprType) {
		teJobRuleExpressRepository.deleteByTeJobInfoId(jobId);
		Map<String, String> filters = new HashMap<>();
		Map<String, String> marks = new HashMap<>();
		principles.forEach((k, v) -> {
			if ("DYNAMIC_RULE_FILTER_FLAG".equals(v)) {
				filters.put(k, v);
			} else {
				marks.put(k, v);
			}
		});
		filters.forEach((key, value) -> teJobRuleExpressRepository.save(TeJobRuleExpress.buildInUsedEtt(jobId, key, 1L, exprType, value)));
		marks.forEach((key, value) -> teJobRuleExpressRepository.save(TeJobRuleExpress.buildInUsedEtt(jobId, key, 0L, exprType, value)));
		log.info("principles for {} updated.", jobId);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void deleteJobRules(List<String> jobIds) {
		jobIds.forEach(teJobRuleExpressRepository::deleteByTeJobInfoId);
	}
}
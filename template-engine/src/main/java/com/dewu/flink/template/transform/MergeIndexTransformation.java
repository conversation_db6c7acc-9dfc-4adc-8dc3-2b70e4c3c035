package com.dewu.flink.template.transform;

import com.dewu.flink.template.utils.TemplateSqlUtil;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.annotation.JsonIgnore;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import java.io.IOException;
import java.io.StringWriter;
import java.util.UUID;

@EqualsAndHashCode(callSuper = true)
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class MergeIndexTransformation extends OneInputTransformation {
	private String dimNames;
	private String udatfClass;
	private String resultCols;
	private String finalSelect;
	@JsonIgnore
	private String viewName = "view_" + UUID.randomUUID().toString().replace("-", "");
	public MergeIndexTransformation() {
	}

	@Override
	public Boolean isFieldNeed(String fieldName) {
		return null;
	}

	@Override
	public String transformStr(StringWriter writer) throws IOException {
		if (getResultFunction() != null) {
			return getResultFunction();
		}
		String inputFunction = this.getInput().transformStr(writer);
		setResultFunction("calc_" + UUID.randomUUID().toString().replace("-", ""));

		writer.write(TemplateSqlUtil.readTemplate("MergeIndex.template")
			.replace("#transformFunction", getResultFunction())
			.replace("#inputFunction", inputFunction)
			.replace("#viewName", viewName)
			.replace("#dimNames", dimNames)
			.replace("#resultCols", resultCols)
			.replace("#finalSelect", finalSelect)
			.replace("#udatfClass", udatfClass));
		return getResultFunction();
	}

	@Override
	public Boolean validate() {
		return true;
	}
}

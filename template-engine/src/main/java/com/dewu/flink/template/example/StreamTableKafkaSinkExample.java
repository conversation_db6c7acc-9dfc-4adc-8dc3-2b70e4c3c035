/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.dewu.flink.template.example;

import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.flink.table.api.*;
import org.apache.flink.table.api.bridge.java.StreamTableEnvironment;

import java.util.Arrays;
import java.util.Objects;

import static org.apache.flink.table.api.Expressions.$;


public class StreamTableKafkaSinkExample {

    // *************************************************************************
    //     PROGRAM
    // *************************************************************************

    public static void main(String[] args) throws Exception {

        final ParameterTool params = ParameterTool.fromArgs(args);
        String planner = params.has("planner") ? params.get("planner") : "blink";

        // set up execution environment
        StreamExecutionEnvironment env = StreamExecutionEnvironment.getExecutionEnvironment();
        StreamTableEnvironment tEnv;
        if (Objects.equals(planner, "blink")) { // use blink planner in streaming mode
            EnvironmentSettings settings =
                    EnvironmentSettings.newInstance().inStreamingMode().useBlinkPlanner().build();
            tEnv = StreamTableEnvironment.create(env, settings);
        } else if (Objects.equals(planner, "flink")) { // use flink planner in streaming mode
            EnvironmentSettings settings =
                    EnvironmentSettings.newInstance().inStreamingMode().useOldPlanner().build();
            tEnv = StreamTableEnvironment.create(env, settings);
        } else {
            System.err.println(
                    "The planner is incorrect. Please run 'StreamSQLExample --planner <planner>', "
                            + "where planner (it is either flink or blink, and the default is blink) indicates whether the "
                            + "example uses flink planner or blink planner.");
            return;
        }

        DataStream<Order> orderA =
                env.fromCollection(
                        Arrays.asList(
                                new Order(1L, "beer", 3),
                                new Order(1L, "diaper", 4),
                                new Order(3L, "rubber", 2)));

        Table tableA = tEnv.fromDataStream(orderA, $("userid"), $("product"), $("amount"));

//        tEnv.createTemporaryTable("kafka_sink_order", TableDescriptor.forConnector("kafka")
//                .schema(Schema.newBuilder()
//                        .column("userid", DataTypes.BIGINT())
//                        .column("product",DataTypes.STRING())
//                        .column("amount",DataTypes.BIGINT())
//                        .build())
//                .option(KafkaConnectorOptions.PROPS_BOOTSTRAP_SERVERS, "***********:9092")
//                .option(KafkaConnectorOptions.TOPIC.key(), "hulin_test")
//                .option(KafkaConnectorOptions.VALUE_FORMAT,"json")
//                .build());
        tEnv.executeSql("CREATE TABLE kafka_sink_order (\n" +
                "  userid BIGINT,\n" +
                "  product STRING,\n" +
                "  amount BIGINT\n" +
                ") WITH (\n" +
                "  'connector' = 'kafka',\n" +
                "  'topic' = 'hulin_test',\n" +
                "  'properties.bootstrap.servers' = '***********:9092',\n" +
                "  'value.format' = 'json'\n" +
                ")");

        tableA.executeInsert("kafka_sink_order");

        DataStream<Product> productA =
                env.fromCollection(
                        Arrays.asList(
                                new Product("beer" , 3,"red"),
                                new Product( "diaper", 4,"black"),
                                new Product("rubber", 2,"blue")));

        Table tableB = tEnv.fromDataStream(productA, $("product"), $("price"), $("color"));

//        tEnv.createTemporaryTable("kafka_sink_product", TableDescriptor.forConnector("kafka")
//                .schema(Schema.newBuilder()
//                        .column("product", DataTypes.STRING())
//                        .column("price",DataTypes.INT())
//                        .column("color",DataTypes.STRING())
//                        .build())
//                .option(KafkaConnectorOptions.PROPS_BOOTSTRAP_SERVERS, "***********:9092")
//                .option(KafkaConnectorOptions.TOPIC.key(), "hulin_sink_test")
//                .option(KafkaConnectorOptions.VALUE_FORMAT,"json")
//                .build());
        tEnv.executeSql("CREATE TABLE kafka_sink_product (\n" +
                "  product STRING,\n" +
                "  price INT,\n" +
                "  color STRING\n" +
                ") WITH (\n" +
                "  'connector' = 'kafka',\n" +
                "  'topic' = 'hulin_sink_test',\n" +
                "  'properties.bootstrap.servers' = '***********:9092',\n" +
                "  'value.format' = 'json'\n" +
                ")");

        tableB.executeInsert("kafka_sink_product");
    }

    // *************************************************************************
    //     USER DATA TYPES
    // *************************************************************************

    /** Simple POJO. */
    public static class Order {
        public Long userid;
        public String product;
        public int amount;

        public Order() {}

        public Order(Long userid, String product, int amount) {
            this.userid = userid;
            this.product = product;
            this.amount = amount;
        }

        @Override
        public String toString() {
            return "Order{"
                    + "userid="
                    + userid
                    + ", product='"
                    + product
                    + '\''
                    + ", amount="
                    + amount
                    + '}';
        }
    }
    public static class Product {
        public String product;
        public int price;
        public String color;

        public Product() {}

        public Product(String product, int price,String color) {
            this.product = product;
            this.price = price;
            this.color = color;
        }

        @Override
        public String toString() {
            return "Product{"
                    + "product="
                    + product
                    + ", price='"
                    + price
                    + '\''
                    + ", color="
                    + color
                    + '}';
        }
    }
}

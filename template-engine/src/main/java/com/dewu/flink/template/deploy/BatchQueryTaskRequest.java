package com.dewu.flink.template.deploy;

import com.dewu.flink.template.common.Request;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * Author: WangLin
 * Date: 2022/9/14 下午19:18
 * Description: 批量查询作业状态请求参数
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class BatchQueryTaskRequest implements Request {
    private List<String> taskIdList;

    private List<String> projectNames;

    private List<Integer> statusList;

    //根据任务名称、项目名、负责人过滤
    private String key;

    private Integer pageIndex;

    private Integer pageSize;

    private List<String> taskNames;

    private Boolean pullResourceInfo;

    private String operator;

    public void checkPageParam() {

		if (pageSize == null) {
			throw new RuntimeException("pageSize Is Mandatory Fields.");
		}

        if (taskIdList == null && statusList == null && projectNames == null && key == null && operator == null) {
            throw new RuntimeException("It is not allowed to have no filtered fields, otherwise a full table scan may occur.");
        }

    }

}

/*
*  Copyright 2019-2020 <PERSON>
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.dewu.flink.template.lineage.domain;

import lombok.Data;
import cn.hutool.core.bean.BeanUtil;
import io.swagger.annotations.ApiModelProperty;
import cn.hutool.core.bean.copier.CopyOptions;
import javax.persistence.*;
import javax.validation.constraints.*;
import java.sql.Timestamp;
import java.io.Serializable;

/**
* @website https://el-admin.vip
* @description /
* <AUTHOR>
* @date 2024-05-22
**/
@Entity
@Data
@Table(name="te_algo_image_lineage")
public class TeAlgoImageLineage implements Serializable {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "`id`")
    @ApiModelProperty(value = "自增id")
    private Long id;

    @Column(name = "`image`",nullable = false)
    @NotBlank
    @ApiModelProperty(value = "镜像名称")
    private String image;

    @Column(name = "`tag`",nullable = false)
    @NotBlank
    @ApiModelProperty(value = "镜像标签")
    private String tag;

    @Column(name = "`job_id`",nullable = false)
    @NotNull
    @ApiModelProperty(value = "作业id")
    private Long jobId;

    @Column(name = "`job_name`",nullable = false)
    @NotBlank
    @ApiModelProperty(value = "作业名称")
    private String jobName;

    @Column(name = "`job_status`",nullable = false)
    @NotNull
    @ApiModelProperty(value = "-1:已删除 0:初始化 1:已创建作业/flink未启动 2:部署中/启动中/flink发布中 3:job上线中/flink运行中 4:job启动失败/flink发布失败 5:job下线中/flink下线中 6:job已下线/flink已下线  7:job运行异常/flink运行异常 8:flink运行已结束")
    private Integer jobStatus;

    @Column(name = "`department_label`",nullable = false)
    @NotBlank
    @ApiModelProperty(value = "部门标签  所属业务域")
    private String departmentLabel;

    @Column(name = "`create_user_name`",nullable = false)
    @NotBlank
    @ApiModelProperty(value = "job创建人")
    private String createUserName;

    @Column(name = "`libra_owers`",nullable = false)
    @NotBlank
    @ApiModelProperty(value = "libra作业负责人")
    private String libraOwers;

    @Column(name = "`job_create_time`",nullable = false)
    @NotBlank
    @ApiModelProperty(value = "任务创建时间")
    private String jobCreateTime;

    @Column(name = "`job_update_time`",nullable = false)
    @NotBlank
    @ApiModelProperty(value = "任务更新时间")
    private String jobUpdateTime;

    @Column(name = "`create_time`",nullable = false)
    @NotNull
    @ApiModelProperty(value = "创建时间")
    private Timestamp createTime;

    @Column(name = "`modify_time`",nullable = false)
    @NotNull
    @ApiModelProperty(value = "修改时间")
    private Timestamp modifyTime;

    @Column(name = "`is_del`")
    @NotBlank
    @ApiModelProperty(value = "是否删除")
    private String isDel;

    public void copy(TeAlgoImageLineage source){
        BeanUtil.copyProperties(source,this, CopyOptions.create().setIgnoreNullValue(true));
    }
}

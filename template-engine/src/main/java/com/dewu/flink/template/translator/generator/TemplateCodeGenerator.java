package com.dewu.flink.template.translator.generator;

import com.alibaba.fastjson.annotation.JSONField;
import com.dewu.flink.template.chaos.ChaosRuleInfo;
import com.dewu.flink.template.job.enums.DefaultMainParamType;
import com.dewu.flink.template.job.handler.codegenerator.impl.STDynamicRuleTemplateParam;
import com.dewu.flink.template.job.handler.codegenerator.impl.SqlTemplateParam;
import com.dewu.flink.template.job.service.dto.TeJobDataDrillDto;
import com.dewu.flink.template.meta.GlobalMetaBaseDomain;
import com.dewu.flink.template.meta.GlobalMetaManager;
import com.dewu.flink.template.meta.tablefield.service.dto.ReDataSourceTableFieldDto;
import com.dewu.flink.template.template.*;
import com.dewu.flink.template.template.b2s.SimpleFlinkSqlTemplate;
import com.dewu.flink.template.translator.CodeResponse;
import com.dewu.flink.template.utils.*;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.core.type.TypeReference;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.flink.util.Preconditions;
import org.apache.flink.util.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Properties;

import static com.dewu.flink.template.translator.generator.TemplateOption.MODULE_NAME;

@Service
public class TemplateCodeGenerator extends AbstractCodeGenerator {
	@Autowired
	private GlobalMetaManager globalMetaManager;

	@Value("${spring.datasource.druid.url}")
	@JSONField(serialize = false)
	private String url;
	@Value("${spring.datasource.druid.username}")
	@JSONField(serialize = false)
	private String username;
	@Value("${spring.datasource.druid.password}")
	@JSONField(serialize = false)
	private String password;

	private TeJobDataDrillDto teJobDataDrillDto = TeJobDataDrillDto.EMPTY;


	@Override
	public void setDataDrill(TeJobDataDrillDto teJobDataDrillDto) {
		this.teJobDataDrillDto = teJobDataDrillDto;
	}

	private static final Logger logger = LoggerFactory.getLogger(TemplateCodeGenerator.class);

	public CodeResponse generate(String jobType, String json, String className, Properties jobConfig, String jobName) {
		return generate(jobType, json, className, jobConfig, jobName, null);
	}

	public CodeResponse generate(String jobType, String json, String className, Properties jobConfig, String jobName, ChaosRuleInfo chaosRules) {

		long startTime = System.currentTimeMillis();
		try {
			Preconditions.checkArgument(!StringUtils.isNullOrWhitespaceOnly(json), "template json params shall not be empty!");
			ObjectMapper mapper = new ObjectMapper();
			String moduleName = String.valueOf(mapper.readValue(json, new TypeReference<Map<String, Object>>() {
			}).get(MODULE_NAME.key()));
			Preconditions.checkArgument(!StringUtils.isNullOrWhitespaceOnly(moduleName), MODULE_NAME.key() + " shall not be empty!");

			AbstractTemplate template;
			Map<String, String> params;
			if (moduleName.equalsIgnoreCase("B2S_TEMPLATE") || moduleName.equalsIgnoreCase("DynamicRuleTemplate") || moduleName.equalsIgnoreCase("CrowdLabelTemplate")
				|| moduleName.equalsIgnoreCase("SYNC_ODPS_TEMPLATE") || moduleName.equalsIgnoreCase("SYNC_KAFKA_TEMPLATE")) {
				params = JsonUtil.parseObject(json, SqlTemplateParam.class).translateToTemplateConfigs();
			} else if (moduleName.endsWith("DynamicRuleTemplate")) {
				params = JsonUtil.parseObject(json, STDynamicRuleTemplateParam.class).translateToTemplateConfigs();
			} else {
				params = mapper.readValue(json, new TypeReference<Map<String, String>>() {
				});
			}
			Map<String, String> optParams = TemplateSqlUtil.buildPreJobConfigs(params, chaosRules);
			params.putAll(optParams);
			Properties newJobConfigs = new Properties();
			newJobConfigs.putAll(optParams);
			if (null != jobConfig && jobConfig.size() > 0) {
				newJobConfigs.putAll(jobConfig);
			}
			newJobConfigs.put(DefaultMainParamType.EXECUTE_JOB_JDBC_URL.getName(), org.apache.commons.lang3.StringUtils.replace(url, ":log4jdbc", ""));
			newJobConfigs.put(DefaultMainParamType.EXECUTE_JOB_PASSWORD.getName(), password);
			newJobConfigs.put(DefaultMainParamType.EXECUTE_JOB_USER_NAME.getName(), username);
			logger.info("meet template : " + params);
			if (moduleName.equals("JoinTemplate")) {
				template = JoinTemplate.apply(params, newJobConfigs, jobName);
			} else if (moduleName.equals("GroupByTemplate")) {
				template = GroupByTemplate.apply(params, newJobConfigs, jobName);
			} else if (moduleName.equals("TopnTemplate")) {
				template = TopnTemplate.apply(params, newJobConfigs, jobName);
			} else if (moduleName.equals("RealtimeIndexModule")) {
				template = RealtimeIndexTemplateWithAgg.apply(params, newJobConfigs, jobName);
			} else if (moduleName.equalsIgnoreCase("B2S_TEMPLATE") || moduleName.endsWith("DynamicRuleTemplate") || moduleName.equalsIgnoreCase("LibraSqlTemplate")) {
				template = SimpleFlinkSqlTemplate.apply(params, newJobConfigs, jobName, jobType);
			} else if (moduleName.equalsIgnoreCase("SYNC_ODPS_TEMPLATE") || moduleName.equalsIgnoreCase("SYNC_KAFKA_TEMPLATE")) {
				String type = params.get("type");
				String[] sinkTableIds = params.get("sinkTableIds").split(",");
				String[] sourceTableIds = params.get("sourceTableIds").split(",");
				Preconditions.checkArgument(sinkTableIds.length == 1, "同步模板的输出表只能为1个");
				Preconditions.checkArgument(sourceTableIds.length == 1, "同步模板的输入表只能为1个");
				long sourceTableId = Long.parseLong(sourceTableIds[0]);
				long sinkTableId = Long.parseLong(sinkTableIds[0]);
				GlobalMetaBaseDomain sourceTable = globalMetaManager.getTableMetaByTableId(sourceTableId);
				GlobalMetaBaseDomain sinkTable = globalMetaManager.getTableMetaByTableId(sinkTableId);
				if (moduleName.equalsIgnoreCase("SYNC_ODPS_TEMPLATE")) {
					genSync2ODPSTemplateSql(DataSourceType.numOf(sourceTable.getReDataSourceDto().getDataSourceType()).getName(), sinkTable, sourceTable, params);
					newJobConfigs.putAll(TemplateSqlUtil.buildPreJobConfigs(params, chaosRules));
				} else {
					genSync2KafkaTemplateSql(DataSourceType.numOf(sourceTable.getReDataSourceDto().getDataSourceType()).getName(), sinkTable, sourceTable, params);
					newJobConfigs.putAll(TemplateSqlUtil.buildPreJobConfigs(params, chaosRules));
				}
				template = SimpleFlinkSqlTemplate.apply(params, newJobConfigs, jobName, jobType);
			} else {
				throw new IllegalArgumentException("Not Support Template:" + moduleName);
			}
			template.setGlobalMetaManager(globalMetaManager);
			template.setClassName(className);
			template.setTeJobDataDrillDto(teJobDataDrillDto);
			template.setChaosRules(chaosRules);

			CodeResponse response = new CodeResponse(template.generateTransformationStr());
			long endTime = System.currentTimeMillis();
			logger.info("codegen takes about " + (endTime - startTime) + " ms.");
			return response;
		} catch (
			Exception e) {
			throw new CodeGenerateException("fail to generate code for template: " + json, e);
		}
	}

	private void genSync2ODPSTemplateSql(String sourceType, GlobalMetaBaseDomain sinkTable, GlobalMetaBaseDomain sourceTable, Map<String, String> params) {
		String sql = "";
		switch (sourceType) {
			case "kafka":
				sql = "insert into `#sinkTable`\n" +
					"select    \n" +
					"    cast ( `messageKey`  as  VARCHAR) as `messagekey`                           ,\n" +
					"    cast ( `message`  as  VARCHAR) as `message`                           ,\n" +
					"    cast ( '" + sourceTable.getTableName() + "' as  VARCHAR) as `topic`,\n" +
					"    cast (`partition` as  BIGINT) as `partition`,\n" +
					"    cast (`offset` as  BIGINT) as `offset`                      ,\n" +
					"    unix_timestamp(cast(`ts`as varchar)) as `ts`,\n" +
					"    replace(substring(cast(`ts`as varchar),1,10),'-','') as pt,\n" +
					"    substring(cast(`ts`as varchar),12,2) as hr  \n" +
					"from `#sourceTable` where `message` is not null\n";
				sql = sql.replace("#sinkTable", sinkTable.getTableName()).replace("#sourceTable", sourceTable.getTableName());
				params.put("sql", sql);
				params.put("optionConfigs", params.get("optionConfigs") + ",binary.tables:" + sourceTable.getTableName());
				break;
			case "dmq":
				sql = "insert into `#sinkTable`\n" +
					"select    \n" +
					"    cast ( `message`  as  VARCHAR) as `message`                           ,\n" +
					"    cast ( '" + sourceTable.getTableName() + "' as  VARCHAR) as `topic`,\n" +
					"    cast (0 as  BIGINT) as `offset`                      ,\n" +
					"    cast (0 as  BIGINT) as `partition`,\n" +
					"    unix_timestamp(cast(proctime() as varchar)) as `ts`,\n" +
					"    replace(substring(cast(proctime() as varchar),1,10),'-','') as pt,  \n" +
					"    substring(cast(proctime() as varchar),12,2) as hr  \n" +
					"from `#sourceTable` where `message` is not null\n";
				sql = sql.replace("#sinkTable", sinkTable.getTableName()).replace("#sourceTable", sourceTable.getTableName());
				params.put("sql", sql);
				params.put("optionConfigs", params.get("optionConfigs") + ",binary.tables:" + sourceTable.getTableName());
				break;
			default:
				throw new RuntimeException("不支持的数据源类型，目前只支持kafka,dmq，但是选择了" + sourceType);
		}
	}

	public static String buildJsonMsgString(GlobalMetaBaseDomain sourceTable) {
		List<ReDataSourceTableFieldDto> reDataSourceTableFieldDtos = sourceTable.getReDataSourceTableFieldDtos();
		StringBuilder stringBuilder = new StringBuilder("cast ( jsonStringUdf (");
		reDataSourceTableFieldDtos.forEach(field -> stringBuilder.append("'").append(field.getFieldName()).append("',cast(`").append(field.getFieldName()).append("` as varchar),"));
		stringBuilder.deleteCharAt(stringBuilder.length() - 1);
		stringBuilder.append(") as VARBINARY)");
		return stringBuilder.toString();
	}

	private void genSync2KafkaTemplateSql(String sourceType, GlobalMetaBaseDomain sinkTable, GlobalMetaBaseDomain sourceTable, Map<String, String> params) {
		String sql = "";
		switch (sourceType) {
			case "odps":
				sql = "CREATE FUNCTION if not exists jsonStringUdf AS 'com.alibaba.blink.udx.JsonStringUdf';\n" +
					"insert into `#sinkTable`\n" +
					"select    cast(UUID() as VARBINARY) AS messageKey,\n" +
					buildJsonMsgString(sourceTable) + " AS message\n" +
					"from `#sourceTable`\n";
				sql = sql.replace("#sinkTable", sinkTable.getTableName()).replace("#sourceTable", sourceTable.getTableName());
				params.put("sql", sql);
				params.put("optionConfigs", params.get("optionConfigs") + ",binary.tables:" + sinkTable.getTableName());
				break;
			case "dmq":
				sql = "insert into `#sinkTable`\n" +
					"select    \n" +
					"   cast(UUID() as VARBINARY) AS messageKey, " +
					"    cast ( `message`  as  VARBINARY) as `message` " +
					"from `#sourceTable` where `message` is not null\n";
				sql = sql.replace("#sinkTable", sinkTable.getTableName()).replace("#sourceTable", sourceTable.getTableName());
				params.put("sql", sql);
				params.put("optionConfigs", params.get("optionConfigs") + ",binary.tables:" + sinkTable.getTableName() + ";" + sourceTable.getTableName());
				break;
			case "kafka":
				sql = "insert into `#sinkTable`\n" +
					"select    \n" +
					"   messageKey as `messageKey`, " +
					"    message as `message` " +
					"from `#sourceTable` ";
				sql = sql.replace("#sinkTable", sinkTable.getTableName()).replace("#sourceTable", sourceTable.getTableName());
				params.put("sql", sql);
				params.put("optionConfigs", params.get("optionConfigs") + ",binary.tables:" + sinkTable.getTableName() + ";" + sourceTable.getTableName());
				break;
			default:
				throw new RuntimeException("不支持的数据源类型，目前只支持kafka,dmq,odps, 但是选择了" + sourceType);
		}
	}
}

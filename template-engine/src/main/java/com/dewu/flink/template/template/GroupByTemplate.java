package com.dewu.flink.template.template;

import com.dewu.flink.template.transform.*;
import com.dewu.flink.template.translator.generator.ParamTool;
import com.google.common.collect.Sets;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.flink.configuration.ConfigOption;

import java.io.IOException;
import java.util.Map;
import java.util.Properties;
import java.util.Set;

import static com.dewu.flink.template.translator.generator.TemplateOption.*;

@EqualsAndHashCode(callSuper = true)
@Data
public class GroupByTemplate extends OneInputTemplate {
	private final static Set<ConfigOption<?>> mustOpts = Sets.newHashSet(
		SOURCE_TABLE_ID,
		GROUP_BY_EXPR,
		SINK_TABLE_ID,
		SINK_SCHEMA_PRIMARY_KEY,
		SELECT_EXPR
	);

	private String havingExpression;
	private String groupByExpression;

	public static GroupByTemplate apply(Map<String, String> params, Properties jobConfigs, String jobName) {
		ParamTool tool = ParamTool.apply(params);
		tool.checkNonNullParam(mustOpts);

		GroupByTemplate groupByTemplate = new GroupByTemplate();
		groupByTemplate.setJobName(jobName);
		groupByTemplate.setSourceTableId(tool.getString(SOURCE_TABLE_ID));
		groupByTemplate.setGroupByExpression(tool.getString(GROUP_BY_EXPR));
		groupByTemplate.setSinkTableId(tool.getString(SINK_TABLE_ID));
		groupByTemplate.setSinkPrimaryKeys(tool.getString(SINK_SCHEMA_PRIMARY_KEY).replaceAll("\\s+", ""));
		groupByTemplate.setSelectExpression(tool.getString(SELECT_EXPR));
		groupByTemplate.setFilterExpression(tool.getString(AGG_FILTER));
		groupByTemplate.setSourceCombinedExpression(tool.getString(SOURCE_COMBINED_TRANSFORM_EXPR));
		groupByTemplate.setHavingExpression(tool.getString(HAVING_EXPR));
		groupByTemplate.setJobConfigurations(jobConfigs);

		return groupByTemplate;
	}

	@Override
	public String generateTransformationStr() throws IOException {
		SourceTransformation sourceTransformation = generateSourceTransformation();
		if (!sourceTransformation.validate()) {
			return "SourceTransformation 验证失败";
		}
		GroupByTransformation groupByTransformation = new GroupByTransformation();
		groupByTransformation.setInput(addCalcExpression(null, getFilterExpression(), sourceTransformation));
		groupByTransformation.setGroupByExpression(groupByExpression);
		groupByTransformation.setSelectExpression(getSelectExpression());
		groupByTransformation.setFilterExpression(getFilterExpression());
		groupByTransformation.setHavingExpression(havingExpression);
		if (!groupByTransformation.validate()) {
			return "GroupByTransformation 验证失败";
		}

		Transformation sinkTransformation = generateSinkTransformation(groupByTransformation);
		if (!sinkTransformation.validate()) {
			return "SinkTransformation 验证失败";
		}
		return buildStrWithSink(sinkTransformation);
	}
}

package com.dewu.flink.template.transform;

import com.dewu.flink.template.utils.TemplateSqlUtil;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import org.apache.flink.util.StringUtils;

import java.io.IOException;
import java.io.StringWriter;
import java.util.UUID;

@EqualsAndHashCode(callSuper = true)
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class UnionTransformation extends TwoInputTransformation{
    private String leftSelectExpression = "*";
    private String rightSelectExpression = "*";
    private String leftFilterExpression = "1=1";
    private String rightFilterExpression = "1=1";
    private String leftView = "left_view_"+UUID.randomUUID().toString().replace("-","");
    private String rightView = "right_view_"+UUID.randomUUID().toString().replace("-","");

    public UnionTransformation() {
    }

    @Override
    public Boolean isFieldNeed(String fieldName) {
        return null;
    }

    @Override
    public String transformStr(StringWriter writer) throws IOException {
        if(getResultFunction() != null){
            return getResultFunction();
        }
        String leftFunction = this.getLeft().transformStr(writer);
        String rightFunction = this.getRight().transformStr(writer);
        setResultFunction("union_"+ UUID.randomUUID().toString().replace("-",""));

        writer.write(TemplateSqlUtil.readTemplate("Union.template")
                .replace("#transformFunction",getResultFunction())
                .replace("#leftFunction",leftFunction)
                .replace("#rightFunction",rightFunction)
                .replace("#leftView",leftView)
                .replace("#rightView",rightView)
                .replace("#leftSelectExpression",leftSelectExpression)
                .replace("#leftFilterExpression", StringUtils.isNullOrWhitespaceOnly(leftFilterExpression) ? "1=1":leftFilterExpression)
                .replace("#rightSelectExpression",rightSelectExpression)
                .replace("#rightFilterExpression",StringUtils.isNullOrWhitespaceOnly(rightFilterExpression) ? "1=1":rightFilterExpression)
        );

        return getResultFunction();
    }

    @Override
    public Boolean validate() {
        return true;
    }
}

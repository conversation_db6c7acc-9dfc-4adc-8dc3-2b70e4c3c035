package com.dewu.flink.template.deploy;

import com.dewu.flink.template.common.Response;
import com.dewu.flink.template.job.domain.TaskStatus;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * Author: WangLin
 * Date: 2022/9/15 上午10:55
 * Description: 批量查询作业状态返回结果
 */
@Data
@NoArgsConstructor
public class QueryTaskResponse implements Response {
	/**
	 * 是否查询成功
	 */
	private boolean flag;
	/**
	 * 查询提示信息【成功："OK" 失败："具体失败原因"】
	 */
	private String msg;
	/**
	 * 查询的结果
	 */
	private List<TaskStatus> taskStatusList;
}

package com.dewu.flink.template.template.b2s.converbean;

import com.dewu.flink.template.utils.JsonUtil;
import lombok.EqualsAndHashCode;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.core.JsonProcessingException;

import java.util.HashMap;
import java.util.Map;

/**
 * @Description: XXX
 * @author: zhiping.lin
 * @date: 2022/8/24
 */
@EqualsAndHashCode(callSuper = true)
public class CalcBean extends AbstractTransformBean {
	String filterExpression;

	public CalcBean(String filterExpression) {
		this.setId(id_id.getAndIncrement());
		this.name = "CalcTransformation";
		this.filterExpression = filterExpression;
	}

	public String getFilterExpression() {
		return filterExpression;
	}

	public void setFilterExpression(String filterExpression) {
		this.filterExpression = filterExpression;
	}

	@Override
	public String wrapTransformation() throws JsonProcessingException {
		Map<String, Object> transform = new HashMap<>();
		transform.put("selectExpression", selectExpression);
		transform.put("filterExpression", filterExpression);
		return JsonUtil.writeString(transform);
	}

	@Override
	public String toString() {
		try {
			return getString();
		} catch (JsonProcessingException e) {
			e.printStackTrace();
		}
		return null;
	}
}

package com.dewu.flink.template.exceptions;

/**
 * Author: <PERSON><PERSON>in
 * Date: 2024/6/6 13:53
 * Description: 无权限异常
 */
public class PermissionDeniedException extends RuntimeException {

    /**
     * Constructs a new runtime exception with the specified detail message.
     * The cause is not initialized, and may subsequently be initialized by a
     * call to {@link #initCause}.
     *
     * @param message the detail message. The detail message is saved for
     *                later retrieval by the {@link #getMessage()} method.
     */
    public PermissionDeniedException(String message) {
        super(message);
    }
}

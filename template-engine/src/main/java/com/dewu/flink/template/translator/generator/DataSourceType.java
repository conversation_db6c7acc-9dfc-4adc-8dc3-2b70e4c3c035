package com.dewu.flink.template.translator.generator;

/**
 * @Description: XXX
 * @author: zhiping.lin
 * @date: 2022/8/16
 */
public enum DataSourceType {
	DMQ(11, "dmq"),
	DATAGEN(10, "datagen"),
	PRINT(9, "print"),
	STARROCKS(8, "starrocks"),
	ADB(7, "adb"),
	ES(6, "du-elasticsearch-6"),
	CLICKHOUSE(5, "clickhouse"),
	ODPS(4, "odps"),
	HBASE(3, "hbase"),
	KAFKA(2, "kafka"),
	RDS(1, "rds");

	int value;
	String name;

	DataSourceType(int value, String name) {
		this.value = value;
		this.name = name;
	}

	public int getValue() {
		return value;
	}

	public String getName() {
		return name;
	}

	public static DataSourceType numOf(int i) {
		switch (i) {
			case 1:
				return RDS;
			case 2:
				return KAFKA;
			case 3:
				return HBASE;
			case 4:
				return ODPS;
			case 5:
				return CLICKHOUSE;
			case 6:
				return ES;
			case 7:
				return ADB;
			case 8:
				return STARROCKS;
			case 9:
				return PRINT;
			case 10:
				return DATAGEN;
			case 11:
				return DMQ;
			default:
				throw new RuntimeException("Not a valid num for DataSourceType : " + i);
		}
	}
}

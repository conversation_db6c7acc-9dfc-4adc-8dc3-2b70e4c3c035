/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.dewu.flink.template.example;
import com.dewu.flink.template.config.SchemaConfig;
import com.dewu.flink.template.transform.*;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.ObjectMapper;
import java.util.ArrayList;
import java.util.List;
import java.util.Properties;

public class StreamTableLateralJoinExample {

    public static void main(String[] args) throws Exception {
        int cnt = 0;
        ObjectMapper mapper = new ObjectMapper();
        List<TransformationWrapper> transformations= new ArrayList();
        Properties orderSourceKafkaConnectConfig = new Properties();
        orderSourceKafkaConnectConfig.put("connector","kafka");
        orderSourceKafkaConnectConfig.put("tableId","340");
        SourceTransformation orderKafkaSourceTransformation = new SourceTransformation();
        orderKafkaSourceTransformation.setConnectConfig(orderSourceKafkaConnectConfig);
        transformations.add(new TransformationWrapper(String.valueOf(cnt++),"SourceTransformation",new String[]{},mapper.writeValueAsString(orderKafkaSourceTransformation)));

        Properties productSourceKafkaConnectConfig = new Properties();
        productSourceKafkaConnectConfig.put("connector","kafka");
        productSourceKafkaConnectConfig.put("tableId","890");
        SourceTransformation productKafkaSourceTransformation = new SourceTransformation();
        productKafkaSourceTransformation.setConnectConfig(productSourceKafkaConnectConfig);
        transformations.add(new TransformationWrapper(String.valueOf(cnt++),"SourceTransformation",new String[]{},mapper.writeValueAsString(productKafkaSourceTransformation)));

        Properties mysqlConnectConfig = new Properties();
        mysqlConnectConfig.put("tableId","1134");
        mysqlConnectConfig.put("connector","jdbc");
        LookupJoinTransformation mysqlLookupJoinTransformation = new LookupJoinTransformation();
        mysqlLookupJoinTransformation.setJoinConditionExpression("l.userid = r.id");
        mysqlLookupJoinTransformation.setSelectExpression("l.product as product,r.username as username,r.age as age,l.amount as amount");
        mysqlLookupJoinTransformation.setConnectConfig(mysqlConnectConfig);
        mysqlLookupJoinTransformation.setJoinType("inner");
        transformations.add(new TransformationWrapper(String.valueOf(cnt++),"LookupJoinTransformation",new String[]{"0"},mapper.writeValueAsString(mysqlLookupJoinTransformation)));

        GroupByTransformation groupByTransformation = new GroupByTransformation();
        groupByTransformation.setGroupByExpression("username,age");
        groupByTransformation.setSelectExpression("username,age,sum(amount) as amount,max(product) as product");
        transformations.add(new TransformationWrapper(String.valueOf(cnt++),"GroupByTransformation",new String[]{"2"},mapper.writeValueAsString(groupByTransformation)));
        JoinTransformation joinTransformation = new JoinTransformation();
        joinTransformation.setSelectExpression("l.username as username,l.age as age,l.amount as amount,r.product as product,r.color as color");
        joinTransformation.setJoinConditionExpression("l.product = r.product");
        joinTransformation.setJoinType("inner");
        transformations.add(new TransformationWrapper(String.valueOf(cnt++),"JoinTransformation",new String[]{"3","1"},mapper.writeValueAsString(joinTransformation)));

        TopnTransformation topnTransformation = new TopnTransformation();
        topnTransformation.setSelectExpression("username,age,amount,product,color");
        topnTransformation.setOrderByExpression("color ASC");
        topnTransformation.setPartitionExpression("age");
        topnTransformation.setN(1);
        transformations.add(new TransformationWrapper(String.valueOf(cnt++),"TopnTransformation",new String[]{"4"},mapper.writeValueAsString(topnTransformation)));

        CalcTransformation calcTransformation = new CalcTransformation();
        calcTransformation.setSelectExpression("username,age,amount,product,color");
        calcTransformation.setFilterExpression("username='wlj'");
        transformations.add(new TransformationWrapper(String.valueOf(cnt++),"CalcTransformation",new String[]{"5"},mapper.writeValueAsString(calcTransformation)));

        String kafkaSinkPrimaryKey = "username,age";
        SchemaConfig sinkSchemaConfig = new SchemaConfig();
        sinkSchemaConfig.setPrimaryKey(kafkaSinkPrimaryKey);
        Properties sinkKafkaConnectConfig = new Properties();
        sinkKafkaConnectConfig.put("tableId","872");
        sinkKafkaConnectConfig.put("connector","upsert-kafka");
        SinkTransformation kafkaSinkTransformation = new SinkTransformation();
        kafkaSinkTransformation.setConnectConfig(sinkKafkaConnectConfig);
        kafkaSinkTransformation.setSchemaConfig(sinkSchemaConfig);
        transformations.add(new TransformationWrapper(String.valueOf(cnt++),"SinkTransformation",new String[]{"6"},mapper.writeValueAsString(kafkaSinkTransformation)));

        Properties sinkMysqlConnectConfig = new Properties();
        sinkMysqlConnectConfig.put("tableId","1137");
        sinkMysqlConnectConfig.put("connector","jdbc");
        SinkTransformation mysqlSinkTransformation = new SinkTransformation();
        mysqlSinkTransformation.setConnectConfig(sinkMysqlConnectConfig);
        mysqlSinkTransformation.setSchemaConfig(sinkSchemaConfig);
        transformations.add(new TransformationWrapper(String.valueOf(cnt++),"SinkTransformation",new String[]{"6"},mapper.writeValueAsString(mysqlSinkTransformation)));

        System.out.println(mapper.writeValueAsString(transformations));
    }
}

/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.dewu.flink.template.example;

import com.dewu.flink.template.config.SchemaConfig;
import com.dewu.flink.template.template.JoinTemplate;
import com.dewu.flink.template.template.TemplateWrapper;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.ObjectMapper;
import java.util.Properties;


public class StreamJoinTemplateExample {
    public static void main(String[] args) throws Exception {

        JoinTemplate joinTemplate = new JoinTemplate();

        joinTemplate.setLeftFilterExpression("userid=1");
        joinTemplate.setLeftTableId("340");

        joinTemplate.setRightFilterExpression("product='beer'");
        joinTemplate.setRightTableId("890");

//        Properties jdbcConnectConfig = new Properties();
//        jdbcConnectConfig.put("connector","jdbc");
//        jdbcConnectConfig.put("tableId","1134");
//        joinTemplate.setRightConnectConfig(jdbcConnectConfig);

        joinTemplate.setJoinConditionExpression("l.product = r.product");
        joinTemplate.setSelectExpression("l.userid as userid,l.amount as amount,r.product as product,r.color as color");
        joinTemplate.setJoinFilterExpression("color='red'");
        joinTemplate.setJoinType("inner");

//        joinTemplate.setJoinConditionExpression("l.userid = r.id");
//        joinTemplate.setSelectExpression("l.userid as userid,l.amount as amount,r.username as username,r.age as age");
//        joinTemplate.setJoinFilterExpression("username='wlj'");
//        joinTemplate.setLeftJoin(false);

        String kafkaSinkPrimaryKey = "userid";
        joinTemplate.setSinkPrimaryKeys(kafkaSinkPrimaryKey);
        joinTemplate.setSinkTableId("872");

        ObjectMapper mapper = new ObjectMapper();
        System.out.println(mapper.writeValueAsString(new TemplateWrapper("JoinTemplate",mapper.writeValueAsString(joinTemplate))));
    }
}

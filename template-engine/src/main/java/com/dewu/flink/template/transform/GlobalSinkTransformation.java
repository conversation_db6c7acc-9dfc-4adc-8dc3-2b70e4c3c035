package com.dewu.flink.template.transform;

import com.dewu.flink.template.utils.TemplateSqlUtil;
import lombok.Data;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import java.io.IOException;
import java.io.StringWriter;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

/**
 * used for representation of multi-sinks
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class GlobalSinkTransformation extends MultiInputTransformation {

	private boolean isDataDrill = false;

	@Override
	public Boolean isFieldNeed(String fieldName) {
		return true;
	}

	@Override
	public String transformStr(StringWriter writer) throws IOException {
		if (getResultFunction() != null) {
			return getResultFunction();
		}
		List<String> inputFunctions = new ArrayList<>();
		for (Transformation input : getInputs()) {
			String sinkName = input.transformStr(writer);
			String sinkFunction = sinkName + "(tEnv)";
			if (isDataDrill) {
				sinkFunction = "DataDrillSink " + sinkName + " = " + sinkFunction + ";\n" + "\t\tresult.add(" + sinkName + ")";
			}
			inputFunctions.add("\t\t" + sinkFunction);
		}
		String sinkFunctions = String.join(";\n", inputFunctions) + ";";

		if (isDataDrill) {
			String resultList = "List result = new ArrayList();\n";
			sinkFunctions = resultList + sinkFunctions;
		}
		setResultFunction("globalSink_" + UUID.randomUUID().toString().replace("-", ""));
		String replace = TemplateSqlUtil.readTemplate(isDataDrill ? "GlobalSinkDataDrill.template" : "GlobalSink.template")
				.replace("#transformFunction", getResultFunction())
				.replace("#sinkFunctions", sinkFunctions);
		writer.write(replace);
		return getResultFunction();
	}

	@Override
	public Boolean validate() {
		return true;
	}
}

package com.dewu.flink.template.template;

import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.annotation.JsonIgnoreProperties;

@JsonIgnoreProperties(ignoreUnknown = false)
public class TemplateWrapper {
    private String name;
    private String template;


    public TemplateWrapper(String name, String template) {
        this.name = name;
        this.template = template;
    }

    public String getTemplate() {
        return template;
    }

    public String getName() {
        return name;
    }

    public TemplateWrapper() {
    }
}

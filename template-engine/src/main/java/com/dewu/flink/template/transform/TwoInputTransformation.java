package com.dewu.flink.template.transform;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.annotation.JsonIgnore;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.annotation.JsonIgnoreProperties;

@EqualsAndHashCode(callSuper = true)
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public abstract class TwoInputTransformation extends AbstractTransformation {
    @JsonIgnore
    private Transformation left;
    @JsonIgnore
    private Transformation right;
    @JsonIgnore
    private String leftId;
    @JsonIgnore
    private String rightId;
}

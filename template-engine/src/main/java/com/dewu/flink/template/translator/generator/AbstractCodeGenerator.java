package com.dewu.flink.template.translator.generator;

import com.dewu.flink.template.job.service.dto.TeJobDataDrillDto;
import com.dewu.flink.template.translator.CodeResponse;

import java.util.Properties;

/**
 * @Description: XXX
 * @author: zhiping.lin
 * @date: 2022/8/18
 */
public abstract class AbstractCodeGenerator {
	public abstract CodeResponse generate (String jobType, String json, String className, Properties properties, String jobName);

	public abstract void setDataDrill(TeJobDataDrillDto teJobDataDrillDto);
}

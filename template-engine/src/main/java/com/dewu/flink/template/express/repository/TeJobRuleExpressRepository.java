/*
*  Copyright 2019-2020 <PERSON>
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.dewu.flink.template.express.repository;


import com.dewu.flink.template.express.domain.TeJobRuleExpress;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;

/**
* @website https://el-admin.vip
* <AUTHOR>
* @date 2023-02-08
**/
public interface TeJobRuleExpressRepository extends JpaRepository<TeJobRuleExpress, Long>, JpaSpecificationExecutor<TeJobRuleExpress> {


    @Query(value = "select count(1)  from te_job_rule_express where `re_data_source_metadata_id`=?1 and is_delete=0",nativeQuery = true)
    int countByMetadataId(long metadataId);

    @Query(value = "select count(1)  from te_job_rule_express where id<>?1 and `re_data_source_metadata_id`=?2 and is_delete=0",nativeQuery = true)
    int countByMetadataIdWithoutId(Long id, Long metadataId);


    @Query(value = "update te_job_rule_express set is_delete=1  where id=?1",nativeQuery = true)
    void updateStatus(Long id);

    void deleteByTeJobInfoId(String job_id);

}
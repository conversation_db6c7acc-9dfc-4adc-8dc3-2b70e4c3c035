package com.dewu.flink.template.transform;

import com.dewu.flink.template.utils.TemplateSqlUtil;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.annotation.JsonIgnore;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import org.apache.flink.shaded.netty4.io.netty.util.internal.StringUtil;

import java.io.IOException;
import java.io.StringWriter;
import java.util.UUID;

@JsonIgnoreProperties(ignoreUnknown = true)
public class DynamicRuleTransformation extends OneInputTransformation {

    @JsonIgnore
    private String viewName = "view_"+UUID.randomUUID().toString().replace("-","");
    private String dynamicExtSelect = "";

    @Override
    public Boolean isFieldNeed(String fieldName) {
        return null;
    }

    public String getDynamicExtSelect() {
        return dynamicExtSelect;
    }

    public void setDynamicExtSelect(String dynamicExtSelect) {
        this.dynamicExtSelect = dynamicExtSelect;
    }

    @Override
    public String transformStr(StringWriter writer) throws IOException {
        if(getResultFunction() != null){
            return getResultFunction();
        }
        String inputFunction = this.getInput().transformStr(writer);
        setResultFunction("drt_"+ UUID.randomUUID().toString().replace("-",""));

        writer.write(TemplateSqlUtil.readTemplate("DynamicRule.template")
            .replace("#transformFunction",getResultFunction())
            .replace("#dynamicExtSelect",dynamicExtSelect)
                .replace("#inputFunction",inputFunction)
                .replace("#viewName",viewName));
        return getResultFunction();
    }

    @Override
    public Boolean validate() {
        return true;
    }
}

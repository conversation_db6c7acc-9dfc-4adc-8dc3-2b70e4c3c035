package com.dewu.flink.template.translator.generator;

import com.dewu.flink.template.transform.TransformationWrapper;
import org.apache.flink.util.CollectionUtil;

import java.util.ArrayList;

/**
 * @Description: XXX
 * @author: zhiping.lin
 * @date: 2024/5/7
 */
public class AlgoUtil {
	public final static String ALGO_ODPS_PRE_CALC_TRANSFORMATION="";
	public final static String ALGO_ODPS_PRE_CALC_ID="10001";
	public final static String ALGO_ODPS_SINK_TRANSFORMATION="";
	public final static String ALGO_ODPS_SINK_ID="10002";
	public final static String ALGO_KAFKA_SINK_TRANSFORMATION="{\"connectConfig\":{\"connector\":\"KAFKA\",\"tableId\":\"#tableId\"},\"schemaConfig\":{\"primaryKey\":\"track_id\"}}";
	public final static String ALGO_KAFKA_SINK_ID="10003";
	public final static String ALGO_REBALANCE_ID="10004";

	/**
	 * {
	 *   "connectConfig": {
	 *     "connector": "odps",
	 *     "tableId": 1441
	 *   },
	 *   "schemaConfig": {
	 *     "primaryKey": "spu_category_name"
	 *   }
	 * }
	 */
	public static TransformationWrapper buildODPSLogSinkTW(String tableId, String[] inputs){

		return new TransformationWrapper(ALGO_ODPS_SINK_ID,"SinkTransformation",inputs,ALGO_ODPS_SINK_TRANSFORMATION.replace("#tableId", tableId));
	}
	public static TransformationWrapper buildKafkaMetricsSinkTW(String tableId, String[] inputs){
		return new TransformationWrapper(ALGO_KAFKA_SINK_ID,"SinkTransformation",inputs,ALGO_KAFKA_SINK_TRANSFORMATION.replace("#tableId", tableId));
	}
	public static TransformationWrapper buildOdpsPreCalcTW(String[] inputs){
		return new TransformationWrapper(ALGO_ODPS_PRE_CALC_ID,"CalcTransformation",inputs,ALGO_ODPS_PRE_CALC_TRANSFORMATION);
	}
}

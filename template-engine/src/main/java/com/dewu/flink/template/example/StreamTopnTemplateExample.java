/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.dewu.flink.template.example;

import com.dewu.flink.template.config.SchemaConfig;
import com.dewu.flink.template.template.TemplateWrapper;
import com.dewu.flink.template.template.TopnTemplate;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.ObjectMapper;

import java.util.Properties;


public class StreamTopnTemplateExample {
    public static void main(String[] args) throws Exception {

        TopnTemplate topnTemplate = new TopnTemplate();

        topnTemplate.setSourceTableId("340");
        topnTemplate.setFilterExpression("userid=1");
        topnTemplate.setSelectExpression("*");
        topnTemplate.setPartitionExpression("userid");
        topnTemplate.setOrderByExpression("product");

        String kafkaSinkPrimaryKey = "userid";

        topnTemplate.setSinkPrimaryKeys(kafkaSinkPrimaryKey);
        topnTemplate.setSinkPrimaryKeys(kafkaSinkPrimaryKey);

        ObjectMapper mapper = new ObjectMapper();
        System.out.println(mapper.writeValueAsString(new TemplateWrapper("TopnTemplate",mapper.writeValueAsString(topnTemplate))));
    }
}

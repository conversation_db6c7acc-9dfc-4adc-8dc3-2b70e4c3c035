package com.dewu.flink.template.deploy;

import com.dewu.flink.template.common.Request;
import com.dewu.flink.template.deploy.param.FlinkCustomParams;
import com.dewu.flink.template.deploy.param.FlinkParams;
import com.dewu.flink.template.deploy.param.MainParams;
import com.dewu.flink.template.deploy.param.UdfJarList;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * Author: WangLin
 * Date: 2022/7/24 下午2:25
 * Description: 更新作业请求参数
 */
@Data
@NoArgsConstructor
public class UpdateTaskRequest implements Request {

	/**
	 * 所有者
	 */
	private String owners;

	/**
	 * 操作者
	 */
	private String operator;

	/**
	 * 作业task，必填项，其他的填的都是更新项
	 */
	private String taskId;

	/**
	 * flink引擎名称，测试环境默认"flink-1.13"，生产环境默认"flink-du-1.13"
	 */
	private String engineName;

	/**
	 * 作业高级参数
	 */
	private FlinkCustomParams flinkCustomParams;

	/**
	 * 作业参数
	 */
	private FlinkParams flinkParams;

	/**
	 * 作业jar包id
	 */
	private Integer jarId;

	/**
	 * 作业优先级
	 */
	private Integer taskPriority;

	/**
	 * 入口类名
	 */
	private String mainClass;

	/**
	 * main函数参数
	 */
	private MainParams mainParams;

	/**
	 * 父文件夹id，默认0（根路径）
	 */
	private Integer parentFolderId;

	/**
	 * 项目名称，默认"tech-data-dw2"
	 */
	private String projectName;

	/**
	 * 资源名称，到web平台查看，测试环境默认"k8s-cluster"，生产环境默认"k8s-flink-bigdata"
	 */
	private String resourceName;

	/**
	 * 作业描述
	 */
	private String taskDesc;

	/**
	 * 作业名称，确保唯一
	 */
	private String taskName;

	/**
	 * 作业类型，1:jar 2:sql  默认1
	 */
	private Integer taskType;

	/**
	 * 用到的udf jar信息
	 */
	private List<UdfJarList> udfJarList;

}


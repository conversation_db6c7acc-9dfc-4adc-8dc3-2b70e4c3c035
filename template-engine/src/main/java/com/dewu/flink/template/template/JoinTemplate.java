package com.dewu.flink.template.template;

import com.dewu.flink.template.meta.GlobalMetaBaseDomain;
import com.dewu.flink.template.transform.JoinTransformation;
import com.dewu.flink.template.transform.LookupJoinTransformation;
import com.dewu.flink.template.transform.SourceTransformation;
import com.dewu.flink.template.transform.Transformation;
import com.dewu.flink.template.translator.generator.DataSourceType;
import com.dewu.flink.template.translator.generator.ParamTool;
import com.dewu.flink.template.utils.JsonUtil;
import com.google.common.collect.Sets;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.flink.configuration.ConfigOption;

import java.io.IOException;
import java.util.Map;
import java.util.Properties;
import java.util.Set;

import static com.dewu.flink.template.translator.generator.TemplateOption.*;

@EqualsAndHashCode(callSuper = true)
@Data
public class JoinTemplate extends TwoInputTemplate {

	private final static Set<ConfigOption<?>> mustOpts = Sets.newHashSet(
		LEFT_TABLE_ID,
		JOIN_TYPE,
		RIGHT_TABLE_ID,
		SINK_TABLE_ID,
		JOIN_CONDITION,
		SINK_SCHEMA_PRIMARY_KEY,
		SELECT_EXPR
	);

	private String joinType;
	private Boolean isDeduplicateByJoinKey = false;
	private String joinConditionExpression;
	private String joinFilterExpression;
	private String leftCombinedTransformExpression;
	private String rightCombinedTransformExpression;
	private Integer delayMinutes;

	public static JoinTemplate apply(Map<String, String> params, Properties jobConfigs, String jobName) {
		ParamTool tool = ParamTool.apply(params);
		System.out.println("jobConfigs:" + jobConfigs);
		tool.checkNonNullParam(mustOpts);

		JoinTemplate joinTemplate = new JoinTemplate();
		joinTemplate.setJobName(jobName);
		joinTemplate.setLeftTableId(tool.getString(LEFT_TABLE_ID));
		joinTemplate.setLeftCombinedTransformExpression(tool.getString(LEFT_COMBINED_TRANSFORM_EXPR));
		joinTemplate.setJoinType(tool.getString(JOIN_TYPE));
		joinTemplate.setRightTableId(tool.getString(RIGHT_TABLE_ID));
		joinTemplate.setRightCombinedTransformExpression(tool.getString(RIGHT_COMBINED_TRANSFORM_EXPR));
		joinTemplate.setSinkTableId(tool.getString(SINK_TABLE_ID));
		joinTemplate.setJoinConditionExpression(tool.getString(JOIN_CONDITION));
		joinTemplate.setSinkPrimaryKeys(tool.getString(SINK_SCHEMA_PRIMARY_KEY).replaceAll("\\s+", ""));
		joinTemplate.setSelectExpression(tool.getString(SELECT_EXPR));

		joinTemplate.setJoinFilterExpression(tool.getString(JOIN_FILTER));
		joinTemplate.setIsDeduplicateByJoinKey(tool.getBoolean(JOIN_KEY_DUPLICATE));
		joinTemplate.setLeftFilterExpression(tool.getString(LEFT_TABLE_FILTER));
		joinTemplate.setJoinFilterExpression(tool.getString(RIGHT_TABLE_FILTER));
		joinTemplate.setJobConfigurations(jobConfigs);
		joinTemplate.setDelayMinutes(tool.getInteger(DELAY_MINUTES));
		return joinTemplate;
	}

	public String getJoinType() {
		return joinType;
	}

	public Boolean getIsDeduplicateByJoinKey() {
		return isDeduplicateByJoinKey == null || isDeduplicateByJoinKey;
	}

	public String getJoinConditionExpression() {
		return joinConditionExpression;
	}


	public String getJoinFilterExpression() {
		return joinFilterExpression;
	}

	public Integer getDelayMinutes() {
		return delayMinutes;
	}

	public void setJoinType(String joinType) {
		this.joinType = joinType;
	}

	public void setIsDeduplicateByJoinKey(Boolean isDeduplicateByJoinKey) {
		this.isDeduplicateByJoinKey = (isDeduplicateByJoinKey != null && isDeduplicateByJoinKey);
	}

	public void setJoinConditionExpression(String joinConditionExpression) {
		this.joinConditionExpression = joinConditionExpression;
	}

	public void setJoinFilterExpression(String joinFilterExpression) {
		this.joinFilterExpression = joinFilterExpression;
	}

	public void setDelayMinutes(Integer delayMinutes) {
		this.delayMinutes = delayMinutes;
	}

	@Override
	public String generateTransformationStr() throws IOException {
		SourceTransformation leftSourceTransformation = new SourceTransformation();
		leftSourceTransformation.setConnectConfig(addStartTsConfig(wrapSimpleKeyPairConfig(TABLE_ID.key(), getLeftTableId())));
		leftSourceTransformation.setCombinedTransformExpression(leftCombinedTransformExpression);
		leftSourceTransformation.setGlobalMetaManager(getGlobalMetaManager());
		GlobalMetaBaseDomain rightSourceDomain = getGlobalMetaManager().getTableMetaByTableId(Long.valueOf(getRightTableId()));
		Transformation leftTransformation = addCalcExpression(null, getLeftFilterExpression(), leftSourceTransformation);
		if (!leftTransformation.validate()) {
			return "Left SourceTransformation 验证失败";
		}
		Map<String, String> globalConfig = JsonUtil.parseObject(getGlobalMetaManager().getDeployInfo().getGlobalConfig(), Map.class);
		if (delayMinutes > 0) {
			leftTransformation = addDelayEmit(leftTransformation, delayMinutes, globalConfig.get("delay_join_table_id"), getGlobalMetaManager());
		}
		Transformation joinTransformation;
		if (DataSourceType.KAFKA.getValue() == rightSourceDomain.getReDataSourceDto().getDataSourceType() || DataSourceType.DMQ.getValue() == rightSourceDomain.getReDataSourceDto().getDataSourceType()) {
			SourceTransformation rightSourceTransformation = new SourceTransformation();
			rightSourceTransformation.setConnectConfig(addStartTsConfig(wrapSimpleKeyPairConfig(TABLE_ID.key(), getRightTableId())));
			rightSourceTransformation.setCombinedTransformExpression(rightCombinedTransformExpression);
			rightSourceTransformation.setGlobalMetaManager(getGlobalMetaManager());
			Transformation rightTransformation = addCalcExpression(null, getRightFilterExpression(), rightSourceTransformation);
			if (!rightTransformation.validate()) {
				return "Right SourceTransformation 验证失败";
			}
			joinTransformation = new JoinTransformation();
			((JoinTransformation) joinTransformation).setJoinType(joinType);
			((JoinTransformation) joinTransformation).setIsDeduplicateByJoinKey(isDeduplicateByJoinKey);
			((JoinTransformation) joinTransformation).setJoinConditionExpression(joinConditionExpression);
			((JoinTransformation) joinTransformation).setSelectExpression(getSelectExpression());
			((JoinTransformation) joinTransformation).setLeft(leftTransformation);
			((JoinTransformation) joinTransformation).setRight(rightTransformation);
			if (!joinTransformation.validate()) {
				return "JoinTransformation 验证失败";
			}
		} else if (DataSourceType.RDS.getValue() == rightSourceDomain.getReDataSourceDto().getDataSourceType()) {
			joinTransformation = new LookupJoinTransformation();
			((LookupJoinTransformation) joinTransformation).setJoinType(joinType);
			((LookupJoinTransformation) joinTransformation).setGlobalMetaManager(getGlobalMetaManager());
			((LookupJoinTransformation) joinTransformation).setJoinConditionExpression(joinConditionExpression);
			Properties properties = new Properties();
			((LookupJoinTransformation) joinTransformation).setConnectConfig(properties);
			((LookupJoinTransformation) joinTransformation).setSelectExpression(getSelectExpression());
			((LookupJoinTransformation) joinTransformation).setInput(leftTransformation);
			if (!joinTransformation.validate()) {
				return "LookupJoinTransformation 验证失败";
			}
		} else {
			throw new IllegalArgumentException("Unsupport connector:" + DataSourceType.numOf(rightSourceDomain.getReDataSourceDto().getDataSourceType()).getName());
		}

		Transformation sinkTransformation = generateSinkTransformation(addCalcExpression(null, joinFilterExpression, joinTransformation));
		if (!sinkTransformation.validate()) {
			return "SinkTransformation 验证失败";
		}

		return buildStrWithSink(sinkTransformation);
	}
}

package com.dewu.flink.template.translator.generator;

import com.dewu.flink.template.transform.TransformationWrapper;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @Description: XXX
 * @author: zhiping.lin
 * @date: 2022/9/7
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class TableCodeParam {
	List<TransformationWrapper> transformationWrappers;
	String optionConfigs;
}

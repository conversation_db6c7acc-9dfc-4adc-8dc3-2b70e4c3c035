package com.dewu.flink.template.deploy;

import com.dewu.flink.template.common.Response;
import com.dewu.flink.template.job.domain.LibraTaskInfo;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import java.util.List;

/**
 * @description: /
 * @Author: lmh
 * @date: 2023/4/3 17:48
 */
@Data
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class BatchQueryTaskResponse implements Response {
    private Long code;
    private String msg;
    private InfoData data;

    @Data
    @NoArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class InfoData {
        private Long resultCode;
        private Long pageIndex;
        private Long pageSize;
        private Long count;
        private Long pageCount;
        private List<LibraTaskInfo> taskList;
    }
}

package com.dewu.flink.template.validator;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONValidator;
import com.dewu.flink.template.job.domain.TeJobDeployDefaultConfig;
import com.dewu.flink.template.job.domain.TeJobDeployInfo;
import com.dewu.flink.template.job.domain.TeJobInfo;
import com.dewu.flink.template.job.enums.BaseType;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.Objects;

/**
 * @Description: validator for Job.
 * @author: zhiping.lin
 * @date: 2022/7/15
 */
public class JobValidator implements Validator<TeJobInfo>{
	@Override
	public boolean validate(TeJobInfo vo) {
		return false;
	}

	public static boolean validateDagJson(String str){
		//空校验
		return StringUtils.isNotEmpty(str) && JSON.isValidObject(str);
	}

	/** 用于Integer类型状态比对 */
	public static boolean equalStatus(Integer status1, BaseType baseEnums2){
		return Objects.equals(status1, baseEnums2.getCode());
	}

	/** 用于Integer类型状态批量比对 */
	public static boolean inStatus(Integer status1, BaseType... baseEnums){

		if (baseEnums == null){
			return false;
		}

		for (BaseType baseEnums2 : baseEnums) {
			if (equalStatus(status1, baseEnums2)){
				return true;
			}
		}
		return false;
	}

	/** JsonString 校验 */
	public static boolean isJsonOrEmpty(String jsonStr){
		return StringUtils.isEmpty(jsonStr) || JSON.isValidObject(jsonStr)|| "[]".equals(jsonStr) ;
	}

	public static boolean isAllJsonOrEmpty(String... jsons){
		if (jsons == null) {
			return false;
		} else {
			for (String json : jsons) {
				if (!isJsonOrEmpty(json)) {
					return false;
				}
			}
			return true;
		}
	}

	/** defaultConfig合理性校验*/
	public static boolean validateDefaultConfig(TeJobDeployDefaultConfig
														defaultConfig){

		if (defaultConfig==null){
			return false;
		}

				/* String非空校验 */
		boolean emptyRule = StringUtils.isNoneEmpty(
				//flink引擎名称
				defaultConfig.getEngineName(),
				//入口类
				defaultConfig.getMainClass(),
				//项目名称
				defaultConfig.getProjectName(),
				//资源名称
				defaultConfig.getResourceName()
		);

				/* 空 或则 不为空时JSON校验*/
		boolean jsonOrEmptyRule = isAllJsonOrEmpty(
				//作业高级参数
				defaultConfig.getFlinkCustomParams(),
				//作业参数
				defaultConfig.getFlinkParams(),
				//main函数参数
				defaultConfig.getMainParams(),
				//用到的udf jar信息
				defaultConfig.getUdfJarList()
		);

		/* 非null校验 */
		boolean nullRule = ObjectUtils.allNotNull(
				//jar id
				defaultConfig.getJarId(),
				//父文件夹id，默认0（根路径 ）
				defaultConfig.getParentFolderId(),
				//作业类型
				defaultConfig.getTaskType()
		);

		return emptyRule && jsonOrEmptyRule && nullRule;
	}

	/** defaultConfig合理性校验*/
	public static boolean validateConfig(TeJobDeployInfo defaultConfig){

		if (defaultConfig==null){
			return true;
		}
		/* 校验 */
		return isAllJsonOrEmpty(
						//作业高级参数
						defaultConfig.getFlinkCustomParams(),
						//作业参数
						defaultConfig.getFlinkParams(),
						//main函数参数
						defaultConfig.getMainParams(),
						//用到的udf jar信息
						defaultConfig.getUdfJarList()
				);
	}

	public static void main(String[] args) {
		String  str="[]";
		System.out.println(JSONValidator.from("[]").validate());
	}

}

/*
*  Copyright 2019-2020 <PERSON>
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.dewu.flink.template.lineage.service.impl;

import com.dewu.flink.template.lineage.domain.TeAlgoImageLineage;
import com.dewu.utils.ValidationUtil;
import com.dewu.utils.FileUtil;
import lombok.RequiredArgsConstructor;
import com.dewu.flink.template.lineage.repository.TeAlgoImageLineageRepository;
import com.dewu.flink.template.lineage.service.TeAlgoImageLineageService;
import com.dewu.flink.template.lineage.service.dto.TeAlgoImageLineageDto;
import com.dewu.flink.template.lineage.service.dto.TeAlgoImageLineageQueryCriteria;
import com.dewu.flink.template.lineage.service.mapstruct.TeAlgoImageLineageMapper;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import com.dewu.utils.PageUtil;
import com.dewu.utils.QueryHelp;
import java.util.List;
import java.util.Map;
import java.io.IOException;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.LinkedHashMap;

/**
* @website https://el-admin.vip
* @description 服务实现
* <AUTHOR>
* @date 2024-05-22
**/
@Service
@RequiredArgsConstructor
public class TeAlgoImageLineageServiceImpl implements TeAlgoImageLineageService {

    private final TeAlgoImageLineageRepository teAlgoImageLineageRepository;
    private final TeAlgoImageLineageMapper teAlgoImageLineageMapper;

    @Override
    public Map<String,Object> queryAll(TeAlgoImageLineageQueryCriteria criteria, Pageable pageable){
        Page<TeAlgoImageLineage> page = teAlgoImageLineageRepository.findAll((root, criteriaQuery, criteriaBuilder) -> QueryHelp.getPredicate(root,criteria,criteriaBuilder),pageable);
        return PageUtil.toPage(page.map(teAlgoImageLineageMapper::toDto));
    }

    @Override
    public List<TeAlgoImageLineageDto> queryAll(TeAlgoImageLineageQueryCriteria criteria){
        return teAlgoImageLineageMapper.toDto(teAlgoImageLineageRepository.findAll((root, criteriaQuery, criteriaBuilder) -> QueryHelp.getPredicate(root,criteria,criteriaBuilder)));
    }

    @Override
    @Transactional
    public TeAlgoImageLineageDto findById(Long id) {
        TeAlgoImageLineage teAlgoImageLineage = teAlgoImageLineageRepository.findById(id).orElseGet(TeAlgoImageLineage::new);
        ValidationUtil.isNull(teAlgoImageLineage.getId(),"TeAlgoImageLineage","id",id);
        return teAlgoImageLineageMapper.toDto(teAlgoImageLineage);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public TeAlgoImageLineageDto create(TeAlgoImageLineage resources) {
        return teAlgoImageLineageMapper.toDto(teAlgoImageLineageRepository.save(resources));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(TeAlgoImageLineage resources) {
        TeAlgoImageLineage teAlgoImageLineage = teAlgoImageLineageRepository.findById(resources.getId()).orElseGet(TeAlgoImageLineage::new);
        ValidationUtil.isNull( teAlgoImageLineage.getId(),"TeAlgoImageLineage","id",resources.getId());
        teAlgoImageLineage.copy(resources);
        teAlgoImageLineageRepository.save(teAlgoImageLineage);
    }

    @Override
    public void deleteAll(Long[] ids) {
        for (Long id : ids) {
            teAlgoImageLineageRepository.deleteById(id);
        }
    }

    @Override
    public void download(List<TeAlgoImageLineageDto> all, HttpServletResponse response) throws IOException {
        List<Map<String, Object>> list = new ArrayList<>();
        for (TeAlgoImageLineageDto teAlgoImageLineage : all) {
            Map<String,Object> map = new LinkedHashMap<>();
            map.put("镜像名称", teAlgoImageLineage.getImage());
            map.put("镜像标签", teAlgoImageLineage.getTag());
            map.put("作业id", teAlgoImageLineage.getJobId());
            map.put("作业名称", teAlgoImageLineage.getJobName());
            map.put("-1:已删除 0:初始化 1:已创建作业/flink未启动 2:部署中/启动中/flink发布中 3:job上线中/flink运行中 4:job启动失败/flink发布失败 5:job下线中/flink下线中 6:job已下线/flink已下线  7:job运行异常/flink运行异常 8:flink运行已结束", teAlgoImageLineage.getJobStatus());
            map.put("部门标签  所属业务域", teAlgoImageLineage.getDepartmentLabel());
            map.put("job创建人", teAlgoImageLineage.getCreateUserName());
            map.put("libra作业负责人", teAlgoImageLineage.getLibraOwers());
            map.put("任务创建时间", teAlgoImageLineage.getJobCreateTime());
            map.put("任务更新时间", teAlgoImageLineage.getJobUpdateTime());
            map.put("创建时间", teAlgoImageLineage.getCreateTime());
            map.put("修改时间", teAlgoImageLineage.getModifyTime());
            list.add(map);
        }
        FileUtil.downloadExcel(list, response);
    }
}
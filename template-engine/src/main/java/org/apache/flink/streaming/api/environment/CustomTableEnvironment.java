package org.apache.flink.streaming.api.environment;

import org.apache.flink.table.api.internal.CustomTableEnvironmentImpl;
import org.apache.flink.table.api.EnvironmentSettings;
import org.apache.flink.table.api.Table;
import org.apache.flink.table.api.TableEnvironment;

/**
 * @description: /
 * @Author: lmh
 * @date: 2023/3/7 15:36
 */
public interface CustomTableEnvironment extends TableEnvironment {

    /**
     * Creates a table environment that is the entry point and central context for creating Table
     * and SQL API programs.
     *
     * <p>It is unified both on a language level for all JVM-based languages (i.e. there is no
     * distinction between Scala and Java API) and for bounded and unbounded data processing.
     *
     * <p>A table environment is responsible for:
     *
     * <ul>
     *   <li>Connecting to external systems.
     *   <li>Registering and retrieving {@link Table}s and other meta objects from a catalog.
     *   <li>Executing SQL statements.
     *   <li>Offering further configuration options.
     * </ul>
     *
     * <p>Note: This environment is meant for pure table programs. If you would like to convert from
     * or to other Flink APIs, it might be necessary to use one of the available language-specific
     * table environments in the corresponding bridging modules.
     *
     * @param settings The environment settings used to instantiate the {@link CustomTableEnvironment}.
     * @return {@link CustomTableEnvironment}.
     */
    static CustomTableEnvironment create(EnvironmentSettings settings) {
        return CustomTableEnvironmentImpl.create(settings);
    }

}

    public Table #transformFunction(TableEnvironmentImpl tEnv) {
        if(registeredTable.containsKey("#transformFunction")){ return (Table)registeredTable.get("#transformFunction");}
        Table inputTable = #inputFunction(tEnv);
        String columnStr = "#columnStr";
        columnStr = (StringUtil.isNullOrEmpty(columnStr) ? generateColumnStr(inputTable) : columnStr);
        String primaryKeyStr = #primaryKeyStr;
        String schemaStr = StringUtil.isNullOrEmpty(primaryKeyStr) ? columnStr:columnStr+","+primaryKeyStr;
        tEnv.executeSql("CREATE TABLE IF NOT EXISTS #tableName (#schema) WITH (#config)".replace("#schema",schemaStr));
        tEnv.createTemporaryView("#viewName",inputTable);
        tEnv.sqlUpdate("INSERT INTO #tableName SELECT * FROM #viewName");
        return null;
    }

    public Table #transformFunction(TableEnvironmentImpl tEnv) {
        if(registeredTable.containsKey("#transformFunction")){ return (Table)registeredTable.get("#transformFunction");}
        DataType row = getDataType(#schema);
        Table t = tEnv.fromValues(row,Arrays.asList(#drillData));
        tEnv.createTemporaryView("#tableName",t);
        Table retTable;
        if(!#isOfflineSource){
            tEnv.createTemporaryView("#viewName", tEnv.sqlQuery("#combinedTransformExpression"));
            retTable = tEnv.from("#viewName");
        }else{
            retTable = tEnv.from("#tableName");
        }
        registeredTable.put("#transformFunction",retTable);
        return retTable;
    }
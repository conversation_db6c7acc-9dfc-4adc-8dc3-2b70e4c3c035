    public Table #transformFunction(TableEnvironmentImpl tEnv) {
        if(registeredTable.containsKey("#transformFunction")){ return (Table)registeredTable.get("#transformFunction");}
        Table inputTable = #inputFunction(tEnv);
        tEnv.createTemporaryView("#viewName",inputTable);
        List fcollect = new ArrayList();
        List collect = new ArrayList();
        for(Column col : inputTable.getResolvedSchema().getColumns()) {
            fcollect.add("`" + col.getName() + "`");
            collect.add("'"+col.getName()+"'," + "`" + col.getName() + "`");
        }
        String finalSelect = String.join(",",fcollect);
        if(!StringUtil.isNullOrEmpty("#dynamicExtSelect")){
            finalSelect = finalSelect + ", #dynamicExtSelect";
        }else{
            finalSelect = finalSelect + ", FLK_DYNAMIC_RULE_TAG";
        }
        String dynamic_rule_wrapped_msg = "JsonBuild(" + String.join(",",collect) + ") as dynamic_rule_wrapped_msg" ;
        Table retTable = tEnv.sqlQuery("select #finalSelect from ( select *,dynamicRule(dynamic_rule_wrapped_msg) as FLK_DYNAMIC_RULE_TAG from (SELECT *,#dynamic_rule_wrapped_msg FROM #viewName ) tmp0 ) tmp1 where FLK_DYNAMIC_RULE_TAG <> 'DYNAMIC_RULE_FILTER_FLAG'".replace("#dynamic_rule_wrapped_msg",dynamic_rule_wrapped_msg).replace("#finalSelect",finalSelect));
        registeredTable.put("#transformFunction",retTable);
        return retTable;
    }
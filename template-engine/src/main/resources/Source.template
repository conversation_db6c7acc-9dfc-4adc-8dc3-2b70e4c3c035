    public Table #transformFunction(TableEnvironmentImpl tEnv) {
        if(registeredTable.containsKey("#transformFunction")){ return (Table)registeredTable.get("#transformFunction");}
        tEnv.executeSql("CREATE TABLE IF NOT EXISTS `#tableName` (#schema) WITH (#config)".replace("#startTimeStr",relative2AbsoluteTs(tEnv.getConfig().getConfiguration().getString("relative.start.time","day"))+""));
        Table retTable;
        if(#needPreCombine){
            tEnv.createTemporaryView("#preCombinedViewName", tEnv.sqlQuery("#preCombinedTransformExpression"));
        }
        if(!#isOfflineSource){
            tEnv.createTemporaryView("#viewName", tEnv.sqlQuery("#combinedTransformExpression"));
            retTable = tEnv.from("#viewName");
        }else{
            retTable = tEnv.from("#tableName");
        }
        registeredTable.put("#transformFunction",retTable);
        return retTable;
    }
#==============================================================================
# User-defined functions
#==============================================================================

# Define scalar, aggregate, or table functions here.

functions:

  # JSON FUNCTION
  - name: json_path_update
    from: class
    class: com.flink.udf.json.JsonUpdateValueFunction

  - name: json_path_update_origin
    from: class
    class: com.flink.udf.json.JsonUpdateValueOriginFunction

  - name: json_path_delete
    from: class
    class: com.flink.udf.json.JsonDeleteValueFunction

  - name: print
    from: class
    class: com.flink.udf.string.Print

  - name: json_value
    from: class
    class: com.flink.udf.json.UDFJson

  - name: blink_date_format
    from: class
    class: com.flink.udf.temporal.BlinkDateFormat

  # URL FUNCTION
  - name: url_encode
    from: class
    class: com.flink.udf.url.URLEncodeFunction

  - name: uel_decode
    from: class
    class: com.flink.udf.url.URLDecodeFunction

  # MATH FUNCTION
  - name: blink_round
    from: class
    class: com.flink.udf.math.BlinkRound

  # STRING FUNCTION
  - name: KEYVALUE
    from: class
    class: com.flink.udf.string.KeyValueFunction

  - name: blink_concat
    from: class
    class: com.flink.udf.string.BlinkConcatFunction

  - name: date_add
    from: class
    class: com.flink.udf.temporal.DateAddFunction

  - name: date_sub
    from: class
    class: com.flink.udf.temporal.DateSubFunction

  # Table Function
  - name: string_split
    from: class
    class: com.flink.udtf.StringSplitTableFunction

  - name: blink_string_split
    from: class
    class: com.flink.udf.string.BlinkStringSplitTableFunction

  - name: json_tuple
    from: class
    class: com.flink.udtf.JsonTupleTableFunction

  - name: json_array_split
    from: class
    class: com.flink.udtf.JsonArraySplitTableFunction

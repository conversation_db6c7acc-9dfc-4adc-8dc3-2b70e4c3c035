    public ModifyOperation #transformFunction(TableEnvironmentImpl tEnv) {
        Table inputTable = #inputFunction(tEnv);
        if(#existsExtSql){
            tEnv.createTemporaryView("#extSqlView",inputTable);
            inputTable = tEnv.sqlQuery("#extSql");
        }
		List collect = new ArrayList();
		for(Column col : inputTable.getResolvedSchema().getColumns()) {
			collect.add("`" + col.getName() + "`");
		}
		String allFields = String.join(",",collect);
        String columnStr = "#columnStr";
        columnStr = (StringUtil.isNullOrEmpty(columnStr) ? generateColumnStr(inputTable) : columnStr);
        String primaryKeyStr = "#primaryKeyStr";
        String schemaStr = StringUtil.isNullOrEmpty(primaryKeyStr) ? columnStr:columnStr+","+primaryKeyStr;
        tEnv.executeSql("CREATE TABLE IF NOT EXISTS #tableName (#schema) WITH (#config)".replace("#schema",schemaStr));
        UnresolvedIdentifier unresolvedIdentifier = tEnv.getParser().parseIdentifier("#tableName");
        ObjectIdentifier objectIdentifier = tEnv.getCatalogManager().qualifyIdentifier(unresolvedIdentifier);
        return new CatalogSinkModifyOperation(objectIdentifier, inputTable.getQueryOperation(), Collections.emptyMap(), false, Collections.emptyMap());
    }
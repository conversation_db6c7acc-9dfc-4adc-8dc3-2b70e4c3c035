    public ModifyOperation #transformFunction(TableEnvironmentImpl tEnv) {
         Table inputTable = #inputFunction(tEnv);
                if(#existsExtSql){
                    tEnv.createTemporaryView("#extSqlView",inputTable);
                    inputTable = tEnv.sqlQuery("#extSql");
                }
        tEnv.executeSql("CREATE TABLE IF NOT EXISTS #tableName (#schema) WITH (#config)");
        UnresolvedIdentifier unresolvedIdentifier = tEnv.getParser().parseIdentifier("#tableName");
        ObjectIdentifier objectIdentifier = tEnv.getCatalogManager().qualifyIdentifier(unresolvedIdentifier);
        return new CatalogSinkModifyOperation(objectIdentifier, inputTable.getQueryOperation(), Collections.emptyMap(), false, Collections.emptyMap());
    }

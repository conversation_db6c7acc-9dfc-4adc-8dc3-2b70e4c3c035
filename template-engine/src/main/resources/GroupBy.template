    public Table #transformFunction(TableEnvironmentImpl tEnv) {
        if(registeredTable.containsKey("#transformFunction")){ return (Table)registeredTable.get("#transformFunction");}
        Table inputTable = #inputFunction(tEnv);
        tEnv.createTemporaryView("#viewName",inputTable);
        Table retTable = tEnv.sqlQuery("SELECT #selectExpression FROM #viewName where #filterExpression GROUP BY #groupByExpression#havingExpression");
        registeredTable.put("#transformFunction",retTable);
        return retTable;
    }
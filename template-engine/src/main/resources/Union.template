    public Table #transformFunction(TableEnvironmentImpl tEnv) {
        if(registeredTable.containsKey("#transformFunction")){ return (Table)registeredTable.get("#transformFunction");}
        Table leftTable = #leftFunction(tEnv);
        tEnv.createTemporaryView("#leftView", leftTable);
        Table rightTable = #rightFunction(tEnv);
        tEnv.createTemporaryView("#rightView", rightTable);
        Table retTable = tEnv.sqlQuery("SELECT #leftSelectExpression FROM #leftView where #leftFilterExpression  union all  SELECT #rightSelectExpression FROM #rightView where #rightFilterExpression ");
        registeredTable.put("#transformFunction",retTable);
        return retTable;
    }

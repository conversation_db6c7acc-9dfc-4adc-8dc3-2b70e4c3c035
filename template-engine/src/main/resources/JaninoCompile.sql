package com.dewu.flink.template.translator;
import org.apache.flink.api.java.tuple.Tuple2;
import org.apache.flink.table.api.Table;
import org.apache.flink.table.api.bridge.java.StreamTableEnvironment;
import org.apache.flink.table.api.internal.TableEnvironmentImpl;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.flink.table.api.bridge.java.internal.StreamTableEnvironmentImpl;
import org.apache.flink.table.api.EnvironmentSettings;
import org.apache.flink.table.operations.ModifyOperation;
import org.apache.flink.table.operations.Operation;
import org.apache.flink.table.catalog.ObjectIdentifier;
import org.apache.flink.table.catalog.UnresolvedIdentifier;
import org.apache.flink.table.operations.CatalogSinkModifyOperation;
import org.apache.flink.table.api.ExplainDetail;
import org.apache.flink.table.catalog.Column;
import org.apache.flink.shaded.netty4.io.netty.util.internal.StringUtil;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.runtime.minicluster.MiniCluster;
import org.apache.flink.runtime.minicluster.MiniClusterConfiguration;
import org.apache.flink.streaming.util.TestStreamEnvironment;
import java.util.*;
import java.util.HashMap;
import java.util.Map;
import java.util.Collections;
import com.google.common.base.Joiner;
import org.apache.flink.streaming.api.functions.sink.RichSinkFunction;
import org.apache.flink.types.Row;
import org.apache.flink.util.StringUtils;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;
import org.apache.flink.api.java.typeutils.RowTypeInfo;
import org.apache.flink.table.api.DataTypes;
import org.apache.flink.table.types.DataType;
import org.apache.flink.table.types.FieldsDataType;
import org.apache.flink.table.types.logical.RowType;
import com.dewu.flink.template.drill.DataDrillSink;
import static org.hamcrest.CoreMatchers.equalTo;
import static org.junit.Assert.assertThat;
import org.junit.Test;
import org.apache.flink.test.util.MiniClusterWithClientResource;
import org.apache.flink.runtime.testutils.MiniClusterResourceConfiguration;

public class TestFlinkTemplateJob {
    private Map<String, Table> registeredTable = new HashMap();
    private StreamExecutionEnvironment env ;
    private MiniCluster miniCluster;
    @Test
    public void execute() throws Exception{
        if (true){
        miniCluster = new MiniCluster(new MiniClusterConfiguration.Builder()
                .setConfiguration(new Configuration())
                .setNumTaskManagers(
                        1)
                .setNumSlotsPerTaskManager(
                        1).build());
            try {
                miniCluster.start();
            } catch (Exception e) {
                e.printStackTrace();
            }
            env = new TestStreamEnvironment(miniCluster, 1);


		}else {
           env = StreamExecutionEnvironment.getExecutionEnvironment();
		}
        EnvironmentSettings settings = EnvironmentSettings.newInstance().inStreamingMode().useBlinkPlanner().build();
        TableEnvironmentImpl tEnv = (TableEnvironmentImpl)StreamTableEnvironment.create(env, settings);
        tEnv.executeSql("CREATE FUNCTION SetConvertSingle AS 'com.alibaba.blink.udx.SetConvertSingle'");
        tEnv.executeSql("CREATE FUNCTION JsonValue AS 'com.alibaba.blink.udx.udf.JsonValue'");

tEnv.executeSql("CREATE FUNCTION touFangDataAnalysis AS 'com.alibaba.blink.udx.udf.TouFangDataAnalysis'");tEnv.executeSql("CREATE FUNCTION binlogParser AS 'com.alibaba.blink.udx.udtf.BinlogParserFunction'");tEnv.executeSql("CREATE FUNCTION msgpackFetch AS 'com.poizon.udf.msgpack.MsgPackParserUdf'");tEnv.executeSql("CREATE FUNCTION JsonPath AS 'com.flink.udf.json.UDFJson'");

        Configuration configuration =  tEnv.getConfig().getConfiguration();
        // default configs
        configuration.setString("table.exec.mini-batch.allow-latency","10000");
        configuration.setString("table.exec.mini-batch.enabled","true");
        configuration.setString("table.exec.mini-batch.size","1000000");

        configuration.setString("job_name","rt_test_chenyuhao");
        configuration.setString("job_id","#jobId");
        configuration.setString("execute.job.jdbc.url","#url");
        configuration.setString("execute.job.jdbc.username","#username");
        configuration.setString("execute.job.jdbc.password","#password");
        env.getConfig().setGlobalJobParameters(configuration);
        //user defined configs

		sink_0452fe460be644a5a18321c812b35a89(tEnv);

        if(!true){
             try {
        	    tEnv.execute("rt_test_chenyuhao");
            } catch (Exception e) {
                e.printStackTrace();
             }
           }
    }
    public void explain() throws Exception{
        StreamExecutionEnvironment env = StreamExecutionEnvironment.getExecutionEnvironment();
        EnvironmentSettings settings = EnvironmentSettings.newInstance().inStreamingMode().useBlinkPlanner().build();
        TableEnvironmentImpl tEnv = (TableEnvironmentImpl)StreamTableEnvironment.create(env, settings);
        tEnv.executeSql("CREATE FUNCTION SetConvertSingle AS 'com.alibaba.blink.udx.SetConvertSingle'");
        tEnv.executeSql("CREATE FUNCTION JsonValue AS 'com.alibaba.blink.udx.udf.JsonValue'");
        List<Operation> operations = new ArrayList();
tEnv.executeSql("CREATE FUNCTION touFangDataAnalysis AS 'com.alibaba.blink.udx.udf.TouFangDataAnalysis'");tEnv.executeSql("CREATE FUNCTION binlogParser AS 'com.alibaba.blink.udx.udtf.BinlogParserFunction'");tEnv.executeSql("CREATE FUNCTION msgpackFetch AS 'com.poizon.udf.msgpack.MsgPackParserUdf'");tEnv.executeSql("CREATE FUNCTION JsonPath AS 'com.flink.udf.json.UDFJson'");

        Configuration configuration =  tEnv.getConfig().getConfiguration();

		sink_0452fe460be644a5a18321c812b35a89(tEnv);

        System.out.println(tEnv.explainInternal(operations,ExplainDetail.JSON_EXECUTION_PLAN));
    }
    private String generateColumnStr(Table inputTable){
        List<Column> columnList = inputTable.getResolvedSchema().getColumns();
        String columnStr = "";
        for (Column column:columnList){
            columnStr += "`"+column.getName()+"`"+column.getDataType().getLogicalType().toString().replace("*PROCTIME*","")+",";
        }
        return columnStr.substring(0,columnStr.length()-1);
    }

     private FieldsDataType getDataType(DataTypes.Field... fields){
        List logicalFields = new ArrayList();
        List fieldDataTypes = new ArrayList();
        for (DataTypes.Field field : fields) {
            logicalFields.add( new RowType.RowField(
                    field.getName(),
                    field.getDataType().getLogicalType(),
                    ""));
            fieldDataTypes.add(field.getDataType());
        }
       return new FieldsDataType(new RowType(logicalFields), fieldDataTypes);
    }

    public Long relative2AbsoluteTs(String relativeTs){
    		String[] arr = relativeTs.split("-");
    		String unit = arr[0].trim().toUpperCase();
    		Integer gap = arr.length == 1 ? 0 : Integer.parseInt(arr[1].trim());
    		Long currentTs = System.currentTimeMillis();
    		switch (unit){
    			case "DAY":
    				return currentTs - currentTs % (1000 * 3600 * 24 ) - gap * (1000 * 3600 * 24) - (1000 * 3600 * 8);
    			case "HOUR":
    				return currentTs - currentTs % (1000 * 3600) - gap * (1000 * 3600);
    			case "MINUTE":
    				return currentTs - currentTs % (1000 * 60) - gap * (1000 * 60);
    			case "SECOND":
    				return currentTs - currentTs % 1000 - gap * 1000 ;
    			default:
    				throw new IllegalArgumentException("Unsupport relativeTs:"+relativeTs);

    		}
    	}
    public Table source_489abd6a83c04401b958c4ae25935c5c(TableEnvironmentImpl tEnv) {
        if(registeredTable.containsKey("source_489abd6a83c04401b958c4ae25935c5c")){ return (Table)registeredTable.get("source_489abd6a83c04401b958c4ae25935c5c");}
        DataType row = getDataType( DataTypes.FIELD("message", DataTypes.STRING()) );
        Table t = tEnv.fromValues(row,Arrays.asList(Row.of("{\"database\":\"dw_delivery_marketing_api\",\"data\":[{\"feature\":\"xxx\",\"id\":\"2170615408\"}],\"pkNames\":[\"id\"],\"old\":[{\"modify_time\":\"2023-02-23 17:30:27\"}],\"id\":29758629,\"type\":\"UPDATE\",\"es\":1677146564000,\"isDdl\":false,\"table\":\"dp_ad_plan\",\"sql\":\"\",\"ts\":1677146564672}"),Row.of("{\"database\":\"dw_delivery_marketing_api\",\"data\":[{\"feature\":\"xxx\",\"id\":\"2170615409\"}],\"pkNames\":[\"id\"],\"old\":[{\"modify_time\":\"2023-02-23 17:30:27\"}],\"id\":29758629,\"type\":\"UPDATE\",\"es\":1677146564000,\"isDdl\":false,\"table\":\"dp_ad_plan\",\"sql\":\"\",\"ts\":1677146564672}")));
        tEnv.createTemporaryView("source_3c3cbcb23dba4fd5b2d026b95b90a57c",t);
        Table retTable;
        if(!false){
            tEnv.createTemporaryView("view_d24b9dd5bb9d46e38e15c5a692c29791", tEnv.sqlQuery("SELECT * FROM ( SELECT *,ROW_NUMBER() OVER (PARTITION BY `id` ORDER BY proctime() desc) AS row_num_src1 FROM (SELECT JsonValue(`data`,'$.order_no') AS `order_no`,JsonValue(`data`,'$.relation_info') AS `relation_info`,JsonValue(`data`,'$.address_info') AS `address_info`,cast(JsonValue(`data`,'$.flag') AS BIGINT) AS `flag`,cast(JsonValue(`data`,'$.create_time') AS TIMESTAMP) AS `create_time`,cast(JsonValue(`data`,'$.discount_amount') AS BIGINT) AS `discount_amount`,cast(JsonValue(`data`,'$.pay_amount') AS BIGINT) AS `pay_amount`,cast(JsonValue(`data`,'$.modify_time') AS TIMESTAMP) AS `modify_time`,JsonValue(`data`,'$.buyer_name') AS `buyer_name`,cast(JsonValue(`data`,'$.buyer_id') AS BIGINT) AS `buyer_id`,cast(JsonValue(`data`,'$.create_time_us') AS TIMESTAMP) AS `create_time_us`,JsonValue(`data`,'$.platform') AS `platform`,cast(JsonValue(`data`,'$.order_status') AS INT) AS `order_status`,cast(JsonValue(`data`,'$.freight_amount') AS BIGINT) AS `freight_amount`,JsonValue(`data`,'$.feature') AS `feature`,cast(JsonValue(`data`,'$.is_del') AS TINYINT) AS `is_del`,cast(JsonValue(`data`,'$.id') AS BIGINT) AS `id`,cast(JsonValue(`data`,'$.deliver_status') AS INT) AS `deliver_status`,JsonValue(`data`,'$.source_name') AS `source_name`,cast(JsonValue(`data`,'$.product_amount') AS BIGINT) AS `product_amount`,dts_operation_flag as FLK_BINLOG_RECORD_TYPE, CAST(JsonValue(cast(`message` as varchar),'$.es') AS BIGINT) AS FLK_BINLOG_TS FROM `source_3c3cbcb23dba4fd5b2d026b95b90a57c`,  LATERAL TABLE (binlogParser (cast(`message` as varbinary))) as tmp (data,old_data,dts_operation_flag,dts_db_name,dts_table_name,dts_before_flag,dts_after_flag))) WHERE row_num_src1 <= 1"));
            retTable = tEnv.from("view_d24b9dd5bb9d46e38e15c5a692c29791");
        }else{
            retTable = tEnv.from("source_3c3cbcb23dba4fd5b2d026b95b90a57c");
        }
        registeredTable.put("source_489abd6a83c04401b958c4ae25935c5c",retTable);
        return retTable;
    }
public Table lookup_join_069d370886bd418ab1b6c2e31d806d2b(TableEnvironmentImpl tEnv) {
        if(registeredTable.containsKey("lookup_join_069d370886bd418ab1b6c2e31d806d2b")){ return (Table)registeredTable.get("lookup_join_069d370886bd418ab1b6c2e31d806d2b");}
        DataType row = getDataType(DataTypes.FIELD("feature", DataTypes.STRING()),DataTypes.FIELD("id", DataTypes.DECIMAL(21,0)));
        Table retTable = tEnv.fromValues(row,Arrays.asList(Row.of("sss",1),Row.of("ttt",2)));
        tEnv.createTemporaryView("lookup_table_30a8b5b5b3804a3c80110e66d2fbbcde",retTable);
        registeredTable.put("lookup_join_069d370886bd418ab1b6c2e31d806d2b",retTable);
        return retTable;
    }
    public Table sink_0452fe460be644a5a18321c812b35a89(TableEnvironmentImpl tEnv) throws Exception{
        if(registeredTable.containsKey("sink_0452fe460be644a5a18321c812b35a89")){ return (Table)registeredTable.get("sink_0452fe460be644a5a18321c812b35a89");}
        Table inputTable = lookup_join_069d370886bd418ab1b6c2e31d806d2b(tEnv);
        tEnv.createTemporaryView("view_7c365267e8f14782a44ba4725994f871",inputTable);

        if(false){
            tEnv.createTemporaryView("view_fbd4cadf59c1434bb33a72b5e4404044",inputTable);
            inputTable = tEnv.sqlQuery("");
        }

        String primaryKeyStr = "primary Key(id) NOT ENFORCED";
        String primaryKey =  "id";
        List<String> columnNames = inputTable.getResolvedSchema().getColumnNames();
        DataDrillSink sink = new DataDrillSink(columnNames,primaryKey);
        ((StreamTableEnvironmentImpl)tEnv).toRetractStream(inputTable, new RowTypeInfo(inputTable.getSchema().getFieldTypes(),(String[]) columnNames.toArray(new String[0]))).addSink(sink);

               try {
        	    env.execute("test_case");
            } catch (Exception e) {
                e.printStackTrace();
             }


        List<String> actuel = sink.getResults();
        if(false){
          assertThat(actuel, equalTo(Arrays.asList(Row.of("sss",1),Row.of("ttt",2))));
         }else{
                  System.out.println(actuel);
                   miniCluster.close();
         }

        return null;
    }




}

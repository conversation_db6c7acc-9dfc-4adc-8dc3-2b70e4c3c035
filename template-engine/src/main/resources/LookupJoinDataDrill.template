public Table #transformFunction(TableEnvironmentImpl tEnv) {
        if(registeredTable.containsKey("#transformFunction")){ return (Table)registeredTable.get("#transformFunction");}
        DataType row = getDataType(#schema);
        Table retTable = tEnv.fromValues(row,Arrays.asList(#drillData));
        tEnv.createTemporaryView("#tableName",retTable);
        registeredTable.put("#transformFunction",retTable);
        return retTable;
    }
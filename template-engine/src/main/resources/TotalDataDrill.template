package com.dewu.flink.template.translator.example;

import org.apache.flink.table.api.Table;
import org.apache.flink.table.api.bridge.java.StreamTableEnvironment;
import org.apache.flink.table.api.internal.TableEnvironmentImpl;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.flink.table.api.bridge.java.internal.StreamTableEnvironmentImpl;
import org.apache.flink.table.api.EnvironmentSettings;
import org.apache.flink.table.api.*;
import org.apache.flink.table.expressions.Expression;
import org.apache.flink.table.operations.ModifyOperation;
import org.apache.flink.table.operations.Operation;
import org.apache.flink.table.catalog.ObjectIdentifier;
import org.apache.flink.table.catalog.UnresolvedIdentifier;
import org.apache.flink.table.operations.CatalogSinkModifyOperation;
import org.apache.flink.table.catalog.Column;
import org.apache.flink.shaded.netty4.io.netty.util.internal.StringUtil;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.runtime.minicluster.MiniCluster;
import org.apache.flink.runtime.minicluster.MiniClusterConfiguration;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.util.*;
import java.util.HashMap;
import java.util.Map;
import java.util.Collections;
import com.google.common.base.Joiner;
import org.apache.flink.types.Row;
import org.apache.flink.util.StringUtils;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import org.apache.flink.api.java.typeutils.RowTypeInfo;
import org.apache.flink.table.api.DataTypes;
import org.apache.flink.table.types.DataType;
import org.apache.flink.table.types.FieldsDataType;
import org.apache.flink.table.types.logical.RowType;
import com.google.common.collect.Lists;
import org.apache.flink.streaming.api.environment.CustomTableEnvironment;
import org.apache.flink.table.api.internal.CustomTableEnvironmentImpl;
import org.apache.flink.table.api.EnvironmentSettings;
import org.apache.flink.table.api.ExplainDetail;
import org.apache.flink.table.api.Table;
import org.apache.flink.table.api.bridge.java.StreamTableEnvironment;
import org.apache.flink.table.api.bridge.java.internal.StreamTableEnvironmentImpl;
import org.apache.flink.table.api.internal.CustomTableEnvironmentImpl;
import org.apache.flink.table.api.internal.TableEnvironmentImpl;
import org.apache.flink.table.catalog.Column;
import org.apache.flink.table.functions.FunctionService;
import org.apache.flink.table.functions.UserDefinedFunction;
import org.apache.flink.table.operations.Operation;
import org.yaml.snakeyaml.Yaml;
import java.io.InputStream;

import java.io.IOException;
import java.net.URL;
import static org.apache.flink.table.api.Expressions.*;
import static org.apache.flink.table.api.Expressions.$;
import com.dewu.flink.template.drill.DataDrillSink;
import static org.hamcrest.CoreMatchers.equalTo;
import static org.junit.Assert.assertThat;
import com.dewu.flink.template.utils.JDBCUtils;
import org.apache.flink.streaming.util.TestStreamEnvironment;
import org.apache.flink.api.java.tuple.Tuple2;
import org.apache.flink.table.types.logical.LocalZonedTimestampType;
import org.apache.flink.table.types.logical.TimestampType;
import org.apache.flink.table.types.logical.ZonedTimestampType;

public class TestFlinkTemplateJob {
    private Map<String, Table> registeredTable = new HashMap();
    private StreamExecutionEnvironment env ;
    private String jobName = "";
    private String sessionId = "";
    private String dbUrl = "";
    private String dbUser = "";
    private String dbPassword = "";

    public static void main(String[] args) throws Exception{
      MiniCluster miniCluster = new MiniCluster(new MiniClusterConfiguration.Builder()
            .setConfiguration(new Configuration())
            .setNumTaskManagers(
                  1)
            .setNumSlotsPerTaskManager(
                  1).build());
      try {
         miniCluster.start();
      } catch (Exception e) {
         e.printStackTrace();
      }
      new TestFlinkTemplateJob().execute(miniCluster);
   }

   	public void initFunctions(TableEnvironmentImpl tEnv) throws ClassNotFoundException {
   		InputStream f = Thread.currentThread().getContextClassLoader().getResourceAsStream("sql-client-defaults.yaml");
   		Yaml yaml = new Yaml();
   		Map properties = (Map) yaml.load(f);
   		System.out.println("properties的值为：" + properties);
   		List functionList = (List) properties.get("functions");
   		for (Object func : functionList) {
   			Map funcMeta = (Map) func;
   			String funcName = (String) funcMeta.get("name");
   			String className = (String) funcMeta.get("class");
   			tEnv.createFunction(funcName, (Class<? extends UserDefinedFunction>) Class.forName(className), true);
   			System.out.println("register function " + funcName + " success.");
   		}
   	}


    public void execute(MiniCluster miniCluster) throws Exception{

       env = new TestStreamEnvironment(miniCluster, 1);
		jobName = "#jobName";
		sessionId = "#sessionId";
		dbUrl = "#dbUrl";
		dbUser = "#dbUser";
		dbPassword = "#dbPassword";

        EnvironmentSettings settings = EnvironmentSettings.newInstance().inStreamingMode().useBlinkPlanner().build();
        TableEnvironmentImpl tEnv = (TableEnvironmentImpl)StreamTableEnvironment.create(env, settings);
        tEnv.executeSql("CREATE FUNCTION SetConvertSingle AS 'com.alibaba.blink.udx.SetConvertSingle'");
        tEnv.executeSql("CREATE FUNCTION JsonValue AS 'com.alibaba.blink.udx.udf.JsonValue'");

        #udfExpr
		initFunctions(tEnv);

        Configuration configuration =  tEnv.getConfig().getConfiguration();
        // default configs
        configuration.setString("table.exec.mini-batch.allow-latency","10000");
        configuration.setString("table.exec.mini-batch.enabled","true");
        configuration.setString("table.exec.mini-batch.size","1000000");

        configuration.setString("job_name","#jobName");
        configuration.setString("dynamic.rules.job.id","#jobId");
        configuration.setString("execute.job.jdbc.url","#url");
        configuration.setString("execute.job.jdbc.username","#username");
        configuration.setString("execute.job.jdbc.password","#password");
        env.getConfig().setGlobalJobParameters(configuration);
        //user defined configs
        #jobConfigurationStr
         #sinkFunctions

        env.execute("#jobName");

        com.alibaba.fastjson.JSONArray array = new com.alibaba.fastjson.JSONArray();
		for (DataDrillSink dataDrillSink : #globalsinkFunctionName) {
			 com.alibaba.fastjson.JSONObject jsonObject = new  com.alibaba.fastjson.JSONObject();
            Tuple2<Tuple2<String,String>, List<String>> dataDrillSinkResults =  dataDrillSink.getResults();
			jsonObject.put("sinkUniqueName", ((Tuple2<String,String>)dataDrillSinkResults.f0).f0);
			jsonObject.put("data", dataDrillSinkResults.f1);
            if(!((List<String>)dataDrillSinkResults.f1).isEmpty()){
                array.add(jsonObject);
            }
		}
        Properties pro = new Properties();
        pro.put("driverClassName", "com.mysql.jdbc.Driver");
        pro.put("url", dbUrl);
        pro.put("username", dbUser);
        pro.put("password", dbPassword);
        JDBCUtils jdbcUtils = new JDBCUtils(pro);
        Connection connection = jdbcUtils.getConnection();
        String insertSql = "insert into te_job_data_drill_result (`job_name`,`session_id`,`drill_result`) values (?,?,?)";
        PreparedStatement preparedStatement = connection.prepareStatement(insertSql);
        preparedStatement.setString(1, jobName);
        preparedStatement.setString(2, sessionId);
        preparedStatement.setString(3,  array.toString());
        preparedStatement.execute();
        jdbcUtils.close(preparedStatement, connection);

    }

    public void explain() throws Exception{
        StreamExecutionEnvironment env = StreamExecutionEnvironment.getExecutionEnvironment();
        EnvironmentSettings settings = EnvironmentSettings.newInstance().inStreamingMode().useBlinkPlanner().build();
        TableEnvironmentImpl tEnv = (TableEnvironmentImpl)StreamTableEnvironment.create(env, settings);
        tEnv.executeSql("CREATE FUNCTION SetConvertSingle AS 'com.alibaba.blink.udx.SetConvertSingle'");
        tEnv.executeSql("CREATE FUNCTION JsonValue AS 'com.alibaba.blink.udx.udf.JsonValue'");
        List<Operation> operations = new ArrayList();
        #udfExpr
		initFunctions(tEnv);

        Configuration configuration =  tEnv.getConfig().getConfiguration();
        #jobConfigurationStr
        #sinkFunctions
        System.out.println(tEnv.explainInternal(operations,ExplainDetail.JSON_EXECUTION_PLAN));
    }

    public void check() throws Exception{
		StreamExecutionEnvironment env = StreamExecutionEnvironment.getExecutionEnvironment();
		EnvironmentSettings settings = EnvironmentSettings.newInstance().inStreamingMode().useBlinkPlanner().build();
		CustomTableEnvironmentImpl tEnv = (CustomTableEnvironmentImpl) CustomTableEnvironment.create( settings);
        tEnv.executeSql("CREATE FUNCTION SetConvertSingle AS 'com.alibaba.blink.udx.SetConvertSingle'");
        tEnv.executeSql("CREATE FUNCTION JsonValue AS 'com.alibaba.blink.udx.udf.JsonValue'");

        #udfExpr
		initFunctions(tEnv);

        Configuration configuration =  tEnv.getConfig().getConfiguration();
        // default configs
        configuration.setString("table.exec.mini-batch.allow-latency","10000");
        configuration.setString("table.exec.mini-batch.enabled","true");
        configuration.setString("table.exec.mini-batch.size","1000000");
        configuration.setString("table.exec.validate.real-connect","true");
        configuration.setString("job_name","#jobName");
        configuration.setString("job_id","#jobId");
        configuration.setString("execute.job.jdbc.url","#url");
        configuration.setString("execute.job.jdbc.username","#username");
        configuration.setString("execute.job.jdbc.password","#password");
        env.getConfig().setGlobalJobParameters(configuration);
        //user defined configs
        #jobConfigurationStr
        #sinkFunctions

        tEnv.getJobGraph("#jobName");

    }

    private String generateColumnStr(Table inputTable){
        List<Column> columnList = inputTable.getResolvedSchema().getColumns();
        String columnStr = "";
        for (Column column:columnList){
            columnStr += "`"+column.getName()+"`"+column.getDataType().getLogicalType().toString().replace("*PROCTIME*","")+",";
        }
        return columnStr.substring(0,columnStr.length()-1);
    }

     private FieldsDataType getDataType(DataTypes.Field... fields){
        List logicalFields = new ArrayList();
        List fieldDataTypes = new ArrayList();
        for (DataTypes.Field field : fields) {
            logicalFields.add( new RowType.RowField(
                    field.getName(),
                    field.getDataType().getLogicalType(),
                    ""));
            fieldDataTypes.add(field.getDataType());
        }
       return new FieldsDataType(new RowType(logicalFields), fieldDataTypes);
   }

    private String generateColumnStr(Table inputTable, String cf){
        if(StringUtil.isNullOrEmpty(cf)){
            return generateColumnStr(inputTable);
        }
        List<Column> columnList = inputTable.getResolvedSchema().getColumns();
        String columnStr = "";
        for (Column column:columnList){
            columnStr += "`"+column.getName()+"`"+column.getDataType().getLogicalType().toString().replace("*PROCTIME*","")+",";
        }
        columnStr = columnStr.substring(0, columnStr.length() - 1);
        columnStr = "`" + cf + "` ROW <" + columnStr + ">";
        return columnStr;
    }

    public Long relative2AbsoluteTs(String relativeTs){
    		String[] arr = relativeTs.split("-");
    		String unit = arr[0].trim().toUpperCase();
    		Integer gap = arr.length == 1 ? 0 : Integer.parseInt(arr[1].trim());
    		Long currentTs = System.currentTimeMillis();
    		switch (unit){
    case "DAY":
    	return currentTs - currentTs % (1000 * 3600 * 24 ) - gap * (1000 * 3600 * 24) - (1000 * 3600 * 8);
    case "HOUR":
    	return currentTs - currentTs % (1000 * 3600) - gap * (1000 * 3600);
    case "MINUTE":
    	return currentTs - currentTs % (1000 * 60) - gap * (1000 * 60);
    case "SECOND":
    	return currentTs - currentTs % 1000 - gap * 1000 ;
    default:
    	throw new IllegalArgumentException("Unsupport relativeTs:"+relativeTs);

    		}
    	}
#transformationFunction



}

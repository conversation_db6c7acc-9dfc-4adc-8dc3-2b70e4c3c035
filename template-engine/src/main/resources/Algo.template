    public Table #transformFunction(TableEnvironmentImpl tEnv) {
        if(registeredTable.containsKey("#transformFunction")){ return (Table)registeredTable.get("#transformFunction");}

        // main logistic
		tEnv.executeSql("CREATE FUNCTION PythonExec AS 'com.alibaba.blink.udx.udf.algo.JCPUDTFFunction'");
        Table inputTable = #inputFunction(tEnv);
        tEnv.createTemporaryView("#viewName",inputTable);
        Table retTable = tEnv.sqlQuery("SELECT #selectExpression FROM (select cast(message as varchar) as inputMsg from #viewName),#lateralViewExpression ");

        // dispatch data


        registeredTable.put("#transformFunction",retTable);
        return retTable;
    }
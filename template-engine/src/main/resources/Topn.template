    public Table #transformFunction(TableEnvironmentImpl tEnv) {
        if(registeredTable.containsKey("#transformFunction")){ return (Table)registeredTable.get("#transformFunction");}
        Table inputTable = #inputFunction(tEnv);
        String topnTemplate = "SELECT #selectStr  FROM ( SELECT *,ROW_NUMBER() OVER (PARTITION BY #partitionStr ORDER BY #orderByStr) AS row_num FROM #viewName) WHERE row_num <= #n";
        tEnv.createTemporaryView("#viewName",inputTable);
        Table retTable = tEnv.sqlQuery(topnTemplate);
        registeredTable.put("#transformFunction",retTable);
        return retTable;
    }

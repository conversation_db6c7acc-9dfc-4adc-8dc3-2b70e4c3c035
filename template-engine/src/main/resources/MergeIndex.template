    public Table #transformFunction(TableEnvironmentImpl tEnv) {
        if(registeredTable.containsKey("#transformFunction")){ return (Table)registeredTable.get("#transformFunction");}
        Table inputTable = #inputFunction(tEnv);
        tEnv.createTemporaryView("#viewName",inputTable);
        tEnv.executeSql("CREATE FUNCTION IDXTPLMergeAdsIndexWithMetricValue AS 'com.alibaba.blink.udx.udaf.MergeAdsIndexWithMetricValue'");
        tEnv.executeSql("CREATE FUNCTION IDXTPLLastNonNullFunction AS 'com.alibaba.blink.udx.udaf.LastNonNullFunction'");
        Expression expr = (ApiExpression) Expressions.call("IDXTPLMergeAdsIndexWithMetricValue", $("metric_value"),$("ts").cast(DataTypes.BIGINT()),#resultCols).as(#resultCols);
        // call registered function in Table API
        Table retTable = tEnv
            .from("#viewName")
            .groupBy(#dimNames)
            .flatAggregate(expr)
            .select(#finalSelect);

        registeredTable.put("#transformFunction",retTable);
        return retTable;
    }
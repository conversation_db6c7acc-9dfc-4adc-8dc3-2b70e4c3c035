    public Table #transformFunction(TableEnvironmentImpl tEnv) {
        if(registeredTable.containsKey("#transformFunction")){ return (Table)registeredTable.get("#transformFunction");}
        Table leftTable = #leftFunction(tEnv);
        tEnv.createTemporaryView("#viewName",leftTable);
        String columnStr = "#columnStr";
        String primaryKeyStr = #primaryKeyStr;
        String schemaStr = StringUtil.isNullOrEmpty(primaryKeyStr) ? columnStr:columnStr+","+primaryKeyStr;
        String dimSql = "CREATE TABLE IF NOT EXISTS #tableName (#schema) WITH (#config)".replace("#schema",schemaStr);
        tEnv.executeSql(dimSql);
        Table retTable = tEnv.sqlQuery("SELECT #selectExpression FROM (select *,proctime() as proc_time from #viewName) as #leftViewAlias #joinType join #tableName FOR SYSTEM_TIME AS OF #leftViewAlias.proc_time AS #rightViewAlias ON #joinConditionExpression");
        registeredTable.put("#transformFunction",retTable);
        return retTable;
    }
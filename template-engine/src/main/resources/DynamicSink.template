    public Table #transformFunction(TableEnvironmentImpl tEnv) {
        if(registeredTable.containsKey("#transformFunction")){ return (Table)registeredTable.get("#transformFunction");}
        Table inputTable = #inputFunction(tEnv);
        if(#existsExtSql){
            tEnv.createTemporaryView("#extSqlView",inputTable);
            inputTable = tEnv.sqlQuery("#extSql");
        }
        tEnv.executeSql("CREATE TABLE IF NOT EXISTS #tableName (#schema) WITH (#config)");
        tEnv.createTemporaryView("#viewName",inputTable);
        tEnv.sqlUpdate("INSERT INTO #tableName SELECT * FROM #viewName");
        return null;
    }

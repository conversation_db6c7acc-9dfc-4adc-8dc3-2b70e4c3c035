    public Table #transformFunction(TableEnvironmentImpl tEnv) {
        if(registeredTable.containsKey("#transformFunction")){ return (Table)registeredTable.get("#transformFunction");}
        Table inputTable = #inputFunction(tEnv);
        if(null!=inputTable){
            if(#existsExtSql){
                tEnv.createTemporaryView("#extSqlView",inputTable);
                inputTable = tEnv.sqlQuery("#extSql");
            }
        }else{
            if(#existsExtSql){
                inputTable = tEnv.sqlQuery("#extSql");
            }
        }
		List collect = new ArrayList();
		for(Column col : inputTable.getResolvedSchema().getColumns()) {
			collect.add("`" + col.getName() + "`");
		}
		String allFields = String.join(",",collect);
        String columnStr = "#columnStr";
        String primaryKeyStr = "#primaryKeyStr";
        String cf = "#cf";
		String targetBinaryMessageCol = "#targetBinaryMessageCol";
		String targetBinaryMessageKeyCol = "#targetBinaryMessageKeyCol";

        tEnv.createTemporaryView("`#viewName`",inputTable);
		if(#isHbaseSink) {
            columnStr = (StringUtil.isNullOrEmpty(columnStr) ? generateColumnStr(inputTable, cf, primaryKeyStr) : columnStr);
            String schemaStr = columnStr;
            if (!StringUtil.isNullOrEmpty(primaryKeyStr) && StringUtil.isNullOrEmpty(cf)) {
                schemaStr = schemaStr + "," + primaryKeyStr;
            }
            String lastSelectStr = generateExtLastSelectStr(inputTable, cf, primaryKeyStr, "#viewName");
            tEnv.executeSql("CREATE TABLE IF NOT EXISTS `#tableName` (#schema) WITH (#config)".replace("#schema",schemaStr));
            tEnv.sqlUpdate("INSERT INTO `#tableName` " + lastSelectStr);
        }else if(#isBinarySink) {
            String lastSelectStr = generateExtLastSelectStr(inputTable, cf, primaryKeyStr, "SELECT * FROM `#viewName`");
            if(#sinkDedup){
                lastSelectStr = generateExtLastSelectStr(inputTable, cf, primaryKeyStr, "SELECT #dedupParserSelectExpr from( SELECT #dedupSelectExpr FROM #viewName group by #dedupKey)".replace("#dedupSelectExpr", generateExtLastDedupSelectExpr(inputTable, "#dedupKey")).replace("#dedupParserSelectExpr", generateExtLastDedupParserSelectExpr(inputTable)));
            }
            inputTable = tEnv.sqlQuery(lastSelectStr);
            tEnv.createTemporaryView("`f_glv_#tableName`",inputTable);

            tEnv.createTemporaryView("`binary_#viewName`",tEnv.sqlQuery("select " + generateBinaryViewStr(inputTable, targetBinaryMessageKeyCol, targetBinaryMessageCol, "#connectorType",#isOriginalSourceSink) + " from " + "`f_glv_#tableName`"));

            columnStr = generateBinaryColumnStr("#connectorType");
            String schemaStr = columnStr + ", PRIMARY KEY (`messageKey`) NOT ENFORCED ";
            tEnv.executeSql("CREATE TABLE IF NOT EXISTS `#tableName` (#schema) WITH (#config)".replace("#schema",schemaStr));
            tEnv.sqlUpdate("INSERT INTO `#tableName` select * from `binary_#viewName`");
        }else{
            String lastSelectStr = generateExtLastSelectStr(inputTable, cf, primaryKeyStr, "SELECT * FROM `#viewName`");
            if(#sinkDedup){
                lastSelectStr = generateExtLastSelectStr(inputTable, cf, primaryKeyStr, "SELECT #dedupParserSelectExpr from( SELECT #dedupSelectExpr FROM #viewName group by #dedupKey)".replace("#dedupSelectExpr", generateExtLastDedupSelectExpr(inputTable, "#dedupKey")).replace("#dedupParserSelectExpr", generateExtLastDedupParserSelectExpr(inputTable)));
            }
            inputTable = tEnv.sqlQuery(lastSelectStr);
            tEnv.createTemporaryView("`f_glv_#tableName`",inputTable);
            columnStr = (StringUtil.isNullOrEmpty(columnStr) ? generateColumnStr(inputTable, cf, primaryKeyStr) : columnStr);
            String schemaStr = columnStr;
            if (!StringUtil.isNullOrEmpty(primaryKeyStr) && StringUtil.isNullOrEmpty(cf)) {
                schemaStr = schemaStr + "," + primaryKeyStr;
            }
            tEnv.executeSql("CREATE TABLE IF NOT EXISTS `#tableName` (#schema) WITH (#config)".replace("#schema",schemaStr));

            tEnv.sqlUpdate("INSERT INTO `#tableName` select * from `f_glv_#tableName`");
        }
        return null;
    }

    public Table #transformFunction(TableEnvironmentImpl tEnv) {
        if(registeredTable.containsKey("#transformFunction")){ return (Table)registeredTable.get("#transformFunction");}
        Table leftTable = #leftFunction(tEnv);
        tEnv.createTemporaryView("#leftInputView",leftTable);
        Table rightTable = #rightFunction(tEnv);
        tEnv.createTemporaryView("#rightInputView",rightTable);
        if(#isDeduplicateByJoinKey){
            tEnv.createTemporaryView("#leftDeduplicateView", tEnv.sqlQuery("SELECT * FROM ( SELECT *,ROW_NUMBER() OVER (PARTITION BY #leftPartitionStr ORDER BY proctime() desc) AS row_num FROM #leftInputView) WHERE row_num <= 1"));
            tEnv.createTemporaryView("#rightDeduplicateView", tEnv.sqlQuery("SELECT * FROM ( SELECT *,ROW_NUMBER() OVER (PARTITION BY #rightPartitionStr ORDER BY proctime() desc) AS row_num FROM #rightInputView) WHERE row_num <= 1"));
        }
        Table retTable = tEnv.sqlQuery("SELECT #selectExpression FROM #leftView as #leftAlias #joinType join #rightView as #rightAlias ON #joinConditionExpression");
        registeredTable.put("#transformFunction",retTable);
        return retTable;
    }

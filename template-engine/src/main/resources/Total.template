    package com.dewu.flink.template.translator;

import org.apache.flink.table.api.Table;
import org.apache.flink.table.api.bridge.java.StreamTableEnvironment;
import org.apache.flink.table.api.internal.TableEnvironmentImpl;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.flink.table.api.bridge.java.internal.StreamTableEnvironmentImpl;
import org.apache.flink.table.api.EnvironmentSettings;
import org.apache.flink.table.api.*;
import org.apache.flink.table.expressions.Expression;
import org.apache.flink.table.operations.ModifyOperation;
import org.apache.flink.table.operations.Operation;
import org.apache.flink.table.catalog.ObjectIdentifier;
import org.apache.flink.table.catalog.UnresolvedIdentifier;
import org.apache.flink.table.operations.CatalogSinkModifyOperation;
import org.apache.flink.table.catalog.Column;
import org.apache.flink.shaded.netty4.io.netty.util.internal.StringUtil;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.runtime.minicluster.MiniCluster;
import org.apache.flink.runtime.minicluster.MiniClusterConfiguration;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.util.*;
import java.util.HashMap;
import java.util.Map;
import java.util.Collections;
import com.google.common.base.Joiner;
import org.apache.flink.types.Row;
import org.apache.flink.util.StringUtils;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import org.apache.flink.api.java.typeutils.RowTypeInfo;
import org.apache.flink.table.api.DataTypes;
import org.apache.flink.table.types.DataType;
import org.apache.flink.table.types.FieldsDataType;
import org.apache.flink.table.types.logical.RowType;
import com.google.common.collect.Lists;
import org.apache.flink.streaming.api.environment.CustomTableEnvironment;
import org.apache.flink.table.api.internal.CustomTableEnvironmentImpl;
import org.apache.flink.table.api.EnvironmentSettings;
import org.apache.flink.table.api.ExplainDetail;
import org.apache.flink.table.api.Table;
import org.apache.flink.table.api.bridge.java.StreamTableEnvironment;
import org.apache.flink.table.api.bridge.java.internal.StreamTableEnvironmentImpl;
import org.apache.flink.table.api.internal.CustomTableEnvironmentImpl;
import org.apache.flink.table.api.internal.TableEnvironmentImpl;
import org.apache.flink.table.catalog.Column;
import org.apache.flink.table.functions.FunctionService;
import org.apache.flink.table.functions.UserDefinedFunction;
import org.apache.flink.table.operations.Operation;
import org.yaml.snakeyaml.Yaml;
import java.io.InputStream;
import java.util.stream.Collectors;

import org.apache.flink.table.types.logical.LocalZonedTimestampType;
import org.apache.flink.table.types.logical.TimestampType;
import org.apache.flink.table.types.logical.ZonedTimestampType;
import java.io.IOException;
import java.net.URL;
import static org.apache.flink.table.api.Expressions.*;
import static org.apache.flink.table.api.Expressions.$;


public class #className {
    private Map<String, Table> registeredTable = new HashMap();
    private StreamExecutionEnvironment env ;
    private String jobName = "";
    private String sessionId = "";
    private String dbUrl = "";
    private String dbUser = "";
    private String dbPassword = "";
	private final ClassLoader systemClassLoader = ClassLoader.getSystemClassLoader();

	public void initFunctions(TableEnvironmentImpl tEnv) throws ClassNotFoundException {
		InputStream f = Thread.currentThread().getContextClassLoader().getResourceAsStream("sql-client-defaults.yaml");
		Yaml yaml = new Yaml();
		Map properties = (Map) yaml.load(f);
		System.out.println("properties的值为：" + properties);
		List functionList = (List) properties.get("functions");
		for (Object func : functionList) {
			Map funcMeta = (Map) func;
			String funcName = (String) funcMeta.get("name");
			String className = (String) funcMeta.get("class");
			tEnv.createFunction(funcName, (Class<? extends UserDefinedFunction>) Class.forName(className), true);
			System.out.println("register function " + funcName + " success.");
		}
	}

    public void execute() throws Exception{

        env = StreamExecutionEnvironment.getExecutionEnvironment();

        EnvironmentSettings settings = EnvironmentSettings.newInstance().inStreamingMode().useBlinkPlanner().build();
        TableEnvironmentImpl tEnv = (TableEnvironmentImpl)StreamTableEnvironment.create(env, settings);
        tEnv.executeSql("CREATE FUNCTION SetConvertSingle AS 'com.alibaba.blink.udx.SetConvertSingle'");
        tEnv.executeSql("CREATE FUNCTION JsonValue AS 'com.alibaba.blink.udx.udf.JsonValue'");
        tEnv.executeSql("CREATE FUNCTION if not exists JsonStringUdf as 'com.alibaba.blink.udx.udf.JsonStringUdfV2'");
        #udfExpr
		initFunctions(tEnv);

        Configuration configuration =  tEnv.getConfig().getConfiguration();
        // default configs

        configuration.setString("table.exec.sink.not-null-enforcer","drop");
        configuration.setString("table.exec.mini-batch.allow-latency","10000");
        configuration.setString("table.exec.mini-batch.enabled","true");
        configuration.setString("table.exec.mini-batch.size","1000000");

        configuration.setString("job_name","#jobName");
        configuration.setString("dynamic.rules.job.id","#jobId");
        configuration.setString("execute.job.jdbc.url","#url");
        configuration.setString("execute.job.jdbc.username","#username");
        configuration.setString("execute.job.jdbc.password","#password");
        env.getConfig().setGlobalJobParameters(configuration);
        //user defined configs
        #jobConfigurationStr
        #sinkFunctions

        tEnv.execute("#jobName");

    }

    public void explain() throws Exception{
        StreamExecutionEnvironment env = StreamExecutionEnvironment.getExecutionEnvironment();
        EnvironmentSettings settings = EnvironmentSettings.newInstance().inStreamingMode().useBlinkPlanner().build();
        TableEnvironmentImpl tEnv = (TableEnvironmentImpl)StreamTableEnvironment.create(env, settings);
        tEnv.executeSql("CREATE FUNCTION if not exists SetConvertSingle AS 'com.alibaba.blink.udx.SetConvertSingle'");
        tEnv.executeSql("CREATE FUNCTION if not exists JsonValue AS 'com.alibaba.blink.udx.udf.JsonValue'");
        tEnv.executeSql("CREATE FUNCTION if not exists JsonStringUdf as 'com.alibaba.blink.udx.udf.JsonStringUdfV2'");
        List<Operation> operations = new ArrayList();
        #udfExpr
		initFunctions(tEnv);

        Configuration configuration =  tEnv.getConfig().getConfiguration();
        configuration.setString("table.exec.sink.not-null-enforcer","drop");
        #jobConfigurationStr
        #sinkFunctions
        System.out.println(tEnv.explainInternal(operations,ExplainDetail.JSON_EXECUTION_PLAN));
    }

    public void check() throws Exception{
    try{
		StreamExecutionEnvironment env = StreamExecutionEnvironment.getExecutionEnvironment();
		EnvironmentSettings settings = EnvironmentSettings.newInstance().inStreamingMode().useBlinkPlanner().build();
		CustomTableEnvironmentImpl tEnv = (CustomTableEnvironmentImpl) CustomTableEnvironment.create( settings);
        tEnv.executeSql("CREATE FUNCTION if not exists SetConvertSingle AS 'com.alibaba.blink.udx.SetConvertSingle'");
        tEnv.executeSql("CREATE FUNCTION if not exists JsonValue AS 'com.alibaba.blink.udx.udf.JsonValue'");
        tEnv.executeSql("CREATE FUNCTION if not exists JsonStringUdf as 'com.alibaba.blink.udx.udf.JsonStringUdfV2'");

        #udfExpr
		initFunctions(tEnv);

        Configuration configuration =  tEnv.getConfig().getConfiguration();
        // default configs
        configuration.setString("table.exec.sink.not-null-enforcer","drop");
        configuration.setString("table.exec.mini-batch.allow-latency","10000");
        configuration.setString("table.exec.mini-batch.enabled","true");
        configuration.setString("table.exec.mini-batch.size","1000000");
        configuration.setString("table.exec.validate.real-connect","true");
        configuration.setString("job_name","#jobName");
        configuration.setString("job_id","#jobId");
        configuration.setString("execute.job.jdbc.url","#url");
        configuration.setString("execute.job.jdbc.username","#username");
        configuration.setString("execute.job.jdbc.password","#password");
        env.getConfig().setGlobalJobParameters(configuration);
        //user defined configs
        #jobConfigurationStr
        #sinkFunctions
		tEnv.getJobGraph("#jobName");
				}catch (Exception e){
        			e.printStackTrace();throw e;
        		}
    }

	private String generateBinaryViewStr(Table inputTable, String pk, String connectorType, boolean isOriginalSourceSink){
		String selectExpr;
		if(isOriginalSourceSink){
			selectExpr = "cast(coalesce(cast(`messageKey`as varchar),'null') as VARBINARY) as messageKey, cast(message as VARBINARY) as `message`";
		}else{
			List<Column> columnList = inputTable.getResolvedSchema().getColumns();
			selectExpr = "cast(coalesce(cast(`" + pk + "`as varchar),'null') as VARBINARY) as messageKey, cast(jsonStringUdf(";
			for (Column column:columnList){
				selectExpr += "'"+column.getName()+"',`"+column.getName()+"`,";
			}
			selectExpr = selectExpr.substring(0,selectExpr.length()-1);
			selectExpr += ") AS VARBINARY)  as `message`";
		}
		if(connectorType.equals("dmq")){
			selectExpr = selectExpr.replace("VARBINARY","VARCHAR");
		}
		return selectExpr;
	}
	private String generateBinaryViewStr(Table inputTable, String pk, String col, String connectorType, boolean isOriginalSourceSink){
		if(StringUtil.isNullOrEmpty(col)){
			return generateBinaryViewStr(inputTable,pk,connectorType,isOriginalSourceSink);
		}
		List<Column> columnList = inputTable.getResolvedSchema().getColumns();
		String selectExpr = "cast(coalesce(cast(`" + pk + "`as varchar),'null') as VARBINARY) as messageKey, cast(`" + col + "` as VARBINARY) as message";
		if(connectorType.equals("dmq")){
			selectExpr = selectExpr.replace("VARBINARY","VARCHAR");
		}
		return selectExpr;
	}

    private String generateColumnStr(Table inputTable){
        List<Column> columnList = inputTable.getResolvedSchema().getColumns();
        String columnStr = "";
        for (Column column:columnList){
            columnStr += "`"+column.getName()+"`"+column.getDataType().getLogicalType().toString().replace("*PROCTIME*","").replace("*ROWTIME*","")+",";
        }
        return columnStr.substring(0,columnStr.length()-1);
    }

    private String generateBinaryColumnStr(String connectorType){
        if("kafka".equals(connectorType)){
            return "messageKey varbinary, message varbinary";
        } else if("dmq".equals(connectorType)){
            return "messageKey varchar, message varchar";
        } else{
            return "messageKey varbinary, message varbinary";
        }
    }

     private FieldsDataType getDataType(DataTypes.Field... fields){
        List logicalFields = new ArrayList();
        List fieldDataTypes = new ArrayList();
        for (DataTypes.Field field : fields) {
            logicalFields.add( new RowType.RowField(
                    field.getName(),
                    field.getDataType().getLogicalType(),
                    ""));
            fieldDataTypes.add(field.getDataType());
        }
       return new FieldsDataType(new RowType(logicalFields), fieldDataTypes);
   }

	private String generateColumnStr(Table inputTable, String cf, String primaryKeyStr){
		if(StringUtil.isNullOrEmpty(cf)){
			return generateColumnStr(inputTable);
		}
		List<Column> columnList = inputTable.getResolvedSchema().getColumns();
		String columnStr = "";
		for (Column column:columnList){
			if (!primaryKeyStr.equals(column.getName())) {
				columnStr += "`" + column.getName() + "` " + column.getDataType().getLogicalType().toString().replace("*PROCTIME*", "").replace("*ROWTIME*","") + ",";
			}
		}
		return StringUtil.isNullOrEmpty(columnStr.toString()) ? "" : wrapWithBackQuote(primaryKeyStr) + " STRING," + wrapWithBackQuote(cf) + " ROW <" + columnStr.substring(0, columnStr.length() - 1) + ">";
	}

	public String wrapWithBackQuote(String str) {
		if (!StringUtil.isNullOrEmpty(str)) {
			String[] subStrs = str.split(",");
			List formattedStrs = new ArrayList();
			for(String subStr : subStrs){
				formattedStrs.add(wrapWithSimpleBackQuote(subStr.trim()));
			}
			return String.join(",", formattedStrs);
		}
		return str;
	}

	public String wrapWithSimpleBackQuote(String str) {
		return "`".concat(str.trim().replace("`", "")).concat("`");
	}

	private String generateExtLastSelectStr(Table inputTable, String cf, String primaryKeyStr, String lastSelect) {
		String[] pks = primaryKeyStr.split(",");
		List<String> pkList = new ArrayList();
		for(String pk : pks){
			pkList.add(pk);
		}
		if (StringUtil.isNullOrEmpty(cf)) {
			List<Column> columnList = inputTable.getResolvedSchema().getColumns();
			StringBuilder selectStr = new StringBuilder("select ");
			for (Column column : columnList) {
				selectStr.append("`").append(column.getName()).append("`,");
			}
			return selectStr.deleteCharAt(selectStr.length() - 1) + " from (" + lastSelect + ")";
		}else {
			List<Column> columnList = inputTable.getResolvedSchema().getColumns();
			StringBuilder selectStr = new StringBuilder("select " + primaryKeyStr + ", row(");
			for (Column column : columnList) {
				if (!pkList.contains(column.getName())) {
					selectStr.append("`").append(column.getName()).append("`,");
				}
			}
			return selectStr.deleteCharAt(selectStr.length() - 1) + ") from " + lastSelect;
		}
	}

	private String generateExtLastDedupSelectExpr(Table inputTable, String primaryKeyStr) {
		List<Column> columnList = inputTable.getResolvedSchema().getColumns();
		StringBuilder selectStr = new StringBuilder(" ");
		String[] pks = primaryKeyStr.split(",");
		List<String> pkList = new ArrayList();
		for(String pk : pks){
			pkList.add(pk);
		}
		for (Column column : columnList) {
			String col = wrapWithBackQuote(column.getName());
			if (column.getDataType().getLogicalType() instanceof TimestampType
			|| column.getDataType().getLogicalType() instanceof LocalZonedTimestampType
			|| column.getDataType().getLogicalType() instanceof ZonedTimestampType) {
				if (!pkList.contains(column.getName())) {
					selectStr.append("dewuLastValue(cast(").append(col).append(" as varchar)) as ").append(col).append(",");
				} else {
					selectStr.append("cast(").append(col).append(" as varchar) as ").append(col).append(",");
				}
			} else {
				if (!pkList.contains(column.getName())) {
					selectStr.append("dewuLastValue(").append(col).append(") as ").append(col).append(",");
				} else {
					selectStr.append(col).append(",");
				}
			}
		}
		return selectStr.deleteCharAt(selectStr.length() - 1).toString();
	}

	private String generateExtLastDedupParserSelectExpr(Table inputTable) {
		List<Column> columnList = inputTable.getResolvedSchema().getColumns();
		StringBuilder selectStr = new StringBuilder(" ");
		for (Column column : columnList) {
			String col = wrapWithBackQuote(column.getName());
			if (column.getDataType().getLogicalType() instanceof TimestampType
				|| column.getDataType().getLogicalType() instanceof LocalZonedTimestampType
				|| column.getDataType().getLogicalType() instanceof ZonedTimestampType) {
				selectStr.append(" to_timestamp(").append(col).append(") as ").append(col).append(",");
			} else {
				selectStr.append(col).append(",");
			}
		}
		return selectStr.deleteCharAt(selectStr.length() - 1).toString();
	}


    public Long relative2AbsoluteTs(String relativeTs){
    		String[] arr = relativeTs.split("-");
    		String unit = arr[0].trim().toUpperCase();
    		Integer gap = arr.length == 1 ? 0 : Integer.parseInt(arr[1].trim());
    		Long currentTs = System.currentTimeMillis();
    		switch (unit){
    			case "DAY":
    				return currentTs - currentTs % (1000 * 3600 * 24 ) - gap * (1000 * 3600 * 24) - (1000 * 3600 * 8);
    			case "HOUR":
    				return currentTs - currentTs % (1000 * 3600) - gap * (1000 * 3600);
    			case "MINUTE":
    				return currentTs - currentTs % (1000 * 60) - gap * (1000 * 60);
    			case "SECOND":
    				return currentTs - currentTs % 1000 - gap * 1000 ;
    			default:
    				throw new IllegalArgumentException("Unsupport relativeTs:"+relativeTs);

    		}
    	}
#transformationFunction



}

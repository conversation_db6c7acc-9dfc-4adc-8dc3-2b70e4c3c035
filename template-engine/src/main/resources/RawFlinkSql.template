    public Table #transformFunction(TableEnvironmentImpl tEnv) {
        if(registeredTable.containsKey("#transformFunction")){ return (Table)registeredTable.get("#transformFunction");}
        #tableReg
        #sqlQuery

        if(!StringUtil.isNullOrEmpty("#lastView")){
            retTable = tEnv.from("#lastView");
        }
        registeredTable.put("#transformFunction",retTable);
        return retTable;
    }

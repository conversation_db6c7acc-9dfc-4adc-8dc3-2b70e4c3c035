    public Table #transformFunction(TableEnvironmentImpl tEnv) {
        if(registeredTable.containsKey("#transformFunction")){ return (Table)registeredTable.get("#transformFunction");}
        Table leftTable = #leftFunction(tEnv);
        tEnv.createTemporaryView("#leftView",leftTable);
        tEnv.executeSql("CREATE TABLE `dim_time_source`(ts BIGINT) WITH (#config)");
        tEnv.executeSql("CREATE VIEW dim_time_view AS SELECT date_format (from_unixtime(`ts`),'yyyy-MM-dd HH:mm') as `min`,row_number() over(partition by date_format (from_unixtime(`ts`),'yyyy-MM-dd HH:mm') order by proctime())  as `rn` from dim_time_source");
        Table retTable = tEnv.sqlQuery("SELECT #leftAlias.* FROM (select *,date_format (TIMESTAMPADD(MINUTE,#delayMinutes,localtimestamp) ,'yyyy-MM-dd HH:mm') as `min` from #leftView) as #leftAlias join dim_time_view as #rightAlias ON #leftAlias.`min` = #rightAlias.`min`");
        registeredTable.put("#transformFunction",retTable);
        return retTable;
    }

    public DataDrillSink #transformFunction(TableEnvironmentImpl tEnv) throws Exception{
        Table inputTable = #inputFunction(tEnv);
        if(null!=inputTable){
            if(#existsExtSql){
                tEnv.createTemporaryView("#extSqlView",inputTable);
                inputTable = tEnv.sqlQuery("#extSql");
            }
        }else{
            if(#existsExtSql){
                inputTable = tEnv.sqlQuery("#extSql");
            }
        }
        tEnv.createTemporaryView("#viewName",inputTable);
        String primaryKey;
		List collect = new ArrayList();
        if(#isRawMode){
			for (Column col : inputTable.getResolvedSchema().getColumns()) {
				collect.add(" cast (`" + col.getName() + "` as varchar) as `" +  col.getName() + "`");
			}
		    primaryKey =  "messageKey";
		}else{
			for (Column col : inputTable.getResolvedSchema().getColumns()) {
				collect.add("`" + col.getName() + "`");
			}
            String allFields = String.join(",",collect);
            String primaryKeyStr = "#primaryKeyStr";
            primaryKey =  "#primaryKey";
		}

        String sinkType = "#sinkType";
        String tableName = "#sinkUniqueName";
		List<String> columnNames = inputTable.getResolvedSchema().getColumnNames();
		List<String> wrappedCols = new ArrayList();
		for (String col : columnNames) {
			wrappedCols.add("cast(`" + col + "` as varchar) as `" + col + "`");
		}
        if(#isRawMode){
            String tmpSql = "select " + String.join(",", wrappedCols) + " from  #viewName";
            inputTable = tEnv.sqlQuery(tmpSql);
            columnNames = inputTable.getResolvedSchema().getColumnNames();
        }
        DataDrillSink sink = new DataDrillSink(columnNames,primaryKey,sinkType,Tuple2.of(tableName,"#transformFunction"));
        ((StreamTableEnvironmentImpl)tEnv).toRetractStream(inputTable, new RowTypeInfo(inputTable.getSchema().getFieldTypes(),(String[])columnNames.toArray(new String[0]))).addSink(sink);

        return sink;
    }

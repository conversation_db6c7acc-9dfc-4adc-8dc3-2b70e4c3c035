#配置数据源
spring:
  redis:
    host: kvredis-4f217ac66b.kvredis.shizhuang-inc.com
    port: 1022
    password: d0c4e5aeee3b28c5
    database: 0
    lettuce:
      pool:
        max-active: 32
        max-wait: 300ms
        max-idle: 16
        min-idle: 8
  datasource:
    druid:
      db-type: com.alibaba.druid.pool.DruidDataSource
      driverClassName: com.mysql.cj.jdbc.Driver
      url: *****************************************************************************************************************************************************************************************
      username: dw_realtime_property_platform
      password: TGKF8vUd64tdvIQb
      # 初始连接数
      initial-size: 5
      # 最小连接数
      min-idle: 15
      # 最大连接数
      max-active: 30
      # 获取连接超时时间
      max-wait: 5000
      # 连接有效性检测时间
      time-between-eviction-runs-millis: 60000
      # 连接在池中最小生存的时间
      min-evictable-idle-time-millis: 300000
      # 连接在池中最大生存的时间
      max-evictable-idle-time-millis: 900000
      # 指明连接是否被空闲连接回收器(如果有)进行检验.如果检测失败,则连接将被从池中去除
      test-while-idle: true
      # 指明是否在从池中取出连接前进行检验,如果检验失败, 则从池中去除连接并尝试取出另一个
      test-on-borrow: true
      # 是否在归还到池中前进行检验
      test-on-return: false
      # 检测连接是否有效
      validation-query: select 1
      # 配置监控统计
      webStatFilter:
        enabled: true
      stat-view-servlet:
        allow:
        enabled: true
        # 控制台管理用户名和密码
        url-pattern: /druid/*
        reset-enable: false
        login-username: admin
        login-password: 123456
      filter:
        stat:
          enabled: true
          # 记录慢SQL
          log-slow-sql: true
          slow-sql-millis: 1000
          merge-sql: true
        wall:
          config:
            multi-statement-allow: true

# 登录相关配置
login:
  #  是否限制单用户登录
  single-login: false
  # Redis用户登录缓存配置
  user-cache:
    # 存活时间/秒
    idle-time: 7200
  #  验证码
  login-code:
    #  验证码类型配置 查看 LoginProperties 类
    code-type: arithmetic
    #  登录图形验证码有效时间/分钟
    expiration: 2
    #  验证码高度
    width: 111
    #  验证码宽度
    height: 36
    # 内容长度
    length: 2
    # 字体名称，为空则使用默认字体，如遇到线上乱码，设置其他字体即可
    font-name:
    # 字体大小
    font-size: 25

#jwt
jwt:
  header: Authorization
  # 令牌前缀
  token-start-with: Bearer
  # 必须使用最少88位的Base64对该令牌进行编码
  base64-secret: ZmQ0ZGI5NjQ0MDQwY2I4MjMxY2Y3ZmI3MjdhN2ZmMjNhODViOTg1ZGE0NTBjMGM4NDA5NzYxMjdjOWMwYWRmZTBlZjlhNGY3ZTg4Y2U3YTE1ODVkZDU5Y2Y3OGYwZWE1NzUzNWQ2YjFjZDc0NGMxZWU2MmQ3MjY1NzJmNTE0MzI=
  # 令牌过期时间 此处单位/毫秒 ，默认2小时，可在此网站生成 https://www.convertworld.com/zh-hans/time/milliseconds.html
  token-validity-in-seconds: 7200000
  # 在线用户key
  online-key: online-token-
  # 验证码
  code-key: code-key-
  # token 续期检查时间范围（默认30分钟，单位默认毫秒），在token即将过期的一段时间内用户操作了，则给用户的token续期
  detect: 1800000
  # 续期时间范围，默认 1小时，这里单位毫秒
  renew: 3600000

# IP 本地解析
ip:
  local-parsing: true

#是否允许生成代码，生产环境设置为false
generator:
  enabled: false

#如果生产环境要开启swagger，需要配置请求地址
#springfox:
#  documentation:
#    swagger:
#      v2:
#        host: # 接口域名或外网ip

#是否开启 swagger-ui
swagger:
  enabled: true

# 文件存储路径
file:
  mac:
    path: ~/file/
    avatar: ~/avatar/
  linux:
    path: /home/<USER>/file/
    avatar: /home/<USER>/avatar/
  windows:
    path: C:\eladmin\file\
    avatar: C:\eladmin\avatar\
  # 文件大小 /M
  maxSize: 100
  avatarMaxSize: 5

management:
  endpoints:
    web:
      exposure:
        include: "prometheus,health,metrics,info,sentinel"

dw:
  realtime-platform:
    appId: realtime_property_platform
    appSecret: 956d7454b2ca427bb88fed8d85a6c127
    baseUrl: https://libra-k8s.shizhuang-inc.com/libra/web
    webUrl: http://bigdata-flink-job.shizhuang-inc.com/
  feishu-bot:
    appId: cli_a2e644668639900b
    appSecret: NMN9wXzKr5JA6oG9NAMhlfzIJ8eGlDpG
    baseUrl: https://lark-center-o.shizhuang-inc.com/open-apis
    env: csprd
  poizon-metrics:
    accessId: algorithm
    accessKey: 71e9358348b2d5747be2306f1659ab43
    url: http://apm-metrics-center-api.shizhuang-inc.com/
  kafka-manager:
    baseUrl: http://ms3.dewu.com
    token: DMQ 5T4oJMupTMbxoHzHLJ7SvRrrwSNLJgVi
    instanceId: INSTANCE_1638164205708_AB8D11C
  odps-datasource-id: 35

db:
  url: *****************************************************************************************************************************************************************************************
  user: dw_realtime_property_platform
  password: TGKF8vUd64tdvIQb


request:
  addmodel: https://ms3.dewu.com/api/v1/h5/olap/metadata/addModel
  updatemodel: https://ms3.dewu.com/api/v1/h5/olap/metadata/updateModel
  searchmodel: https://ms3.dewu.com/api/v1/h5/olap/metadata/searchModels?engineType=CLICKHOUSE&&name=
  startmodel: https://ms3.dewu.com/api/v1/h5/olap/metadata/updateModelStatus
  addmetrics: https://ms3.dewu.com/api/v1/h5/compass/indicators/add
  searchmetrics: https://ms3.dewu.com/api/v1/h5/compass/indicators/list
  startmetrics: https://ms3.dewu.com/api/v1/h5/compass/indicators/status/update
  database: olap_xlgc
  datasource_name: ck_default_bp11xxl475ui839o5
  add16: https://stream-datalineage-16.shizhuang-inc.com/api/quartz/service/add/16
  update16: https://stream-datalineage-16.shizhuang-inc.com/api/quartz/service/update/16
  updatestate16: https://stream-datalineage-16.shizhuang-inc.com/api/quartz/service/update/16/state
  exec16: https://stream-datalineage-16.shizhuang-inc.com/api/quartz/service/exec/16
  delete16: https://stream-datalineage-16.shizhuang-inc.com/api/quartz/service/delete/16
  add13: https://stream-datalineage-13.shizhuang-inc.com/api/quartz/service/add/13
  update13: https://stream-datalineage-13.shizhuang-inc.com/api/quartz/service/update/13
  updatestate13: https://stream-datalineage-13.shizhuang-inc.com/api/quartz/service/update/13/state
  exec13: https://stream-datalineage-13.shizhuang-inc.com/api/quartz/service/exec/13
  delete13: https://stream-datalineage-13.shizhuang-inc.com/api/quartz/service/delete/13


monitorReceiver: 实时模板平台作业告警群
monitorReceiverOffline: 离线数仓binlog2odps模版任务告警群

kafka:
  ip: ************:9092,************:9092,************:9092,***********:9092,*************:9092,*************:9092
  job_status_sync_topic: ods_metric_platform_realtime_index_module_job_status_sync
  job_status_monitor_topic: ods_metric_platform_realtime_index_module_job_status_monitor
  alarm:
    ip: jgs-stability-kafka.shizhuang-inc.com:10004
    topic: alert_realtime-platfrom-dcheck

flink:
  projectName: tech-data-template
  jarId: 1267
  parentFolderId: 206
  engineName: flink-1.13-v0.6
  resourceName: k8s-flink-bigdata

libra:
  binlog:
    task_info:
      ip: binlog-kafka-bigdata.shizhuang-inc.com:10025
      topic: ods_dw_libra_task_info
      group_id: rpp_libra_task_info_new
      time_out: 10000

dqc:
  warn:
    kafka:
      bootstrap_server: rt-kafka-bigdata-ads.shizhuang-inc.com:9092
      topic: REALTIME_DQC_EVENT_SINK
      time_out: 10000
      interval: 600000

realtime:
  data_wharehouse:
    cost:
      bootstrap_server: rt-kafka-bigdata-ods.shizhuang-inc.com:10001
      topic: ods_realtime_data_wharehouse_cost
      metric_topic: ods_dewu_datawarehouse_metrics

chaos:
  send:
    kafka:
      bootstrap_server: rt-kafka-bigdata-ads.shizhuang-inc.com:9092
      topic: FLK_TE_CHAOS_DATA_BUS

extra:
  extend:
    job:
      label:
        trade:
          table_id: 2418
        user:
          table_id: 2547

dts:
  manager:
    url:
      base: https://ms3.dewu.com
      prefix: /api/v1/internal/dts-manager
    url_sgp:
      base: https://ms3-plus.poizonapp.com
      prefix: /api/v1/internal/dts-manager
    appId: TEMPLATE_ENGINE
    appSecret: DC38F5729A1E440886BBCDA6CF6C7485
    targetId: 10
    alarmId: 2
    domainId: 26
    libraDept: 45
    targetIdSgp: 5
    alarmIdSgp: 3
    domainIdSgp: 26
pipeline:
  metrics:
    sr:
      url: *******************************************
      username: dw_bigdata_realtime
      password: jh8S9sjh8iu92gyYG9
      database: flink_monitor
      table: metric_table
    hbase:
      zookeeperQuorum: al-hzi-csprd-hbase-flink-ssd-c1-hmaster-01.shizhuang-inc.com:2181,al-hzi-csprd-hbase-flink-ssd-c1-hmaster-02.shizhuang-inc.com:2181,al-hzi-csprd-hbase-flink-ssd-c1-hmaster-03.shizhuang-inc.com:2181
      username: root
      password: root
      table: dim:rt_lineage_job_info
    kafka:
      bootstrap_server: rt-kafka-bigdata-ods.shizhuang-inc.com:10001
      topic: lineage_metrics_topic
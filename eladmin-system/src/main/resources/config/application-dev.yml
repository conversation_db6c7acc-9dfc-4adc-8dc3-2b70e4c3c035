#配置数据源
spring:
  redis:
    host: r-uf6w4jeitf13wkzgj8.redis.rds.aliyuncs.com
    port: 6379
    password: _3lM)sue)ijdMW!
    database: 230
    lettuce:
      pool:
        max-active: 32
        max-wait: 300ms
        max-idle: 16
        min-idle: 8
  datasource:
    druid:
      db-type: com.alibaba.druid.pool.DruidDataSource
      driverClassName: net.sf.log4jdbc.sql.jdbcapi.DriverSpy
      url: jdbc:log4jdbc:mysql://${DB_HOST:rm-uf6xriti4mg9309j8.mysql.rds.aliyuncs.com}:${DB_PORT:3306}/${DB_NAME:dw_realtime_property_platform}?serverTimezone=Asia/Shanghai&characterEncoding=utf8&useSSL=false&allowPublicKeyRetrieval=true
      username: ${DB_USER:dw_realtime_property_platform}
      password: ${DB_PWD:ZS5AoXs7lduYbnXN}
      # 初始连接数
      initial-size: 5
      # 最小连接数
      min-idle: 15
      # 最大连接数
      max-active: 30
      # 超时时间(以秒数为单位)
      remove-abandoned-timeout: 180
      # 获取连接超时时间
      max-wait: 3000
      # 连接有效性检测时间
      time-between-eviction-runs-millis: 60000
      # 连接在池中最小生存的时间
      min-evictable-idle-time-millis: 300000
      # 连接在池中最大生存的时间
      max-evictable-idle-time-millis: 900000
      # 指明连接是否被空闲连接回收器(如果有)进行检验.如果检测失败,则连接将被从池中去除
      test-while-idle: true
      # 指明是否在从池中取出连接前进行检验,如果检验失败, 则从池中去除连接并尝试取出另一个
      test-on-borrow: true
      # 是否在归还到池中前进行检验
      test-on-return: false
      # 检测连接是否有效
      validation-query: select 1
      # 配置监控统计
      webStatFilter:
        enabled: true
      stat-view-servlet:
        enabled: true
        url-pattern: /druid/*
        reset-enable: false
      filter:
        stat:
          enabled: true
          # 记录慢SQL
          log-slow-sql: true
          slow-sql-millis: 1000
          merge-sql: true
        wall:
          config:
            multi-statement-allow: true

# 登录相关配置
login:
  #  是否限制单用户登录
  single-login: false
  # Redis用户登录缓存配置
  user-cache:
    # 存活时间/秒
    idle-time: 7200
  #  验证码
  login-code:
    #  验证码类型配置 查看 LoginProperties 类
    code-type: arithmetic
    #  登录图形验证码有效时间/分钟
    expiration: 2
    #  验证码高度
    width: 111
    #  验证码宽度
    height: 36
    # 内容长度
    length: 2
    # 字体名称，为空则使用默认字体
    font-name:
    # 字体大小
    font-size: 25

#jwt
jwt:
  header: Authorization
  # 令牌前缀
  token-start-with: Bearer
  # 必须使用最少88位的Base64对该令牌进行编码
  base64-secret: ZmQ0ZGI5NjQ0MDQwY2I4MjMxY2Y3ZmI3MjdhN2ZmMjNhODViOTg1ZGE0NTBjMGM4NDA5NzYxMjdjOWMwYWRmZTBlZjlhNGY3ZTg4Y2U3YTE1ODVkZDU5Y2Y3OGYwZWE1NzUzNWQ2YjFjZDc0NGMxZWU2MmQ3MjY1NzJmNTE0MzI=
  # 令牌过期时间 此处单位/毫秒 ，默认4小时，可在此网站生成 https://www.convertworld.com/zh-hans/time/milliseconds.html
  token-validity-in-seconds: 14400000
  # 在线用户key
  online-key: online-token-
  # 验证码
  code-key: code-key-
  # token 续期检查时间范围（默认30分钟，单位毫秒），在token即将过期的一段时间内用户操作了，则给用户的token续期
  detect: 1800000
  # 续期时间范围，默认1小时，单位毫秒
  renew: 3600000

#是否允许生成代码，生产环境设置为false
generator:
  enabled: true

#是否开启 swagger-ui
swagger:
  enabled: true

# IP 本地解析
ip:
  local-parsing: true

# 文件存储路径
file:
  mac:
    path: ~/file/
    avatar: ~/avatar/
  linux:
    path: /home/<USER>/file/
    avatar: /home/<USER>/avatar/
  windows:
    path: C:\eladmin\file\
    avatar: C:\eladmin\avatar\
  # 文件大小 /M
  maxSize: 100
  avatarMaxSize: 5


management:
  endpoints:
    web:
      exposure:
        include: "prometheus,health,metrics,info,sentinel"

dw:
  realtime-platform:
    appId: realtime_property_platform
    appSecret: 956d7454b2ca427bb88fed8d85a6c127
    baseUrl: http://t1-bigdata-libra-k8s.shizhuang-inc.net/libra/web
    webUrl: http://test-bigdata-flink.shizhuang-inc.net/
  feishu-bot:
    appId: cli_a3a1611b70b9900c
    appSecret: bdI5keas4EFymFDpDUttMbCAKq3eIbXp
    baseUrl: https://t1-lark-center-o.shizhuang-inc.net/open-apis
    env: t1
  poizon-metrics:
    accessId: algorithm
    accessKey: 71e9358348b2d5747be2306f1659ab43
    url: http://t1-apm-metrics-center.shizhuang-inc.net/
  kafka-manager:
    baseUrl: http://t1-ms3.dewu.net
    token: DMQ KkiJ84DTLigw9S2FW84EiknLUaREDBCy
    instanceId: INSTANCE_1638430050348_8006166
  odps-datasource-id: 16

db:
  url: *****************************************************************************************************************************************************************************************
  user: dw_realtime_property_platform
  password: ZS5AoXs7lduYbnXN

request:
  addmodel: https://t1-ms3.dewu.com/api/v1/h5/olap/metadata/addModel
  updatemodel: https://t1-ms3.dewu.com/api/v1/h5/olap/metadata/updateModel
  searchmodel: https://t1-ms3.dewu.com/api/v1/h5/olap/metadata/searchModels?engineType=CLICKHOUSE&&name=
  startmodel: https://t1-ms3.dewu.com/api/v1/h5/olap/metadata/updateModelStatus
  addmetrics: https://t1-ms3.dewu.com/api/v1/h5/compass/indicators/add
  searchmetrics: https://t1-ms3.dewu.com/api/v1/h5/compass/indicators/list
  startmetrics: https://t1-ms3.dewu.com/api/v1/h5/compass/indicators/status/update
  database: olap_znyy
  datasource_name: ck-cluster-default
  add16: http://localhost:9000/api/quartz/service/add/16
  update16: http://localhost:9000/api/quartz/service/update/16
  updatestate16: http://localhost:9000/api/quartz/service/update/16/state
  exec16: http://localhost:9000/api/quartz/service/exec/16
  delete16: http://localhost:9000/api/quartz/service/delete/16
  add13: http://localhost:9000/api/quartz/service/add/13
  update13: http://localhost:9000/api/quartz/service/update/13
  updatestate13: http://localhost:9000/api/quartz/service/update/13/state
  exec13: http://localhost:9000/api/quartz/service/exec/13
  delete13: http://localhost:9000/api/quartz/service/delete/13

monitorReceiver: 实时模板平台测试作业告警群
monitorReceiverOffline: 实时模板平台测试作业告警群

kafka:
  ip: t0-kafka.shizhuang-inc.net:18100
  #  job_status_sync_topic: metric_platform_realtime_index_module_job_status_sync
  #  job_status_monitor_topic: ods_metric_platform_realtime_index_module_job_status_monitor
  job_status_sync_topic: metric_platform_realtime_index_module_job_status_sync_pre
  job_status_monitor_topic: ods_metric_platform_realtime_index_module_job_status_monitor_pre
  alarm:
    ip: t0-kafka.shizhuang-inc.net:18100
    topic: realtime_platform_quartz_alarm



flink:
  projectName: tech-data-template
  jarId: 744
  parentFolderId: 1
  engineName: flink-1.13
  resourceName: k8s-cluster
log:
  path: logs
  debug: false

libra:
  binlog:
    task_info:
      ip: t0-kafka.shizhuang-inc.net:18100
      topic: ods_dw_libra_task_info
      group_id: dev_rpp_libra_task_info
      time_out: 30000

dqc:
  warn:
    kafka:
      bootstrap_server: t0-kafka.shizhuang-inc.net:18100
      topic: dqc_warn
      time_out: 30000
      interval: 600000

realtime:
  data_wharehouse:
    cost:
      bootstrap_server: t0-kafka.shizhuang-inc.net:18100
      topic: ods_realtime_data_wharehouse_cost
      metric_topic: ods_dewu_datawarehouse_metrics

chaos:
  send:
    kafka:
      bootstrap_server: t0-kafka.shizhuang-inc.net:18100
      topic: FLK_TE_CHAOS_DATA_BUS

extra:
  extend:
    job:
      label:
        trade:
          table_id: 7318
        user:
          table_id: 12824
dts:
  manager:
    url:
      base: https://t1-ms3.dewu.net
      prefix: /api/v1/internal/dts-manager
    url_sgp:
      base: https://t1-ms3.dewu.net
      prefix: /api/v1/internal/dts-manager
    appId: TEMPLATE_ENGINE
    appSecret: DC38F5729A1E440886BBCDA6CF6C7485
    targetId: 24
    alarmId: 6
    domainId: 26
    libraDept: 45
    targetIdSgp: 24
    alarmIdSgp: 6
    domainIdSgp: 26
pipeline:
  metrics:
    sr:
      url: *******************************************
      username: dw_bigdata_realtime
      password: jh8S9sjh8iu92gyYG9
      database: flink_monitor
      table: metric_table
    hbase:
      zookeeperQuorum: dw-t1-ops-hadoop-test-011.shizhuang-inc.net:2181,dw-t1-ops-hadoop-test-012.shizhuang-inc.net:2181,dw-t1-ops-hadoop-test-013.shizhuang-inc.net:2181
      username: root
      password: root
      table: DIM:rt_lineage_job_info
    kafka:
      bootstrap_server: t0-kafka.shizhuang-inc.net:18100
      topic: lineage_metrics_topic
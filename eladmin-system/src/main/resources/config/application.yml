server:
  port: 8000
  max-http-header-size: 32768

spring:
  freemarker:
    check-template-location: false
  profiles:
    active: dev
  jackson:
    time-zone: GMT+8
  data:
    redis:
      repositories:
        enabled: false
#  pid:
#    file: /自行指定位置/eladmin.pid
  #配置 Jpa
  jpa:
    hibernate:
      ddl-auto: none
    show-sql: false #打印执行的sql语句，false则不打印sql
    open-in-view: true
    properties:
      hibernate:
        dialect: org.hibernate.dialect.MySQL5InnoDBDialect
  #mybatis
  mybatis:
    #mapper xml扫描目录
    mapperLocations: classpath:mapper/**.xml
    executorType: SIMPLE
    configuration:
      # 驼峰式
      map-underscore-to-camel-case: true
      #日志打印
#      log-impl: org.apache.ibatis.logging.stdout.StdOutImpl

task:
  pool:
    # 核心线程池大小
    core-pool-size: 10
    # 最大线程数
    max-pool-size: 20
    # 活跃时间
    keep-alive-seconds: 60
    # 队列容量
    queue-capacity: 50

#七牛云
qiniu:
  # 文件大小 /M
  max-size: 15

#邮箱验证码有效时间/秒
code:
  expiration: 300

#密码加密传输，前端公钥加密，后端私钥解密
rsa:
  private_key: MIIBUwIBADANBgkqhkiG9w0BAQEFAASCAT0wggE5AgEAAkEA0vfvyTdGJkdbHkB8mp0f3FE0GYP3AYPaJF7jUd1M0XxFSE2ceK3k2kw20YvQ09NJKk+OMjWQl9WitG9pB6tSCQIDAQABAkA2SimBrWC2/wvauBuYqjCFwLvYiRYqZKThUS3MZlebXJiLB+Ue/gUifAAKIg1avttUZsHBHrop4qfJCwAI0+YRAiEA+W3NK/RaXtnRqmoUUkb59zsZUBLpvZgQPfj1MhyHDz0CIQDYhsAhPJ3mgS64NbUZmGWuuNKp5coY2GIj/zYDMJp6vQIgUueLFXv/eZ1ekgz2Oi67MNCk5jeTF2BurZqNLR3MSmUCIFT3Q6uHMtsB9Eha4u7hS31tj1UWE+D+ADzp59MGnoftAiBeHT7gDMuqeJHPL4b+kC+gzV4FGTfhR9q3tTbklZkD2A==
log:
  path: /logs
  debug: false

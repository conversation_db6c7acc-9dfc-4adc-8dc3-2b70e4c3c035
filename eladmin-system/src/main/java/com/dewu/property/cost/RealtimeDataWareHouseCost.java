package com.dewu.property.cost;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.dewu.modules.quartz.libra.LibraURLUtils;
import com.dewu.modules.quartz.libra.PrometheusBean;
import com.dewu.modules.quartz.libra.QueryParam;
import com.dewu.modules.quartz.libra.QueryRangeParam;
import com.dewu.modules.quartz.task.PrometheusOperateComponent;
import com.dewu.property.bi.service.KeTopicService;
import com.dewu.property.bi.service.dto.KeTopicDto;
import com.dewu.property.bi.service.dto.KeTopicQueryCriteria;
import com.dewu.property.sql.leaf.domain.FlinkSqlLeafSceneAndBusinessLine;
import com.dewu.property.sql.leaf.service.FlinkSqlLeafSceneAndBusinessLineService;
import com.dewu.property.sql.leaf.service.dto.FlinkSqlLeafSceneAndBusinessLineDto;
import com.dewu.property.sql.leaf.service.dto.FlinkSqlLeafSceneAndBusinessLineQueryCriteria;
import com.dewu.property.sql.lineage.service.FlinkSqlLineageService;
import com.dewu.property.sql.lineage.service.dto.FlinkSqlLineageDto;
import com.dewu.property.sql.lineage.service.dto.FlinkSqlLineageQueryCriteria;
import com.dewu.utils.HttpClientUtil;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.google.common.base.Joiner;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.api.java.tuple.Tuple2;
import org.apache.flink.api.java.tuple.Tuple3;
import org.apache.flink.client.program.PackagedProgram;
import org.apache.flink.client.program.PackagedProgramUtils;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.runtime.jobgraph.JobGraph;
import org.apache.kafka.clients.producer.KafkaProducer;
import org.apache.kafka.clients.producer.Producer;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;


@Component
@RequiredArgsConstructor
@Slf4j
public class RealtimeDataWareHouseCost {

	@Value("${realtime.data_wharehouse.cost.bootstrap_server}")
	private String bootstrapServer;


	@Value("${realtime.data_wharehouse.cost.topic}")
	private String topic;

	@Value("${realtime.data_wharehouse.cost.metric_topic}")
	private String metricTopic;

	private final FlinkSqlLineageService flinkSqlLineageService;

	private final KeTopicService keTopicService;

	private final FlinkSqlLeafSceneAndBusinessLineService flinkSqlLeafSceneAndBusinessLineService;


	private static final Map<String,String> kafkaInstance = Maps.newHashMap();

	private static PrometheusOperateComponent component;

	private static ExecutorService executor;

	public static final Set<String> specialCpuProjectSet = Sets.newHashSet("tech-data-dw2",
			"dw-rt-2","du_algo_commu","search-mointor","tech-data-dw4","dw-risk-flink-pro","tech-obs");

	private static ArrayList<String>  pools = Lists.newArrayList("dw-rt-2",
				"tech-data-dw2-amd",
				"tech-data-dw2-amd-1-8");
	private static ArrayList<String>  clusters = Lists.newArrayList("k8s-prd-flink-hz-1",
				"k8s-prd-standard-flink-bigdata"
			);

	private static ArrayList<Tuple2<String, String>> clusterAndPool = Lists.newArrayList(
			Tuple2.of("k8s-prd-flink-hz-1", "bigdata-tech-data-dw2-amd"),
			Tuple2.of("k8s-prd-flink-hz-1", "bigdata-tech-data-dw2-amd-1-8"),
			Tuple2.of("k8s-prd-flink-hz-2", "bigdata-tech-data-dw2-amd"),
			Tuple2.of("k8s-prd-flink-hz-1", "bigdata-tech-data-dw2-amd8"),
			Tuple2.of("k8s-prd-flink-hz-2", "bigdata-tech-data-dw2-amd8")
	);

	static {
		kafkaInstance.put("dwd-du-sensors-log-prod-kafka-v2.shizhuang-inc.com:10067","dwd-du-sensors-log-prod-kafka-v2");
		kafkaInstance.put("dwd-du-sensors-log-prod-kafka.shizhuang-inc.com:10035","dwd-du-sensors-log-prod-kafka");
		kafkaInstance.put("bigdata-algo-ods.shizhuang-inc.com:10053","bigdata-algo-ods");
		kafkaInstance.put("binlog-kafka-bigdata","binlog-kafka-bigdata");
		kafkaInstance.put("rt-kafka-bigdata-ads","rt-kafka-bigdata-ads");
		kafkaInstance.put("rt-kafka-bigdata-dwd-v2","rt-kafka-bigdata-dwd-v2");
		kafkaInstance.put("ods-sensors-log-kafka-v2.shizhuang-inc.com:10066","ods-sensors-log-kafka-v2");
		kafkaInstance.put("ods-sensors-log-kafka.shizhuang-inc.com:10015","ods-sensors-log-kafka");
		kafkaInstance.put("rt-kafka-bigdata-ods","rt-kafka-bigdata-ods");
		kafkaInstance.put("dpp-log-ods-kafka.shizhuang-inc.com:10032","dpp-log-ods-kafka");
		kafkaInstance.put("rt-kafka-bigdata-ads-v2","rt-kafka-bigdata-ads-v2");

		component = new PrometheusOperateComponent(
				"apm-metrics-center-api.shizhuang-inc.com/default",
				"data-warehouse",
				"ea88d3da0e57494a7006a9439b39aeb3"
		);

		executor = Executors.newFixedThreadPool(4);

	}

	public static final String project_jobManager_job_up_time = "sum by(pod,job_name)\n" +
			"(flink_jobmanager_job_uptime{task_project=~\"%s\"})";

	// 专用CpuRequest
	public static final String taskManager_cpu_request_by_pod_pool_cluster = "max_over_time(sum( label_replace(kube_pod_container_resource_requests{cluster=~\"%s\", namespace=\"bigdata\",pod=~\".*taskmanager.*\",resource=\"cpu\"} , \"app_id\", \"$1\", \"pod\", \"flink-(.*)-(.*)-(.*)\") " +
			"+on(node) group_left(resource_pool) " +
			" label_replace(kubeone_rm_cpu_total{apm_k8s_name=~\"%s\", resource_pool=\"%s\"}, \"node\", \"$1\", \"node_name\", \"(.*)\")*0) " +
			" by (pod, cluster, resource_pool) [30m]) ";

	// 专用CpuRequest
	public static final String taskManager_avg_cpu_request_by_pod_pool_cluster = "avg_over_time(sum( label_replace(kube_pod_container_resource_requests{cluster=~\"%s\", namespace=\"bigdata\",pod=~\".*taskmanager.*\",resource=\"cpu\"} , \"app_id\", \"$1\", \"pod\", \"flink-(.*)-(.*)-(.*)\") " +
			"+on(node) group_left(resource_pool) " +
			" label_replace(kubeone_rm_cpu_total{apm_k8s_name=~\"%s\", resource_pool=\"%s\"}, \"node\", \"$1\", \"node_name\", \"(.*)\")*0) " +
			" by (pod, cluster, resource_pool) [10m]) ";

	public static final String taskManager_cpu_usage_by_node = "sum( irate(container_cpu_usage_seconds_total{cluster=\"%s\",container=\"flink-main-container\",container!=\"POD\",cpu=\"total\"}[2m]) + on(node) group_left(resource_pool) label_replace(kubeone_rm_cpu_total{apm_k8s_name=~\"%s\", resource_pool=\"%s\"}, \"node\", \"$1\", \"node_name\", \"(.*)\")*0) by (node)";

	// 专用CpuRequest
	public static final String taskManager_cpu_request_by_pod_pool_cluster_node = "max_over_time(sum( label_replace(kube_pod_container_resource_requests{cluster=~\"%s\", namespace=\"bigdata\",pod=~\".*taskmanager.*\",resource=\"cpu\"} , \"app_id\", \"$1\", \"pod\", \"flink-(.*)-(.*)-(.*)\") " +
			"+on(node) group_left(resource_pool) " +
			" label_replace(kubeone_rm_cpu_total{apm_k8s_name=~\"%s\", resource_pool=\"%s\"}, \"node\", \"$1\", \"node_name\", \"(.*)\")*0) " +
			" by (pod, cluster, resource_pool,node) [30m]) ";

	// 专用MEMRequest
	public static final String taskManager_memory_request_by_pod_pool_cluster = "max_over_time(sum( label_replace(kube_pod_container_resource_requests{cluster=~\"%s\", namespace=\"bigdata\",pod=~\".*taskmanager.*\",resource=\"memory\"} , \"app_id\", \"$1\", \"pod\", \"flink-(.*)-(.*)-(.*)\")/1024/1024/1024 " +
			"+on(node) group_left(resource_pool) " +
			" label_replace(kubeone_rm_cpu_total{apm_k8s_name=~\"%s\", resource_pool=\"%s\"}, \"node\", \"$1\", \"node_name\", \"(.*)\")*0) " +
			" by (pod, cluster, resource_pool) [30m]) ";

	// 专用MEMRequest
	public static final String taskManager_memory_request_by_pod_pool_cluster_node = "max_over_time(sum( label_replace(kube_pod_container_resource_requests{cluster=~\"%s\", namespace=\"bigdata\",pod=~\".*taskmanager.*\",resource=\"memory\"} , \"app_id\", \"$1\", \"pod\", \"flink-(.*)-(.*)-(.*)\")/1024/1024/1024 " +
			"+on(node) group_left(resource_pool) " +
			" label_replace(kubeone_rm_cpu_total{apm_k8s_name=~\"%s\", resource_pool=\"%s\"}, \"node\", \"$1\", \"node_name\", \"(.*)\")*0) " +
			" by (pod, cluster, resource_pool,node) [30m]) ";

	// 专用CpuUsage
	public static final String taskManager_cpu_usage_by_pod_pool_cluster = "sum(max_over_time(irate(container_cpu_usage_seconds_total{namespace=\"bigdata\", container=\"flink-main-container\",cluster=~\"%s\",pod=~\".*taskmanager.*\"})[30m]) +on(node) group_left(resource_pool) \n" +
			"\t\t\tlabel_replace(kubeone_rm_cpu_total{apm_k8s_name=~\"%s\",resource_pool=~\"%s\" }, \"node\", \"$1\", \"node_name\", \"(.*)\")*0)\n" +
			"by (pod, cluster, resource_pool)";

	// 专用CpuUsage
	public static final String taskManager_avg_cpu_usage_by_pod_pool_cluster = "sum(max_over_time(irate(container_cpu_usage_seconds_total{namespace=\"bigdata\", container=\"flink-main-container\",cluster=~\"%s\",pod=~\".*taskmanager.*\"})[1m]) +on(node) group_left(resource_pool) \n" +
			"\t\t\tlabel_replace(kubeone_rm_cpu_total{apm_k8s_name=~\"%s\",resource_pool=~\"%s\" }, \"node\", \"$1\", \"node_name\", \"(.*)\")*0)\n" +
			"by (pod, cluster, resource_pool)";

	// 专用CpuUsage
	public static final String taskManager_cpu_usage_by_pod_pool_cluster_node = "sum(max_over_time(irate(container_cpu_usage_seconds_total{namespace=\"bigdata\", container=\"flink-main-container\",cluster=~\"%s\",pod=~\".*taskmanager.*\"})[30m]) +on(node) group_left(resource_pool) \n" +
			"\t\t\tlabel_replace(kubeone_rm_cpu_total{apm_k8s_name=~\"%s\",resource_pool=~\"%s\" }, \"node\", \"$1\", \"node_name\", \"(.*)\")*0)\n" +
			"by (pod, cluster, resource_pool,node)";

	// 混部CPUrequest
	public static final String taskManager_reclaimed_cpu_request_by_pod_pool_cluster = "\t\tmax_over_time(sum(label_replace(kube_pod_container_resource_requests{namespace=~\"bigdata\",pod=~\"%s\",container=\"flink-main-container\",container !=\"\",resource=\"dewu_com_reclaimed_cpu\",container!=\"POD\",namespace=\"bigdata\"}/2 , \"app_id\", \"$1\", \"pod\", \"flink-(.*)-taskmanager-(.*)\")\n" +
			"\t\t\t+on(node) group_left(resource_pool) \n" +
			"\t\t\tlabel_replace(kubeone_rm_cpu_total, \"node\", \"$1\", \"node_name\", \"(.*)\")*0)by(pod, cluster, resource_pool) [30m])";

		// 混部MEM request
	public static final String taskManager_reclaimed_memory_request_by_pod_pool_cluster =  "\t\tmax_over_time(sum(label_replace(kube_pod_container_resource_requests{namespace=~\"bigdata\",pod=~\"%s\",container=\"flink-main-container\",container !=\"\",resource=\"dewu_com_reclaimed_mem\",container!=\"POD\",namespace=\"bigdata\"}/2 , \"app_id\", \"$1\", \"pod\", \"flink-(.*)-taskmanager-(.*)\")\n" +
			"\t\t\t+on(node) group_left(resource_pool) \n" +
			"\t\t\tlabel_replace(kubeone_rm_cpu_total, \"node\", \"$1\", \"node_name\", \"(.*)\")*0)by(pod, cluster, resource_pool) [30m])";


	// 混部CpuUsage
	public static final String taskManager_reclaimed_cpu_usage_by_pod_pool_cluster = "\t\tmax_over_time(sum(label_replace(irate(container_cpu_usage_seconds_total{namespace=~\"bigdata\",pod=~\"%s\",container=\"flink-main-container\",container !=\"\",container!=\"POD\",namespace=\"bigdata\"}), \"app_id\", \"$1\", \"pod\", \"flink-(.*)-taskmanager-(.*)\")\n" +
			"\t\t\t+on(node) group_left(resource_pool) \n" +
			"\t\t\tlabel_replace(kubeone_rm_cpu_total, \"node\", \"$1\", \"node_name\", \"(.*)\")*0)by(pod, cluster, resource_pool) [30m])";


	public static final String max_cpu_usage = "\t\t\tsum(max_over_time(sum(irate(container_cpu_usage_seconds_total{namespace=\"bigdata\", container=\"flink-main-container\",cluster=~\"k8s-prd-flink-hz-1|k8s-prd-standard-flink-bigdata|k8s-prd-flink-hz-2\"})+on(node) group_left(resource_pool) \n" +
			"\t\t\tlabel_replace(kubeone_rm_cpu_total{apm_k8s_name=~\"k8s-prd-flink-hz-1|k8s-prd-standard-flink-bigdata|k8s-prd-flink-hz-2\",resource_pool=~\"bigdata-tech-data-dw2-amd|bigdata-tech-data-dw2-amd-1-8|bigdata-dw-rt-2\"}, \"node\", \"$1\", \"node_name\", \"(.*)\")*0)  [30m]))";

	public final static DateTimeFormatter dayFormat = DateTimeFormatter.ofPattern("yyyy-MM-dd");
	public final static DateTimeFormatter minuteFormat = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
	public final static DateTimeFormatter monthFormat = DateTimeFormatter.ofPattern("yyyy-MM");
	public final static int stepSeconds = 120;


	/**
	 * 生成每日新的叶子节点
	 * @throws Exception
	 */
	public void scheduleLeafJob(String date) throws Exception{
		String nowBefore1day = "0".equals(date) ? LocalDate.now().plusDays(-1).format(dayFormat) : date;

			// 检索新的叶子节点
		Map<Tuple2<String, String>, Float> jobCostInfo = getEveryDayJobCost(nowBefore1day);

		long start = LocalDate.parse(nowBefore1day, dayFormat).atStartOfDay().toInstant(ZoneOffset.UTC).getEpochSecond()-8*3600;
		long end = start+3600*24-1;
		Tuple2<Map<Tuple2<String, String>, Double>, Map<Tuple2<String, String>, Double>> metricsBefore1day = getCpuMetricsByJob(start, end);

		Graph graph = buildGraph(jobCostInfo,metricsBefore1day.f0,metricsBefore1day.f1);

		Set<FlinkSqlLeafSceneAndBusinessLine> newLeafNode = graph.getNewLeafNode(buildExistScene());
		flinkSqlLeafSceneAndBusinessLineService.updateAll(newLeafNode);
	}

	public void scheduleEveryMonthLeafJob(String date) throws Exception{
		String realDate = "0".equals(date) ? LocalDate.now().plusMonths(-1).format(monthFormat) : date;
			// 检索新的叶子节点
		Map<Tuple2<String, String>, Float> jobCostInfo = getEveryMonthJobCost(realDate);

		Graph graph = buildGraph(jobCostInfo,new HashMap<>(),new HashMap<>());

		Set<FlinkSqlLeafSceneAndBusinessLine> newLeafNode = graph.getNewLeafNode(buildExistScene());
		flinkSqlLeafSceneAndBusinessLineService.updateAll(newLeafNode);
	}

	/**
	 * 调度生成每日成本
	 * @param date
	 * @throws Exception
	 */
	public void scheduleDayJobCost(String date) throws Exception {
		String nowBefore1day = "0".equals(date) ? LocalDate.now().plusDays(-2).format(dayFormat) : date;
		Map<Tuple2<String, String>, Float> jobCostInfo = getEveryDayJobCost(nowBefore1day);

		long start = LocalDate.parse(nowBefore1day, dayFormat).atStartOfDay().toInstant(ZoneOffset.UTC).getEpochSecond()-8*3600;
		long end = start+3600*24-1;
		Tuple2<Map<Tuple2<String, String>, Double>, Map<Tuple2<String, String>, Double>> metricsBefore1day = getCpuMetricsByJob(start, end);

		Graph graph = buildGraph(jobCostInfo,metricsBefore1day.f0,metricsBefore1day.f1);
		Map<Tuple2<String, String>, Tuple2<List<String>, String>> jobScene = buildExistScene();

//		Set<FlinkSqlLeafSceneAndBusinessLine> newLeafNode = graph.getNewLeafNode(jobScene);
//		flinkSqlLeafSceneAndBusinessLineService.updateAll(newLeafNode);

		Map<LinkedJob, JobShareMetrics> linkedJobRealCost = graph.getTailJobShareMetrics();

		sendLinkedJob2Kafka(nowBefore1day,linkedJobRealCost,LinkedJob.class.getSimpleName());

		Map<String, Float> kafkaInstanceCost = getEveryDayKafkaCost(nowBefore1day);
		scheduleKafkaCost(graph,date,linkedJobRealCost.keySet(),kafkaInstanceCost);

	}

	@Test
	public  void  testScheduleDayJobCost() throws Exception{
		String currentDay = LocalDate.now().plusDays(-1).format(dayFormat);
		long start = LocalDate.parse(currentDay, dayFormat).atStartOfDay().toInstant(ZoneOffset.UTC).getEpochSecond()-8*3600;
		long end = start+3600*24-1;
		Tuple2<Map<Tuple2<String, String>, Double>, Map<Tuple2<String, String>, Double>> metricsBefore1day = getCpuMetricsByJob(start, end);
		String token = "Bearer eyJhbGciOiJIUzUxMiJ9.eyJqdGkiOiJEOUVDREUyRjI2QzFDQkE0NDczNEVBODBFQjcwNEE0NzAyQkE5QzgxQTVFNDhEQTYxRURBOTA3MUY0N0U4MEM0RTdFOUEyMUMwQjJBMTJBNTkxQzQ2NDExRTBFQkYzMEU4MTMzQThGODFFNTI2RUE5RkU5MkE2QkRFRjU3NTRDMzcwOUVGQUU4OUI1RDk0QTcwNDc2N0I0MUUwNEU5QkYwQ0NCRjM4MDRENkRFOEVCRjM5Njc4Q0UzRDJCM0U1Mzc0M0ZGQzdENjM3RTI2OTgyOUNFQUU1NjBGODZEQkI0MTc0RTRCRjlEODNBRTg1RTc0NjlCOUFGNjYzOUQzRDREQzE5QjA0QTdGMjQ3MTZGMEQyNkU2QzExMjJDNDkyOUNCRTA3NjU3OEFGNzBGN0Q3NEI2NUVBMjc2QjREREZCOEVDQUVCNTM1RUVBOUU2OUI1RDM0QUI1NEQ2QUYwNzI0MDkyREU4NkY2RDY1RDZEQTgzRTkzNUQ0OThFOEI2OTQ2RjcwRUI5OTIzMkE0NDc2QkI0RUZGMUI5OUZBQ0UxMDQ3NTM4NDU2NEZGNDZGQkQyNzJDMzAwOEY2OTU0QzZFNTIyNTZGNTFDNDJDODAyNTQ4RDA0RkM1MjRENzU4OTVGN0FFOUU5Mjc1MzdBRTUzMUM0OTcyMEU4RENCQTM5RjA3RkEzQzFCRUQ2MjQ0MDYwNjM3QTI4MUI1Q0U5MTgyOTlCNzVCNzlGQ0RFMTVBRUVFODdFQ0I2MkY1NkQzMEY4NUZBMEZEQTk2NzBEQTgyMENCMDFBQTZGODc2RUYyNjZFNEJCQjAyMjhGODBERjI5QjM4QzhGNUMxQ0YyNUMyNjFBM0FGNUVCMDc4OEYzRjkwMTkxMzZDN0E5NUEzMEEwRDE0M0Q0NDA2QUU4RjYxQjMyMUQyNTM2QkZBMkFBQUM1QzE4NEQ4NDUwMjhFNjkxREYxQkE3QzYwMjQ4Mzk2MEUyMTM3MTY1MjU5MDhCQjI1QTZCNjAxNzBGMDM2QTg2QzdGNTMwQUZFMjcxRTkyQTk4NTdFRjU5RDFEQ0EwNTlDRUMzOTE5REFEN0Y5MTBFQzYyQUJCRTJEQkNCQjlBIiwidXNlciI6ImNoZW55dWhhbyIsInN1YiI6ImNoZW55dWhhbyJ9.2mgr8Yk3HR5ivXdf0nI8Oxjd8sBYFl6Rz1lEN1ONxljVYpV3pwfpATM0nPBLtqWwSJ__AXpO16YWeu6mi2uVxg\n";
		List<FlinkSqlLineageDto> flinkSqlLineage = getFlinkSqlLineage(token);
		Graph graph = new Graph(metricsBefore1day.f0,metricsBefore1day.f1);
		graph.buildTableGraph(flinkSqlLineage);

		Map<LinkedJob, JobShareMetrics> linkedJobRealCost = graph.getTailJobShareMetrics();
		Map<String, Float> everyDayKafkaCost = getEveryDayKafkaCost(currentDay);
		scheduleKafkaCost(graph,currentDay,linkedJobRealCost.keySet(),everyDayKafkaCost);

	}



	/**
	 * 调度每月作业成本
	 * @param date
	 * @throws Exception
	 */
	public void scheduleMonthJobCost(String date) throws Exception {
		String nowBefore1Month = "0".equals(date) ? LocalDate.now().plusMonths(-1).format(monthFormat) : date;

		Map<Tuple2<String, String>, Float> jobCostInfo = getEveryMonthJobCost(nowBefore1Month);

		Graph graph = buildGraph(jobCostInfo,new HashMap<>(),new HashMap<>());
		Map<Tuple2<String, String>, Tuple2<List<String>, String>> jobScene = buildExistScene();
//		Set<FlinkSqlLeafSceneAndBusinessLine> newLeafNode = graph.getNewLeafNode(jobScene);
//		flinkSqlLeafSceneAndBusinessLineService.updateAll(newLeafNode);

		Map<LinkedJob, JobShareMetrics> linkedJobRealCost = graph.getTailJobShareMetrics();
		sendLinkedJob2Kafka(date,linkedJobRealCost,LinkedJob.class.getSimpleName());

		Map<String, Float> kafkaInstanceCost = getEveryMonthKafkaCost(date);

		scheduleKafkaCost(graph,nowBefore1Month,linkedJobRealCost.keySet(),kafkaInstanceCost);

	}

	/**
	 * 调度生成专用cpu使用
	 * @param date
	 * @throws Exception
	 */
	public void scheduleDayJobCpuMetrics(String date) throws Exception {
		String nowBefore1day = "0".equals(date) ? LocalDate.now().plusDays(-1).format(dayFormat) : date;

		long start = LocalDate.parse(nowBefore1day, dayFormat).atStartOfDay().toInstant(ZoneOffset.UTC).getEpochSecond() - 8 * 3600;
		long end = start + 3600 * 24 - 1;

		Map<JSONObject, JobMetrics> cpuMetricsByPodPoolCluster = getCpuMetricsByPod(start, end);

		sendCpuResult2Kafka(cpuMetricsByPodPoolCluster, nowBefore1day, "CPU");
	}

	public void scheduleDayAvgJobCpuMetrics(String date) throws Exception {
		String nowBefore1day = "0".equals(date) ? LocalDate.now().plusDays(-1).format(dayFormat) : date;

		long start = LocalDate.parse(nowBefore1day, dayFormat).atStartOfDay().toInstant(ZoneOffset.UTC).getEpochSecond() - 8 * 3600;
		long end = start + 3600 * 24 - 1;

		Map<JSONObject, JobMetrics> cpuMetricsByPodPoolCluster = getAverageSpecificCpuMetricsByPodPoolCluster(start, end);

		sendCpuResult2Kafka(cpuMetricsByPodPoolCluster, nowBefore1day, "AVG_CPU");
	}


	/**
	 * 调度生成专用cpu使用
	 *
	 * @param date
	 * @throws Exception
	 */
	public void scheduleLatest7DayJobCpuMetrics(String date) throws Exception {
		for (int i = 1; i <= 7; i++) {

			String nowBefore1day = "0".equals(date) ? LocalDate.now().plusDays(-i).format(dayFormat) : date;

			long start = LocalDate.parse(nowBefore1day, dayFormat).atStartOfDay().toInstant(ZoneOffset.UTC).getEpochSecond() - 8 * 3600;
			long end = start + 3600 * 24 - 1;

			Map<JSONObject, JobMetrics> cpuMetricsByPodPoolCluster = getCpuMetricsByPod(start, end);

			sendCpuResult2Kafka(cpuMetricsByPodPoolCluster, nowBefore1day, "CPU");
		}
	}

	/**
	 * 调度生成该月到当日成本
	 *
	 * @param num
	 * @throws Exception
	 */
	public void scheduleLatest7DayJobCpu(String date) throws Exception {
		for (int i = 2; i <= 8; i++) {
			String nowBefore1day = LocalDate.now().plusDays(-i).format(dayFormat);
			Map<Tuple2<String, String>, Float> jobCostInfo = getEveryDayJobCost(nowBefore1day);

			long start = LocalDate.parse(nowBefore1day, dayFormat).atStartOfDay().toInstant(ZoneOffset.UTC).getEpochSecond()-8*3600;
			long end = start+3600*24-1;
			Tuple2<Map<Tuple2<String, String>, Double>, Map<Tuple2<String, String>, Double>> metricsBefore1day = getCpuMetricsByJob(start, end);

			Graph graph = buildGraph(jobCostInfo,metricsBefore1day.f0,metricsBefore1day.f1);
			Map<Tuple2<String, String>, Tuple2<List<String>, String>> jobScene = buildExistScene();

			Set<FlinkSqlLeafSceneAndBusinessLine> newLeafNode = graph.getNewLeafNode(jobScene);
			flinkSqlLeafSceneAndBusinessLineService.updateAll(newLeafNode);

			Map<LinkedJob, JobShareMetrics> linkedJobRealCost = graph.getTailJobShareMetrics();

			sendLinkedJob2Kafka(nowBefore1day,linkedJobRealCost,LinkedJob.class.getSimpleName());

			Map<String, Float> kafkaInstanceCost = getEveryDayKafkaCost(nowBefore1day);
			scheduleKafkaCost(graph,date,linkedJobRealCost.keySet(),kafkaInstanceCost);
		}

	}





	private Graph buildGraph(Map<Tuple2<String, String>, Float> jobCostInfo,Map<Tuple2<String, String>, Double> jobCpuUsage,Map<Tuple2<String, String>, Double> jobCpuLimit) throws Exception{
		Graph graph = new Graph(jobCostInfo,jobCpuUsage,jobCpuLimit);
		FlinkSqlLineageQueryCriteria flinkSqlLineageQueryCriteria = new FlinkSqlLineageQueryCriteria();
		List<FlinkSqlLineageDto> flinkSqlLineageDtos = flinkSqlLineageService.queryAll(flinkSqlLineageQueryCriteria);
		graph.buildTableGraph(flinkSqlLineageDtos);
		return graph;
	}


	private Graph buildGraph(Map<Tuple2<String, String>, Float> jobCostInfo) throws Exception{
		Graph graph = new Graph(jobCostInfo);
		FlinkSqlLineageQueryCriteria flinkSqlLineageQueryCriteria = new FlinkSqlLineageQueryCriteria();
		List<FlinkSqlLineageDto> flinkSqlLineageDtos = flinkSqlLineageService.queryAll(flinkSqlLineageQueryCriteria);
		graph.buildTableGraph(flinkSqlLineageDtos);
		return graph;
	}

	private Graph buildGraph(Map<Tuple2<String, String>, Double> jobCpuUsage,Map<Tuple2<String, String>, Double> jobCpuLimit) throws Exception{
		Graph graph = new Graph(jobCpuUsage,jobCpuLimit);
		FlinkSqlLineageQueryCriteria flinkSqlLineageQueryCriteria = new FlinkSqlLineageQueryCriteria();
		List<FlinkSqlLineageDto> flinkSqlLineageDtos = flinkSqlLineageService.queryAll(flinkSqlLineageQueryCriteria);
		graph.buildTableGraph(flinkSqlLineageDtos);
		return graph;
	}

	private Graph buildGraph(List<FlinkSqlLineageDto> flinkSqlLineageDtos ,Map<Tuple2<String, String>, Float> jobCostInfo,Map<Tuple2<String, String>, Double> jobCpuUsage,Map<Tuple2<String, String>, Double> jobCpuLimit) throws Exception{
		Graph graph = new Graph(jobCostInfo,jobCpuUsage,jobCpuLimit);
		graph.buildTableGraph(flinkSqlLineageDtos);
		return graph;
	}


	/**
	 * 生成kafka成本
	 * @param graph
	 * @param date
	 * @param tailJobs
	 * @throws Exception
	 */
	public void scheduleKafkaCost(Graph graph,String date,Set<LinkedJob> tailJobs,Map<String, Float> kafkaInstanceCost) throws Exception {

		KeTopicQueryCriteria keTopicQueryCriteria = new KeTopicQueryCriteria();
		keTopicQueryCriteria.setDiskSize(0);
		keTopicQueryCriteria.setCluster(Graph.kafkaInstance.values());
		keTopicQueryCriteria.setStatus(2);
		keTopicQueryCriteria.setCreated(date);
		List<KeTopicDto> keTopicDtos = keTopicService.queryAll(keTopicQueryCriteria);
		// 数据平台各集群成本
		Map<String, Long> clusterAgg = keTopicDtos.stream().collect(Collectors.groupingBy(KeTopicDto::getCluster, Collectors.summingLong(KeTopicDto::getDiskSize)));
		//数据平台Flink sql作业使用的kafka表
		Set<Tuple2<String, String>> datePlatformTables = graph.tableMapping.keySet().stream().filter(s -> kafkaInstance.containsKey(s.getInstanceName()))
				.map(s -> Tuple2.of(kafkaInstance.get(s.getInstanceName()), s.getTableName())).collect(Collectors.toSet());		
		//数据平台sql作业未使用的topic表 
		Map<Tuple2<String, String>, Float> sqlJobUnUseKafkaTopicCost = new HashMap<>();
		Map<Tuple2<String, String>, Float> sqlJobUseKafkaTopicCost = new HashMap<>();
		
		for (KeTopicDto keTopicDto : keTopicDtos) {
			long diskSize = keTopicDto.getDiskSize();
			long total = clusterAgg.getOrDefault(keTopicDto.getCluster(),0L);
			float instanceFloat = kafkaInstanceCost.getOrDefault(keTopicDto.getCluster(),0f);
			if (!datePlatformTables.contains(Tuple2.of(keTopicDto.getCluster(),keTopicDto.getTopic()))){
				sqlJobUnUseKafkaTopicCost.put(Tuple2.of(keTopicDto.getCluster(),keTopicDto.getTopic()),diskSize*instanceFloat/total);
			}else {
				sqlJobUseKafkaTopicCost.put(Tuple2.of(keTopicDto.getCluster(),keTopicDto.getTopic()),diskSize*instanceFloat/total);
			}
		}

		//sql作业使用的实例
		Map<String, Double> sqlJobContainsInstance = sqlJobUseKafkaTopicCost.entrySet().stream().collect(Collectors.groupingBy(
				e -> e.getKey().f0, Collectors.summingDouble(Map.Entry::getValue)));

		double sqlJobUseKafkaTopicCostValue = sqlJobContainsInstance.values().stream().mapToDouble(v -> v).sum();
		log.info("date:{},sqlJobUseKafkaTopicCostValue is:{}",date,sqlJobUseKafkaTopicCostValue);

		double sqlJobUnUseKafkaTopicCostValue = sqlJobUnUseKafkaTopicCost.values().stream().mapToDouble(v -> v).sum();
		log.info("date:{},sqlJobUnUseKafkaTopicCostValue is:{}",date,sqlJobUnUseKafkaTopicCostValue);

		//产生的成本但是下线的kafka集群成本
		Map<String, Float> unUseInstanceCost = kafkaInstanceCost.entrySet().stream().
				filter(k -> !sqlJobContainsInstance.containsKey(k.getKey())).collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));

		unUseInstanceCost.forEach((k,v)-> log.info("date:{},not found Instance:{},cost:{}",date,k,v));

		double notFoundKafkaInstanceCostValue = unUseInstanceCost.values().stream().mapToDouble(v -> v).sum();
		log.info("date:{},notFoundKafkaInstanceCostValue is:{}",date,notFoundKafkaInstanceCostValue);

		//计算kafka表包含的叶子节点
		Map<Table, Set<Job>> tableContainLeafJobs = new HashMap<>();
		tailJobs.forEach(s-> graph.calcLinkedKafka(s,s,tableContainLeafJobs));

		Set<CostTable> costTables = buildCostTable(tableContainLeafJobs,sqlJobUseKafkaTopicCost);

		sendCostTable2Kafka(date,costTables);

	}

	private Set<CostTable> buildCostTable(Map<Table, Set<Job>> tableContainLeafJobs,Map<Tuple2<String, String>, Float> sqlJobUseKafkaTopicCost ){
		Set<CostTable> costTableResult =  new HashSet<>();

		for (Map.Entry<Table, Set<Job>> entry : tableContainLeafJobs.entrySet()) {
			CostTable costTable = new CostTable();
			costTable.setDbType(entry.getKey().getDbType());
			costTable.setInstanceName(entry.getKey().getInstanceName());
			costTable.setDatabase(entry.getKey().getDatabase());
			costTable.setTableName(entry.getKey().getTableName());
			Float tableCost = sqlJobUseKafkaTopicCost.getOrDefault(
					Tuple2.of(kafkaInstance.get(entry.getKey().getInstanceName()), entry.getKey().getTableName()), 0f);
			costTable.setRealCost(tableCost);
			costTable.setUsedTailJobs(entry.getValue());
			costTableResult.add(costTable);
		}
		return  costTableResult;
	}
	
	private Map<String,Float> allocateKafkaCost2BusinessLine(Map<Tuple2<String, String>, Float> sqlJobUseKafkaTopicCost,
	                                            Map<Tuple2<String, String>, Float> sqlJobUnUseKafkaTopicCost,
	                                            Map<Table, Set<Job>> tableContainLeafJobs,
	                                                         Map<String, Float> unUseInstanceCost) throws Exception{
		Map<Tuple2<String, String>, Tuple2<List<String>, String>> jobScene = buildExistScene();
		Map<String,Float> sceneCost = new HashMap<>();

		for (Map.Entry<Table, Set<Job>> entry : tableContainLeafJobs.entrySet()) {
			Float tableCost = sqlJobUseKafkaTopicCost.getOrDefault(
					Tuple2.of(kafkaInstance.get(entry.getKey().getInstanceName()),entry.getKey().getTableName()),0f);

			Map<List<String>, Integer> sceneInfo = new HashMap<>();
			//计算每个表业务线下的数量
			for (Job job : entry.getValue()) {
				Tuple2<List<String>, String> scene = jobScene.get(Tuple2.of(job.getProjectName(), job.getTaskName()));
				List<String> key;
				if (scene==null){
					key = new ArrayList<>();
					key.add("待打标");
				}else {
					key = scene.f0;
				}

				Integer exist = sceneInfo.getOrDefault(key, 0);
				sceneInfo.put(key,exist+1);
			}


			sceneInfo.forEach((k,v)->{
				//计算表在每个业务线所分摊的成本
				for (String business : k) {
					Float existCost = sceneCost.getOrDefault(business, 0f);
					int leafJobSize = entry.getValue().size();
					//计算每个业务线下的成本  每条业务线成本=表成本/叶子节点作业个数/该作业的业务线数量   每条业务线成本*该
					Float newCost = existCost + (tableCost/leafJobSize/k.size()  * v*1.0f);
					sceneCost.put(business, newCost);
				}

			});
		}

		//把未在sql作业里的表分摊成本
		for (Map.Entry<Tuple2<String, String>, Float> entry : sqlJobUnUseKafkaTopicCost.entrySet()) {
			if (entry.getKey().f0.contains("binlog")){
				Float notFoundDataPlatform = sceneCost.getOrDefault("未分配-数据平台", 0f);
				sceneCost.put("未分配-数据平台",entry.getValue()+notFoundDataPlatform);
			}else if(entry.getKey().f1.contains("algo")){
				Float notFoundDataPlatform = sceneCost.getOrDefault("未分配-算法", 0f);
				sceneCost.put("未分配-算法",entry.getValue()+notFoundDataPlatform);
			}else{
				Float notFoundDataPlatform = sceneCost.getOrDefault("未分配-其他", 0f);
				sceneCost.put("未分配-其他",entry.getValue()+notFoundDataPlatform);
			}
		}

		unUseInstanceCost.forEach(sceneCost::put);
		
		return sceneCost;

	}


	public Map<Tuple2<String, String>, Tuple2<List<String>, String>> buildExistScene()  {

		FlinkSqlLeafSceneAndBusinessLineQueryCriteria flinkSqlLeafSceneAndBusinessLineQueryCriteria = new FlinkSqlLeafSceneAndBusinessLineQueryCriteria();
		List<FlinkSqlLeafSceneAndBusinessLineDto> flinkSqlLeafSceneAndBusinessLineDtos = flinkSqlLeafSceneAndBusinessLineService.queryAll(flinkSqlLeafSceneAndBusinessLineQueryCriteria);

		Map<Tuple2<String, String>, Tuple2<List<String>, String>> jobScene = new HashMap<>();
		for (FlinkSqlLeafSceneAndBusinessLineDto record : flinkSqlLeafSceneAndBusinessLineDtos) {
				String taskName = record.getJobName();
				String projectName = record.getProjectName();
				List<String> businessLine = Arrays.asList(record.getBusinessLine().split(","));
				String scene = record.getScene();
				jobScene.put(Tuple2.of(projectName, taskName), Tuple2.of(businessLine, scene));
		}
		return jobScene;
	}

	/**
	 * 从成本中心获取每月FLink成本
	 *
	 * @return
	 * @throws Exception
	 * @Param date: yyyy-MM
	 */
	public static Map<Tuple2<String, String>, Float> getEveryMonthJobCost(String date) throws Exception {
		String url = "https://cost.shizhuang-inc.com/api-cost/v1/bill/dw-project/date-detail/?dimension=month&date=" + date + "&project_id=flink&by_product_id=1&by_instance_id=1";
		URL obj = new URL(url);
		HttpURLConnection con = (HttpURLConnection) obj.openConnection();
		con.setRequestMethod("GET");
		con.setRequestProperty("Authorization", "ead45d7d0f6525d57e89bff16e9f2342160ba88f63c70d927107485988d66f96af8fce802e3f55e4bb3f0fbde36fd6aa");

		BufferedReader in = new BufferedReader(new InputStreamReader(con.getInputStream()));
		String inputLine;
		StringBuffer response = new StringBuffer();

		while ((inputLine = in.readLine()) != null) {
			response.append(inputLine);
		}
		in.close();

		ObjectMapper objectMapper = new ObjectMapper();
		objectMapper.setPropertyNamingStrategy(new PropertyNamingStrategy.SnakeCaseStrategy());
		objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
		Response<CostInfo> responseData = objectMapper.readValue(response.toString(), new TypeReference<Response<CostInfo>>() {
		});
		List<CostInfo> data = responseData.getData();

		return data.stream().collect(Collectors.toMap(c -> {
			String[] split = c.getInstanceId().split("/");
			return Tuple2.of(split[0], split[1]);
		}, CostInfo::getAmount));
	}

	/**
	 * 从成本中心获取每天FLink成本
	 *
	 * @param date:yyyy-MM-dd
	 * @return
	 * @throws Exception
	 */
	public static Map<Tuple2<String, String>, Float> getEveryDayJobCost(String date) throws Exception {
		String url = "https://cost.shizhuang-inc.com/api-cost/v1/bill/dw-project/date-detail/?dimension=day&date=" + date + "&project_id=flink&by_dept_lv2=1&by_dept_lv3=1&by_product_id=1&by_instance_id=1";
		URL obj = new URL(url);
		HttpURLConnection con = (HttpURLConnection) obj.openConnection();
		con.setRequestMethod("GET");
		con.setRequestProperty("Authorization", "ead45d7d0f6525d57e89bff16e9f2342160ba88f63c70d927107485988d66f96af8fce802e3f55e4bb3f0fbde36fd6aa");

		BufferedReader in = new BufferedReader(new InputStreamReader(con.getInputStream()));
		String inputLine;
		StringBuilder response = new StringBuilder();

		while ((inputLine = in.readLine()) != null) {
			response.append(inputLine);
		}
		in.close();

		ObjectMapper objectMapper = new ObjectMapper();
		objectMapper.setPropertyNamingStrategy(new PropertyNamingStrategy.SnakeCaseStrategy());
		Response<CostInfo> responseData = objectMapper.readValue(response.toString(), new TypeReference<Response<CostInfo>>() {
		});
		List<CostInfo> data = responseData.getData();

		return data.stream().collect(Collectors.toMap(c -> {
			String[] split = c.getInstanceId().split("/");
			return Tuple2.of(split[0], split[1]);
		}, CostInfo::getAmount,Float::sum));
	}

	/**
	 * 获取每天kafka成本
	 * @param date
	 * @return
	 * @throws Exception
	 */
	public static Map<String, Float> getEveryDayKafkaCost(String date) throws Exception {
		String url = "https://cost.shizhuang-inc.com/api-cost/v1/bill/dw-project/date-detail/?dimension=day&date="+date+"&project_id=kafka&by_dept_lv2=1&by_dept_lv3=1&by_product_id=1&by_instance_id=1";
		URL obj = new URL(url);
		HttpURLConnection con = (HttpURLConnection) obj.openConnection();
		con.setRequestMethod("GET");
		con.setRequestProperty("Authorization", "ead45d7d0f6525d57e89bff16e9f2342160ba88f63c70d927107485988d66f96af8fce802e3f55e4bb3f0fbde36fd6aa");

		BufferedReader in = new BufferedReader(new InputStreamReader(con.getInputStream()));
		String inputLine;
		StringBuffer response = new StringBuffer();

		while ((inputLine = in.readLine()) != null) {
			response.append(inputLine);
		}
		in.close();

		ObjectMapper objectMapper = new ObjectMapper();
		objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
		objectMapper.setPropertyNamingStrategy(new PropertyNamingStrategy.SnakeCaseStrategy());
		Response<DeptCostInfo> responseData = objectMapper.readValue(response.toString(), new TypeReference<Response<DeptCostInfo>>() {
		});
		List<DeptCostInfo> data = responseData.getData();

		Map<String, Float> map = new HashMap<>();
		for (DeptCostInfo d : data) {
			if (
					("实时数仓".equals(d.getDeptLv3Name()) || d.getInstanceId().contains("rt-kafka-bigdata-ads-v2"))
					&& (!d.getInstanceId().startsWith("d-"))
			) {
				String replace = d.getInstanceId().replace("(自建kafka)", "")
						.replace("rt-bigdata-kafka-dwd-v2", "rt-kafka-bigdata-dwd-v2")
						.replace("ods-sensor-log-kafka-v2","ods-sensors-log-kafka-v2");
				d.setInstanceId(replace);
				map.merge(d.getInstanceId(), d.getAmount(), Float::sum);
			}
		}

		map.forEach((k,v)->{
			log.info("date:{},instance:{},cost:{}",date,k,v);
		});
		return map;
	}


	/**
	 * 获取专用集群作业cpu使用
	 * @param start
	 * @param end
	 * @return
	 * @throws Exception
	 */
	public static Tuple2<Map<JSONObject, JobMetrics>, Integer> getSpecificCpuMetricsByPodPoolCluster(long start, long end) throws Exception{
		Map<JSONObject, JobMetrics> totalCpuUsageAndRequestResult = new HashMap<>();
		Map<String,Tuple2<String,String>> totalInstanceJobMap = new HashMap<>();
		for (String taskProject : specialCpuProjectSet) {
			String format = String.format(project_jobManager_job_up_time, taskProject);
			log.info("execute pull metric:{}, project:{},startTime:{}", "jobManager_cpu_usage", taskProject, start);
			JSONArray result = executeQuery(format, start, end, 1200);
			// job pod mapping
			Map<String,Tuple2<String,String>> instanceJobMap = result.stream().
					map(o -> ((JSONObject) o).getJSONObject("metric")).collect(Collectors.toMap(
							e -> e.getString("pod").split("-")[1],
					e -> Tuple2.of(taskProject,e.getString("job_name")),
					(existing, replacement) -> existing
			));
			totalInstanceJobMap.putAll(instanceJobMap);
		}

		//获取数据平台专用集群cpu使用最大的时间
		JSONArray objects = executeQuery(max_cpu_usage, start, end, 600);
		JSONArray maxCpuUsage = objects.getJSONObject(0).getJSONArray("values").stream().map(each -> ((JSONArray) each)).max((o1, o2) -> {
			double diff = Double.parseDouble(o1.getString(1)) - Double.parseDouble(o2.getString(1));
			if (diff > 0) {
				return 1;
			} else if (diff < 0) {
				return -1;
			} else {
				return o1.getInteger(0) - o2.getInteger(0);
			}
		}).get();
		Integer maxCpuUsageTimestamp = maxCpuUsage.getInteger(0);

		for (Tuple2<String, String> clusterPool: clusterAndPool) {
			String cluster = clusterPool.f0;
			String pool = clusterPool.f1;
			JSONArray cpuUsageResult = executeQuery(String.format(taskManager_cpu_usage_by_pod_pool_cluster, cluster, cluster, pool), maxCpuUsageTimestamp);
			JSONArray cpuRequestResult = executeQuery(String.format(taskManager_cpu_request_by_pod_pool_cluster, cluster, cluster, pool), maxCpuUsageTimestamp);
			JSONArray memoryRequestResult = executeQuery(String.format(taskManager_memory_request_by_pod_pool_cluster, cluster, cluster, pool), maxCpuUsageTimestamp);
			Map<JSONObject, JobMetrics> cpuUsageAndRequestMap = cpuUsageResult.stream().peek(e -> {
				JSONObject metric = ((JSONObject) e).getJSONObject("metric");
				String pod = metric.getString("pod");
				String instance = pod.split("-")[1];
				Tuple2<String, String> jobNameAndProject = totalInstanceJobMap.get(instance);
				if (jobNameAndProject == null){
					log.error("instance of project not found:{}",instance);
				}else {
					metric.put("task_project", jobNameAndProject.f0);
					metric.put("job_name", jobNameAndProject.f1);
				}
			}).collect(Collectors.toMap(
					e -> ((JSONObject) e).getJSONObject("metric"),
					e -> {
						Double cpuUsage = ((JSONObject) e).getJSONArray("value").getDouble(1);
						JobMetrics jobMetrics = new JobMetrics();
						jobMetrics.setCpuUsage(cpuUsage);
						return jobMetrics;
					}));

			cpuRequestResult.forEach(e -> {
				JSONObject metric = ((JSONObject) e).getJSONObject("metric");
				Double cpuRequest = ((JSONObject) e).getJSONArray("value").getDouble(1);
				String pod = metric.getString("pod");
				String instance = pod.split("-")[1];
				Tuple2<String, String> jobNameAndProject = totalInstanceJobMap.get(instance);
				if (jobNameAndProject == null){
					log.error("monitor is not running,{}",instance);
				}else {
					metric.put("task_project", jobNameAndProject.f0);
					metric.put("job_name", jobNameAndProject.f1);
				}
				JobMetrics exist = cpuUsageAndRequestMap.get(metric);
				if ( cpuUsageAndRequestMap.get(metric) != null){
					exist.setCpuRequest(cpuRequest); ;
					cpuUsageAndRequestMap.put(metric,exist);
				}else {
					log.error("not found cpu usage,{}，only has cpu request",metric);
				}
			});

			memoryRequestResult.forEach(e -> {
				JSONObject metric = ((JSONObject) e).getJSONObject("metric");
				Double memoryRequest = ((JSONObject) e).getJSONArray("value").getDouble(1);
				String pod = metric.getString("pod");
				String instance = pod.split("-")[1];
				Tuple2<String, String> jobNameAndProject = totalInstanceJobMap.get(instance);
				if (jobNameAndProject == null){
					log.error("monitor is not running,{}",instance);
				}else {
					metric.put("task_project", jobNameAndProject.f0);
					metric.put("job_name", jobNameAndProject.f1);
				}
				JobMetrics exist = cpuUsageAndRequestMap.get(metric);
				if (cpuUsageAndRequestMap.get(metric) != null) {
					exist.setMemoryRequest(memoryRequest);
					cpuUsageAndRequestMap.put(metric, exist);
				} else {
					log.error("not found cpu usage,{}，only has cpu request", metric);
				}
			});

			totalCpuUsageAndRequestResult.putAll(cpuUsageAndRequestMap);
		}

		return Tuple2.of(totalCpuUsageAndRequestResult, maxCpuUsageTimestamp);
	}

	public static Map<JSONObject, JobMetrics> getAverageSpecificCpuMetricsByPodPoolCluster(long start, long end) throws Exception {
		Map<JSONObject, JobMetrics> totalCpuUsageAndRequestResult = new HashMap<>();
		Map<String, Tuple2<String, String>> totalInstanceJobMap = new HashMap<>();
		for (String taskProject : specialCpuProjectSet) {
			String format = String.format(project_jobManager_job_up_time, taskProject);
			log.info("execute pull metric:{}, project:{},startTime:{}", "jobManager_cpu_usage", taskProject, start);
			JSONArray result = executeQuery(format, start, end, 1200);
			// job pod mapping
			Map<String, Tuple2<String, String>> instanceJobMap = result.stream().
					map(o -> ((JSONObject) o).getJSONObject("metric")).collect(Collectors.toMap(
					e -> e.getString("pod").split("-")[1],
					e -> Tuple2.of(taskProject, e.getString("job_name")),
					(existing, replacement) -> existing
			));
			totalInstanceJobMap.putAll(instanceJobMap);
		}

		//获取数据平台专用集群cpu使用最大的时间
		JSONArray objects = executeQuery(max_cpu_usage, start, end, 600);
		JSONArray maxCpuUsage = objects.getJSONObject(0).getJSONArray("values").stream().map(each -> ((JSONArray) each)).max((o1, o2) -> {
			double diff = Double.parseDouble(o1.getString(1)) - Double.parseDouble(o2.getString(1));
			if (diff > 0) {
				return 1;
			} else if (diff < 0) {
				return -1;
			} else {
				return o1.getInteger(0) - o2.getInteger(0);
			}
		}).get();
		Integer maxCpuUsageTimestamp = maxCpuUsage.getInteger(0);

		for (Tuple2<String, String> clusterPool : clusterAndPool) {
			String cluster = clusterPool.f0;
			String pool = clusterPool.f1;
			JSONArray cpuUsageResult = executeQuery(String.format(taskManager_avg_cpu_usage_by_pod_pool_cluster, cluster, cluster, pool), start, end, 60);
			JSONArray cpuRequestResult = executeQuery(String.format(taskManager_cpu_request_by_pod_pool_cluster, cluster, cluster, pool), maxCpuUsageTimestamp);
			Map<JSONObject, JobMetrics> cpuUsageAndRequestMap = cpuUsageResult.stream().peek(e -> {
				JSONObject metric = ((JSONObject) e).getJSONObject("metric");
				String pod = metric.getString("pod");
				String instance = pod.split("-")[1];
				Tuple2<String, String> jobNameAndProject = totalInstanceJobMap.get(instance);
				if (jobNameAndProject == null) {
					log.error("instance of project not found:{}", instance);
				} else {
					metric.put("task_project", jobNameAndProject.f0);
					metric.put("job_name", jobNameAndProject.f1);
				}
			}).collect(Collectors.toMap(
					e -> ((JSONObject) e).getJSONObject("metric"),
					e -> {
						JSONArray cpuUsage = ((JSONObject) e).getJSONArray("values");
						Double init = 0d;
						for (int i = 0; i < cpuUsage.size(); i++) {
							init += cpuUsage.getJSONArray(i).getDouble(1);
						}
						double avgCpuUsage = init / cpuUsage.size();
						JobMetrics jobMetrics = new JobMetrics();
						jobMetrics.setCpuUsage(avgCpuUsage);
						return jobMetrics;
					}));

			cpuRequestResult.forEach(e -> {
				JSONObject metric = ((JSONObject) e).getJSONObject("metric");
				Double cpuRequest = ((JSONObject) e).getJSONArray("value").getDouble(1);
				String pod = metric.getString("pod");
				String instance = pod.split("-")[1];
				Tuple2<String, String> jobNameAndProject = totalInstanceJobMap.get(instance);
				if (jobNameAndProject == null) {
					log.error("monitor is not running,{}", instance);
				} else {
					metric.put("task_project", jobNameAndProject.f0);
					metric.put("job_name", jobNameAndProject.f1);
				}
				JobMetrics exist = cpuUsageAndRequestMap.get(metric);
				if (cpuUsageAndRequestMap.get(metric) != null) {
					exist.setCpuRequest(cpuRequest);
					;
					cpuUsageAndRequestMap.put(metric, exist);
				} else {
					log.error("not found cpu usage,{}，only has cpu request", metric);
				}
			});


			totalCpuUsageAndRequestResult.putAll(cpuUsageAndRequestMap);
		}

		return totalCpuUsageAndRequestResult;
	}


	/**
	 * 获取混部集群cpu使用
	 *
	 * @param start
	 * @param end
	 * @return
	 * @throws Exception
	 */
	private static Map<JSONObject, JobMetrics> getReclaimedCpuMetricsByPodPoolCluster(long start, long end, Set<String> specialInstanceSet, Integer pullTimestamp) throws Exception {
		Map<JSONObject, JobMetrics> totalCpuUsageAndRequestResult = new HashMap<>();
		//step 1  获取混部集群实例和空间作业mapping
		Map<String, Tuple2<String, String>> totalInstanceJobMap = new HashMap<>();
		for (String taskProject : Graph.projectSet) {
			String format = String.format(project_jobManager_job_up_time, taskProject);
			log.info("execute pull metric:{}, project:{},startTime:{}", "jobManager_cpu_usage", taskProject, start);
			JSONArray result = executeQuery(format, start, end, 1200);
			// job pod mapping
			Map<String, Tuple2<String, String>> jobPodMap = result.stream()
					.map(o -> ((JSONObject) o).getJSONObject("metric"))
					.filter(e->!specialInstanceSet.contains(e.getString("pod").split("-")[1]))
					.collect(Collectors.toMap(
							e -> e.getString("pod").split("-")[1],
					e -> Tuple2.of(taskProject,e.getString("job_name")),
					(existing, replacement) -> existing
			));
			totalInstanceJobMap.putAll(jobPodMap);
		}

		//step 2  获取混部集群实例 cpu usage和reqeust
		List<String> instanceList = totalInstanceJobMap.keySet().stream().map(e -> String.format("flink-%s-taskmanager.*", e)).collect(Collectors.toList());
		for (int i = 0; i < instanceList.size(); i += 100) {
			String podLists = Joiner.on("|").join(instanceList.subList(i, Math.min(i + 100, instanceList.size())));
			JSONArray cpuUsageResult = executeQuery(String.format(taskManager_reclaimed_cpu_usage_by_pod_pool_cluster,podLists), start);
			JSONArray cpuRequestResult = executeQuery(String.format(taskManager_reclaimed_cpu_request_by_pod_pool_cluster,podLists), start);
			JSONArray memoryRequestResult = executeQuery(String.format(taskManager_reclaimed_memory_request_by_pod_pool_cluster,podLists), start);
			Map<JSONObject, JobMetrics> cpuUsageAndRequestMap = cpuUsageResult.stream()
					.peek(
							o -> {
								JSONObject metric = ((JSONObject) o).getJSONObject("metric");
								String pod = metric.getString("pod");
								String instanceId = pod.split("-")[1];
								Tuple2<String, String> projectAndJobName = totalInstanceJobMap.get(instanceId);
								if (projectAndJobName != null) {
									metric.put("task_project", projectAndJobName.f0);
									metric.put("job_name", projectAndJobName.f1);
								}
							}
					).collect(Collectors.toMap(
							e -> ((JSONObject) e).getJSONObject("metric"),
							e -> {
								Double cpuUsage = ((JSONObject) e).getJSONArray("value").getDouble(1);
								JobMetrics jobMetrics = new JobMetrics();
								jobMetrics.setCpuUsage(cpuUsage);
								return jobMetrics;
							}
					));

			cpuRequestResult.forEach(e -> {
				JSONObject metric = ((JSONObject) e).getJSONObject("metric");
				Double cpuRequest = ((JSONObject) e).getJSONArray("value").getDouble(1);
				String pod = metric.getString("pod");
				String instanceId = pod.split("-")[1];
				Tuple2<String, String> jobNameAndProject = totalInstanceJobMap.get(instanceId);
				metric.put("task_project", jobNameAndProject.f0);
				metric.put("job_name", jobNameAndProject.f1);
				JobMetrics exist = cpuUsageAndRequestMap.get(metric);
				if ( cpuUsageAndRequestMap.get(metric) != null){
					exist.setCpuRequest(cpuRequest);
					cpuUsageAndRequestMap.put(metric,exist);
				}else {
					log.error("not found cpu usage,{}，only has cpu request",metric);
				}
			});

			memoryRequestResult.forEach(e -> {
				JSONObject metric = ((JSONObject) e).getJSONObject("metric");
				Double memoryRequest = ((JSONObject) e).getJSONArray("value").getDouble(1);
				String pod = metric.getString("pod");
				String instanceId = pod.split("-")[1];
				Tuple2<String, String> jobNameAndProject = totalInstanceJobMap.get(instanceId);
				metric.put("task_project", jobNameAndProject.f0);
				metric.put("job_name", jobNameAndProject.f1);
				JobMetrics exist = cpuUsageAndRequestMap.get(metric);
				if ( cpuUsageAndRequestMap.get(metric) != null){
					exist.setMemoryRequest(memoryRequest);
					cpuUsageAndRequestMap.put(metric,exist);
				}else {
					log.error("not found cpu usage,{}，only has cpu request",metric);
				}
			});
			totalCpuUsageAndRequestResult.putAll(cpuUsageAndRequestMap);
		}

		return totalCpuUsageAndRequestResult;
	}

	/**
	 * 获取pod维度的cpu使用
	 * @param start
	 * @param end
	 * @return
	 * @throws Exception
	 */
	public static Map<JSONObject, JobMetrics> getCpuMetricsByPod(long start, long end) throws Exception{
		//step1 获取专用集群cpu使用
		Tuple2<Map<JSONObject, JobMetrics>, Integer> specificCpuMetricsByPodPoolClusterAndTimestamp = getSpecificCpuMetricsByPodPoolCluster(start, end);

		Map<JSONObject, JobMetrics> specificCpuMetricsByPodPoolCluster = specificCpuMetricsByPodPoolClusterAndTimestamp.f0;
		Integer specialMaxCpuUsageTimestamp = specificCpuMetricsByPodPoolClusterAndTimestamp.f1;
		Set<String> specialInstanceSet = specificCpuMetricsByPodPoolCluster.keySet().stream()
				.map(doubleDoubleTuple2 -> doubleDoubleTuple2.getString("pod").split("-")[1])
				.collect(Collectors.toSet());

		//step1 获取混部集群cpu使用
		Map<JSONObject, JobMetrics> reclaimedCpuMetricsByPodPoolCluster = getReclaimedCpuMetricsByPodPoolCluster(start, end, specialInstanceSet,specialMaxCpuUsageTimestamp);

		specificCpuMetricsByPodPoolCluster.putAll(reclaimedCpuMetricsByPodPoolCluster);

		return specificCpuMetricsByPodPoolCluster;
	}



	/**
	 * 获取数据平台cpu使用 专用和混部
	 * @param start
	 * @param end
	 * @return
	 * @throws Exception
	 */
	private static Tuple2<Map<Tuple2<String, String>, Double>, Map<Tuple2<String, String>, Double>> getCpuMetricsByJob(long start, long end) throws Exception{
		Map<JSONObject, JobMetrics> cpuMetricsByPod = getCpuMetricsByPod(start, end);
		Map<Tuple2<String,String>, Double> cpuUsage = cpuMetricsByPod.entrySet().stream().collect(Collectors.groupingBy(
				e -> Tuple2.of(
						e.getKey().getString("task_project"),
						e.getKey().getString("job_name")
				),
				Collectors.summingDouble(e -> e.getValue().getCpuUsage())
		));
		Map<Tuple2<String,String>, Double> cpuRequest = cpuMetricsByPod.entrySet().stream().collect(Collectors.groupingBy(
				e -> Tuple2.of(
						e.getKey().getString("task_project"),
						e.getKey().getString("job_name")
				),
				Collectors.summingDouble(e -> e.getValue().getCpuRequest())
		));

		return Tuple2.of(cpuUsage,cpuRequest);
	}



	private static JSONArray executeQuery(String queryLanguage, long start, long end, int step) {
		QueryRangeParam queryRangeParam = new QueryRangeParam(LibraURLUtils.encode(queryLanguage));
		queryRangeParam.setEnd(end);
		queryRangeParam.setStart(start);
		queryRangeParam.setStep(step);
		queryRangeParam.setTimeout(30*1000);
		PrometheusBean result = component.queryRange(queryRangeParam);
		return Optional.ofNullable(result).map(PrometheusBean::getData).map(e -> e.getJSONArray("result")).orElse(new JSONArray());
	}

	private static JSONArray executeQuery(String queryLanguage, long timestamp) {
		QueryParam queryParam = new QueryParam(LibraURLUtils.encode(queryLanguage));
		queryParam.setTime(timestamp);
		queryParam.setTimeout(30*1000);
		PrometheusBean result = component.query(queryParam);
		return Optional.ofNullable(result).map(PrometheusBean::getData).map(e -> e.getJSONArray("result")).orElse(new JSONArray());
	}


	/**
	 * 获取每月kafka成本
	 * @param date
	 * @return
	 * @throws Exception
	 */
	public static Map<String, Float> getEveryMonthKafkaCost(String date) throws Exception {
		String url = "https://cost.shizhuang-inc.com/api-cost/v1/bill/dw-project/date-detail/?dimension=month&date="+date+"&project_id=kafka&by_dept_lv2=1&by_dept_lv3=1&by_product_id=1&by_instance_id=1";
		URL obj = new URL(url);
		HttpURLConnection con = (HttpURLConnection) obj.openConnection();
		con.setRequestMethod("GET");
		con.setRequestProperty("Authorization", "ead45d7d0f6525d57e89bff16e9f2342160ba88f63c70d927107485988d66f96af8fce802e3f55e4bb3f0fbde36fd6aa");

		BufferedReader in = new BufferedReader(new InputStreamReader(con.getInputStream()));
		String inputLine;
		StringBuffer response = new StringBuffer();

		while ((inputLine = in.readLine()) != null) {
			response.append(inputLine);
		}
		in.close();

		ObjectMapper objectMapper = new ObjectMapper();
		objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
		objectMapper.setPropertyNamingStrategy(new PropertyNamingStrategy.SnakeCaseStrategy());
		Response<DeptCostInfo> responseData = objectMapper.readValue(response.toString(), new TypeReference<Response<DeptCostInfo>>() {
		});
		List<DeptCostInfo> data = responseData.getData();

		Map<String, Float> map = new HashMap<>();
		for (DeptCostInfo d : data) {
			if (
					("实时数仓".equals(d.getDeptLv3Name()) || d.getInstanceId().contains("rt-kafka-bigdata-ads-v2")||"数据仓库".equals(d.getDeptLv3Name()))
					&& (!d.getInstanceId().startsWith("d-"))
			) {
				String replace = d.getInstanceId().replace("(自建kafka)", "")
						.replace("rt-bigdata-kafka-dwd-v2", "rt-kafka-bigdata-dwd-v2")
						.replace("ods-sensor-log-kafka-v2","ods-sensors-log-kafka-v2");
				d.setInstanceId(replace);
				map.merge(d.getInstanceId(), d.getAmount(), Float::sum);
			}
		}
		map.forEach((k,v)->{
			log.info("date:{},instance:{},cost:{}",date,k,v);
		});
		return map;
	}

	public void sendKafkaBusinessCost2Kafka(String date,Map<String,Float> result) {
		Properties props = new Properties();
		props.put("bootstrap.servers", bootstrapServer);
		props.put("acks", "all");
		props.put("retries", 3);
		props.put("max.in.flight.requests.per.connection", 1);
		props.put("batch.size", 16384);
		props.put("linger.ms", 1);
		props.put("buffer.memory", 33554432);
		props.put("key.serializer", "org.apache.kafka.common.serialization.StringSerializer");
		props.put("value.serializer", "org.apache.kafka.common.serialization.StringSerializer");
		Producer<String, String> producer = new KafkaProducer<>(props);

		for (Map.Entry<String, Float> entry : result.entrySet()) {
			JSONObject jsonObject = new JSONObject();
			JSONObject data = new JSONObject();
			data.put("businessLine",entry.getKey());
			data.put("realCost",entry.getValue());
			jsonObject.put("date",date);
			jsonObject.put("processTime",new Date().getTime());
			jsonObject.put("type","kafkaBusiness");
			jsonObject.put("data",data);
			producer.send(new ProducerRecord<>(topic, entry.getKey(), jsonObject.toJSONString()), (metadata, exception) -> {
				if (exception == null) {
//					log.info(" success-> offset:" + metadata.offset() + "  partiton:" + metadata.partition());
				} else {
					exception.printStackTrace();
				}
			});
		}


		producer.close();
	}

	public void sendLinkedJob2Kafka(String date, Map<LinkedJob, JobShareMetrics>  result,String type) throws Exception{
		Map<Tuple2<String, String>, Tuple2<List<String>, String>> jobScene = buildExistScene();

		Properties props = new Properties();
		props.put("bootstrap.servers", bootstrapServer);
		props.put("acks", "all");
		props.put("retries", 3);
		props.put("max.in.flight.requests.per.connection", 1);
		props.put("batch.size", 16384);
		props.put("linger.ms", 1);
		props.put("buffer.memory", 33554432);
		props.put("key.serializer", "org.apache.kafka.common.serialization.StringSerializer");
		props.put("value.serializer", "org.apache.kafka.common.serialization.StringSerializer");
		Producer<String, String> producer = new KafkaProducer<>(props);
		for (Map.Entry<LinkedJob, JobShareMetrics> entry : result.entrySet()) {
			CostJob costJob = new CostJob();
			JSONObject jsonObject = new JSONObject();

			Tuple2<List<String>, String> jobSceneInfo = jobScene.get(Tuple2.of(entry.getKey().getJob().getProjectName(), entry.getKey().getJob().getTaskName()));
			if (jobSceneInfo == null) {
				List<String> empty =  new ArrayList<>();
				empty.add("待打标");
				costJob.setBusinessLine(empty);
				costJob.setScene("待打标");
			} else {
				costJob.setBusinessLine(jobSceneInfo.f0);
				costJob.setScene(jobSceneInfo.f1);
			}
			costJob.setLinkedJob(entry.getKey());
			costJob.setRealCost(entry.getValue().getSelfCost());
			costJob.setCpuLimit(entry.getValue().getCpuLimit());
			costJob.setCpuUsage(entry.getValue().getCpuUsage());

			jsonObject.put("date",date);
			jsonObject.put("processTime",new Date().getTime());
			jsonObject.put("data",JSONObject.toJSON(costJob));
			jsonObject.put("type",type);


			producer.send(new ProducerRecord<>(
					topic,
					Tuple2.of(entry.getKey().getJob().getProjectName(), entry.getKey().getJob().getTaskName()).toString(),
					jsonObject.toJSONString()), (metadata, exception) -> {
				if (exception == null) {
//					log.info(" success-> offset:" + metadata.offset() + "  partiton:" + metadata.partition());
				} else {
					exception.printStackTrace();
				}
			});
		}
		producer.close();
	}


	public void sendCostTable2Kafka(String date,Set<CostTable>  result) throws Exception{
		Map<Tuple2<String, String>, Tuple2<List<String>, String>> jobScene = buildExistScene();

		Properties props = new Properties();
		props.put("bootstrap.servers", bootstrapServer);
		props.put("acks", "all");
		props.put("retries", 3);
		props.put("max.in.flight.requests.per.connection", 1);
		props.put("batch.size", 16384);
		props.put("linger.ms", 1);
		props.put("buffer.memory", 33554432);
		props.put("key.serializer", "org.apache.kafka.common.serialization.StringSerializer");
		props.put("value.serializer", "org.apache.kafka.common.serialization.StringSerializer");
		Producer<String, String> producer = new KafkaProducer<>(props);
		for (CostTable costTable : result) {
			JSONObject jsonObject = new JSONObject();

			jsonObject.put("date",date);
			jsonObject.put("processTime",new Date().getTime());
			jsonObject.put("data",JSONObject.toJSON(costTable));
			jsonObject.put("type",CostTable.class.getSimpleName());


			producer.send(new ProducerRecord<>(
					topic,
					Tuple3.of(costTable.getInstanceName(), costTable.getDatabase(),costTable.getTableName()).toString(),
					jsonObject.toJSONString()), (metadata, exception) -> {
				if (exception == null) {
//					log.info(" success-> offset:" + metadata.offset() + "  partiton:" + metadata.partition());
				} else {
					exception.printStackTrace();
				}
			});
		}
		producer.close();
	}


	public void sendCpuResult2Kafka(Map<JSONObject, JobMetrics> specificCpuMetricsByPodPoolCluster, String date, String type) {
		Properties props = new Properties();
		props.put("bootstrap.servers", bootstrapServer);
		props.put("acks", "all");
		props.put("retries", 3);
		props.put("max.in.flight.requests.per.connection", 1);
		props.put("batch.size", 16384);
		props.put("linger.ms", 1);
		props.put("buffer.memory", 33554432);
		props.put("key.serializer", "org.apache.kafka.common.serialization.StringSerializer");
		props.put("value.serializer", "org.apache.kafka.common.serialization.StringSerializer");
		Producer<String, String> producer = new KafkaProducer<>(props);
		JSONObject data = new JSONObject();
		data.put("date", date);
		data.put("processTime", new Date().getTime());
		data.put("type", type);
		specificCpuMetricsByPodPoolCluster.forEach((key, value) -> {
			key.put("cpu_usage", value.getCpuUsage());
			key.put("cpu_request", value.getCpuRequest());
			key.put("memory_request", value.getMemoryRequest());
			data.put("data", JSONObject.toJSON(key));
			producer.send(new ProducerRecord<>(
					metricTopic, key.toString(), data.toJSONString()), (metadata, exception) -> {
				if (exception == null) {
//					log.info(" success-> offset:" + metadata.offset() + "  partiton:" + metadata.partition());
				} else {
					exception.printStackTrace();
				}
			});
		});
		producer.close();
	}

	public static List<FlinkSqlLineageDto>  getFlinkSqlLineage(String token) throws Exception{
		String url = "https://stream-rpp.shizhuang-inc.com/api/flink/sql/lineage";
		ObjectMapper objectMapper = new ObjectMapper();
		        // 设置日期格式
        DateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        objectMapper.setDateFormat(df);
		Map<String, String> map = new HashMap<>();
		map.put("Authorization", token);
		Map<String, String> paramMap = new HashMap<>();

		int total = Integer.MAX_VALUE;
		List<FlinkSqlLineageDto> flinkSqlLineages = new ArrayList<>();

		for (int i=0;(total-2000*i)>0;i++){
			paramMap.put("page",String.valueOf(i));
			paramMap.put("size","2000");
			HttpClientUtil.HttpClientResult httpClientResult = HttpClientUtil.doGet(url, map, paramMap);
			String content = httpClientResult.getContent();
			JSONObject data = JSONObject.parseObject(content);
			String dataContent = data.getString("content");
			total = data.getInteger("totalElements");
			flinkSqlLineages.addAll(objectMapper.readValue(dataContent, new TypeReference<List<FlinkSqlLineageDto>>() {
			}));
		}

		return flinkSqlLineages;
	}

	public static void download(List<Tuple2<Table,Job>> data,List<String> schemas) throws Exception {
		Workbook workbook = new XSSFWorkbook();
		Sheet sheet = workbook.createSheet("Sheet1");
		int headRowNum = 0;
		// 创建表头
		Row headerRow = sheet.createRow(0);
		for (String string : schemas) {
			Cell cell = headerRow.createCell(headRowNum);
			cell.setCellValue(string);
			headRowNum++;
		}

		int rowNum = 1;
		for (Tuple2<Table, Job> datum : data) {
			Row row = sheet.createRow(rowNum++);
			for (int i = 0; i < schemas.size(); i++) {
				Cell cell = row.createCell(i);
				String value;
				if (i < 2) {
					value = i==0?datum.f0.getDatabase():datum.f0.getTableName();
				} else {
					value = i==2?datum.f1.getProjectName():datum.f1.getTaskName();
				}
				cell.setCellValue(value);
			}

		}

				// 将工作簿写入 Excel 文件
		try (FileOutputStream fileOut = new FileOutputStream("output.xlsx")) {
			workbook.write(fileOut);
		} catch (IOException e) {
			e.printStackTrace();
		}
	}

	public static void getLeafNode() throws Exception{
		List<FlinkSqlLineageDto> flinkSqlLineage = getFlinkSqlLineage("");
		long start = LocalDate.parse("2024-06-19", dayFormat).atStartOfDay().toInstant(ZoneOffset.UTC).getEpochSecond()-8*3600;
		long end = start+3600*24-1;
		long s = System.currentTimeMillis();
		Tuple2<Map<Tuple2<String, String>, Double>, Map<Tuple2<String, String>, Double>> metricsBefore1day = getCpuMetricsByJob(start, end);
		Graph graph = new Graph(metricsBefore1day.f0,metricsBefore1day.f1);
		graph.buildTableGraph(flinkSqlLineage);

		Set<Job> tailJob = graph.getTailJob();
		HashSet<Table> sinks = new HashSet<>();
		for (Job job : tailJob) {
			Set<Table> sinkTables = graph.jobWithAllSinkTableMapping.getOrDefault(job, new HashSet<>());
			sinks.addAll(sinkTables);
		}

		Map<String, List<Table>> collect = sinks.stream().collect(Collectors.groupingBy(t -> t.getDbType()));
		List<Table> odpsTables = collect.get("odps");
		List<Tuple2<Table,Job>> mixData = new ArrayList<>();
		for (Table odpsTable : odpsTables) {
			Map<Table, Set<Job>> tableAsSinkOfJobMapping = graph.tableAsSinkOfJobMapping;
			Set<Job> jobs = tableAsSinkOfJobMapping.get(odpsTable);
			for (Job job : jobs) {
				mixData.add(Tuple2.of(odpsTable,job));
			}

		}

		download(mixData,Lists.newArrayList("database", "tableName", "projectName", "taskName"));
	}


	public static void main(String[] args) throws Exception {
		String currentDay = LocalDate.now().plusDays(-1).format(dayFormat);
		long start = LocalDate.parse(currentDay, dayFormat).atStartOfDay().toInstant(ZoneOffset.UTC).getEpochSecond() - 8 * 3600;
		long end = start + 3600 * 24 - 1;

		Map<JSONObject, JobMetrics> averageSpecificCpuMetricsByPodPoolCluster = getAverageSpecificCpuMetricsByPodPoolCluster(start, end);


		JSONArray objects = executeQuery(max_cpu_usage, start, end, 600);
		JSONArray maxCpuUsage = objects.getJSONObject(0).getJSONArray("values").stream().map(each -> ((JSONArray) each)).max((o1, o2) -> {
			double diff = Double.parseDouble(o1.getString(1)) - Double.parseDouble(o2.getString(1));
			if (diff > 0) {
				return 1;
			} else if (diff < 0) {
				return -1;
			} else {
				return o1.getInteger(0) - o2.getInteger(0);
			}
		}).get();
		Integer maxCpuUsageTimestamp = maxCpuUsage.getInteger(0);

		List<String> nodeList = new ArrayList<>();
		JSONArray nodeArray = executeQuery(String.format(taskManager_cpu_usage_by_node,"k8s-prd-flink-hz-2","k8s-prd-flink-hz-2","bigdata-tech-data-dw2-amd"), maxCpuUsageTimestamp);
		for (int i = 0; i< nodeArray.size(); i++){
			JSONArray values = nodeArray.getJSONObject(i).getJSONArray("value");
			JSONObject metric = nodeArray.getJSONObject(i).getJSONObject("metric");
			String node = metric.getString("node");
			Double cpuUsage = values.getDouble(1);
				if (cpuUsage<9){
					nodeList.add(node);
				}
			}

		Map<String,Tuple2<String,String>> totalInstanceJobMap = new HashMap<>();
		for (String taskProject : specialCpuProjectSet) {
			String format = String.format(project_jobManager_job_up_time, taskProject);
			log.info("execute pull metric:{}, project:{},startTime:{}", "jobManager_cpu_usage", taskProject, start);
			JSONArray result = executeQuery(format, start, end, 1200);
			// job pod mapping
			Map<String,Tuple2<String,String>> instanceJobMap = result.stream().
					map(o -> ((JSONObject) o).getJSONObject("metric")).collect(Collectors.toMap(
							e -> e.getString("pod").split("-")[1],
					e -> Tuple2.of(taskProject,e.getString("job_name")),
					(existing, replacement) -> existing
			));
			totalInstanceJobMap.putAll(instanceJobMap);
		}

			JSONArray cpuUsageResult = executeQuery(String.format(taskManager_cpu_usage_by_pod_pool_cluster_node, "k8s-prd-flink-hz-2", "k8s-prd-flink-hz-2", "bigdata-tech-data-dw2-amd"), maxCpuUsageTimestamp);
			JSONArray cpuRequestResult = executeQuery(String.format(taskManager_cpu_request_by_pod_pool_cluster_node, "k8s-prd-flink-hz-2", "k8s-prd-flink-hz-2", "bigdata-tech-data-dw2-amd"), maxCpuUsageTimestamp);
			JSONArray memoryRequestResult = executeQuery(String.format(taskManager_memory_request_by_pod_pool_cluster_node, "k8s-prd-flink-hz-2", "k8s-prd-flink-hz-2", "bigdata-tech-data-dw2-amd"), maxCpuUsageTimestamp);
			Map<JSONObject, JobMetrics> cpuUsageAndRequestMap = cpuUsageResult.stream().peek(e -> {
				JSONObject metric = ((JSONObject) e).getJSONObject("metric");
				String pod = metric.getString("pod");
				String instance = pod.split("-")[1];
				Tuple2<String, String> jobNameAndProject = totalInstanceJobMap.get(instance);
				if (jobNameAndProject == null){
					log.error("instance of project not found:{}",instance);
				}else {
					metric.put("task_project", jobNameAndProject.f0);
					metric.put("job_name", jobNameAndProject.f1);
				}
			}).collect(Collectors.toMap(
					e -> ((JSONObject) e).getJSONObject("metric"),
					e -> {
						Double cpuUsage = ((JSONObject) e).getJSONArray("value").getDouble(1);
						JobMetrics jobMetrics = new JobMetrics();
						jobMetrics.setCpuUsage(cpuUsage);
						return jobMetrics;
					}));

			cpuRequestResult.forEach(e -> {
				JSONObject metric = ((JSONObject) e).getJSONObject("metric");
				Double cpuRequest = ((JSONObject) e).getJSONArray("value").getDouble(1);
				String pod = metric.getString("pod");
				String instance = pod.split("-")[1];
				Tuple2<String, String> jobNameAndProject = totalInstanceJobMap.get(instance);
				if (jobNameAndProject == null){
					log.error("monitor is not running,{}",instance);
				}else {
					metric.put("task_project", jobNameAndProject.f0);
					metric.put("job_name", jobNameAndProject.f1);
				}
				JobMetrics exist = cpuUsageAndRequestMap.get(metric);
				if ( cpuUsageAndRequestMap.get(metric) != null){
					exist.setCpuRequest(cpuRequest); ;
					cpuUsageAndRequestMap.put(metric,exist);
				}else {
					log.error("not found cpu usage,{}，only has cpu request",metric);
				}
			});

			memoryRequestResult.forEach(e -> {
				JSONObject metric = ((JSONObject) e).getJSONObject("metric");
				Double memoryRequest = ((JSONObject) e).getJSONArray("value").getDouble(1);
				String pod = metric.getString("pod");
				String instance = pod.split("-")[1];
				Tuple2<String, String> jobNameAndProject = totalInstanceJobMap.get(instance);
				if (jobNameAndProject == null){
					log.error("monitor is not running,{}",instance);
				}else {
					metric.put("task_project", jobNameAndProject.f0);
					metric.put("job_name", jobNameAndProject.f1);
				}
				JobMetrics exist = cpuUsageAndRequestMap.get(metric);
				if ( cpuUsageAndRequestMap.get(metric) != null){
					exist.setMemoryRequest(memoryRequest);
					cpuUsageAndRequestMap.put(metric,exist);
				}else {
					log.error("not found cpu usage,{}，only has cpu request",metric);
				}
			});

		Map<Tuple2<String, String>, Double> cpuTotal = cpuUsageAndRequestMap.entrySet().stream().collect(Collectors.groupingBy(
				e -> Tuple2.of(
						e.getKey().getString("task_project"),
						e.getKey().getString("job_name")
				),
				Collectors.summingDouble(e -> e.getValue().getCpuUsage())
		));

		Map<Tuple2<String, String>, Double> cpuRequestTotal = cpuUsageAndRequestMap.entrySet().stream().collect(Collectors.groupingBy(
				e -> Tuple2.of(
						e.getKey().getString("task_project"),
						e.getKey().getString("job_name")
				),
				Collectors.summingDouble(e -> e.getValue().getCpuRequest())
		));

		Map<Tuple2<String, String>, Double> memoryRequestTotal = cpuUsageAndRequestMap.entrySet().stream().collect(Collectors.groupingBy(
				e -> Tuple2.of(
						e.getKey().getString("task_project"),
						e.getKey().getString("job_name")
				),
				Collectors.summingDouble(e -> e.getValue().getMemoryRequest())
		));


		Map<Tuple2<String, String>, Double> collect = cpuUsageAndRequestMap.entrySet().stream().filter(e -> {
			if (nodeList.contains(e.getKey().getString("node"))) {
				Tuple2<String, String> key = Tuple2.of(
						e.getKey().getString("task_project"),
						e.getKey().getString("job_name"));
				if (
						(
								(cpuTotal.get(key) / cpuRequestTotal.get(key)) < 0.2
										&& cpuRequestTotal.get(key) > 10)
//							|| ((memoryRequestTotal.get(key)/cpuRequestTotal.get(key))>4)
				) {
					return true;
				}
			}
			return false;
		}).collect(Collectors.toMap(v -> Tuple2.of(
				v.getKey().getString("task_project"),
				v.getKey().getString("job_name")),
				v -> v.getValue().getCpuUsage() / v.getValue().getCpuRequest(),
				(existing, replacement) -> existing
				)
		);

		collect.forEach((k, v) -> {
			System.out.println(k.f0 + "@"+k.f1+","+v);
		});


	}

	@Test
	public void testDownloadJar() throws Exception{
		File file = new File("upload-dynamic-compilation-2.61092167099153068953-20230516150912144.jar");
//    // 将字节流写入文件
//        try (FileOutputStream fos = new FileOutputStream(file)) {
//            fos.write(bytes);
//        } catch (IOException e) {
//            e.printStackTrace();
//        }
      String arg = "--execute.job.jdbc.url;***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************";
      PackagedProgram packagedProgram = PackagedProgram.newBuilder()
            .setJarFile(file)
            .setArguments(arg.split(";"))
            .setEntryPointClassName("com.dewu.flink.template.compilation.DynamicCompilationFlinkJob")
            .build();

      JobGraph jobGraph =
                            PackagedProgramUtils.createJobGraph(
                                    packagedProgram, new Configuration(), 1, false);
	}

	private static String initBusinessLine(String s){
		ArrayList<String> strings = Lists.newArrayList(Splitter.on(",").split(s).iterator());
		JSONArray array = new JSONArray();
		for (String e :strings){
			String businessLine = "";
			FlinkSqlLeafSceneAndBusinessLine flinkSqlLeafSceneAndBusinessLine = new FlinkSqlLeafSceneAndBusinessLine();
			flinkSqlLeafSceneAndBusinessLine.setJobName(e);
			flinkSqlLeafSceneAndBusinessLine.setProjectName("tech-data-dw2");
			if (e.contains("kefu")||e.contains("ads_center")||e.contains("voc")||e.contains("_cs_")||e.contains("_im_")||e.contains("_msd_")||e.contains("ticket")){
				businessLine = "客服";
			}else if(
				e.contains("ads_dealer")||e.contains("community")
			){
				businessLine = "社区";
			}else if(
				e.contains("increase")||e.contains("media")||e.contains("delivery")
			){
				businessLine = "增长";
			}else if(e.contains("crowd_label")){
				businessLine = "用户";
			}else if(e.contains("test")||e.contains("tmp")){
				businessLine = "数据平台";
			}else {
				System.out.println(e);
				businessLine = "交易";
			}

			flinkSqlLeafSceneAndBusinessLine.setBusinessLine(businessLine);
			flinkSqlLeafSceneAndBusinessLine.setScene("");
			array.add(JSONObject.toJSON(flinkSqlLeafSceneAndBusinessLine));
		}

		return array.toJSONString();
	}

	public static String init(String s){

		ArrayList<String> strings = Lists.newArrayList(Splitter.on(",").split(s).iterator());
		JSONArray array = new JSONArray();
		for (String e :strings){
			FlinkSqlLeafSceneAndBusinessLine flinkSqlLeafSceneAndBusinessLine = new FlinkSqlLeafSceneAndBusinessLine();
			flinkSqlLeafSceneAndBusinessLine.setJobName(e.trim());
			flinkSqlLeafSceneAndBusinessLine.setProjectName("tech-data-dw2");
			flinkSqlLeafSceneAndBusinessLine.setBusinessLine("交易,社区,用户,增长");
			flinkSqlLeafSceneAndBusinessLine.setScene("");
			array.add(JSONObject.toJSON(flinkSqlLeafSceneAndBusinessLine));
		}
		return array.toJSONString();
	}





}
